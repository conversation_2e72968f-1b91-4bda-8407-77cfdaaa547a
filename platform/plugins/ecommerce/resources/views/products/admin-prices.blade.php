@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="card">
        <div class="card-header">
            <h4>أسعار المنتجات للمشرفين</h4>
            <p class="text-muted">عرض سعر المفرد وسعر الجملة لجميع المنتجات</p>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th width="60">الصورة</th>
                            <th>المنتج</th>
                            <th>رمز المنتج</th>
                            <th width="130">سعر المفرد</th>
                            <th width="130">سعر الجملة</th>
                            <th width="100">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($products as $product)
                            <tr>
                                <td>
                                    <img
                                        src="{{ RvMedia::getImageUrl($product->image, 'thumb', false, RvMedia::getDefaultImage()) }}"
                                        alt="{{ $product->name }}"
                                        width="50"
                                    >
                                </td>
                                <td>
                                    <a href="{{ route('products.edit', $product->id) }}" target="_blank">
                                        {{ $product->name }}
                                    </a>
                                </td>
                                <td>{{ $product->sku }}</td>
                                <td>
                                    <div class="price-field" data-type="price" data-id="{{ $product->id }}">
                                        <span class="price">{{ format_price($product->price) }}</span>
                                        <input
                                            type="number"
                                            class="form-control d-none price-input"
                                            value="{{ $product->price }}"
                                            step="0.01"
                                            min="0"
                                        >
                                    </div>
                                </td>
                                <td>
                                    <div class="price-field" data-type="wholesale_price" data-id="{{ $product->id }}">
                                        <span class="price">{{ format_price($product->wholesale_price) }}</span>
                                        <input
                                            type="number"
                                            class="form-control d-none price-input"
                                            value="{{ $product->wholesale_price }}"
                                            step="0.01"
                                            min="0"
                                        >
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ route('products.edit', $product->id) }}" class="btn btn-sm btn-icon btn-primary" title="تعديل المنتج">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            {!! $products->links() !!}
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تفعيل تعديل الأسعار عند النقر
            document.querySelectorAll('.price-field').forEach(field => {
                const priceSpan = field.querySelector('.price');
                const priceInput = field.querySelector('.price-input');

                // تفعيل التعديل عند النقر على السعر
                priceSpan.addEventListener('click', function() {
                    priceSpan.classList.add('d-none');
                    priceInput.classList.remove('d-none');
                    priceInput.focus();
                });

                // حفظ التغييرات عند الخروج من الحقل
                priceInput.addEventListener('blur', function() {
                    priceSpan.classList.remove('d-none');
                    priceInput.classList.add('d-none');

                    if (priceInput.value !== priceInput.defaultValue) {
                        savePrice(field.dataset.id, field.dataset.type, priceInput.value);
                    }
                });

                // حفظ عند الضغط على Enter
                priceInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        priceInput.blur();
                    }
                });
            });

            // دالة حفظ السعر
            function savePrice(productId, column, value) {
                const formData = new FormData();
                formData.append('_token', '{{ csrf_token() }}');
                formData.append('column', column);
                formData.append('value', value);

                fetch('{{ route('ecommerce.product-prices.update', ['product' => 'PRODUCT_ID']) }}'.replace('PRODUCT_ID', productId), {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        Botble.showError(data.message);
                    } else {
                        Botble.showSuccess(data.message);
                        const field = document.querySelector(`.price-field[data-type="${column}"][data-id="${productId}"]`);
                        field.querySelector('.price').textContent = column === 'price' ?
                            '{{ get_application_currency()->symbol }}' + parseFloat(value).toFixed(2) :
                            '{{ get_application_currency()->symbol }}' + parseFloat(value).toFixed(2);
                    }
                })
                .catch(error => {
                    Botble.showError('حدث خطأ أثناء الحفظ');
                    console.error(error);
                });
            }
        });
    </script>
@endsection
