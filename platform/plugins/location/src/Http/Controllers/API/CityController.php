<?php

namespace Botble\Location\Http\Controllers\API;

use Botble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Location\Http\Resources\CityResource;
use Botble\Location\Models\City;
use Illuminate\Http\Request;

class CityController extends BaseController
{
    /**
     * قائمة المدن
     *
     * @group Location
     */
    public function index(Request $request)
    {
        $query = City::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with(['country', 'state']);

        // فلترة حسب الدولة
        if ($request->has('country_id') && $request->input('country_id')) {
            $query->where('country_id', $request->input('country_id'));
        }

        // فلترة حسب الولاية
        if ($request->has('state_id') && $request->input('state_id')) {
            $query->where('state_id', $request->input('state_id'));
        }

        // البحث في اسم المدينة
        if ($request->has('search') && $request->input('search')) {
            $search = $request->input('search');
            $query->where('name', 'LIKE', "%{$search}%");
        }

        // ترتيب النتائج
        $sortBy = $request->input('sort_by', 'order');
        $sortOrder = $request->input('sort_order', 'asc');
        
        if (in_array($sortBy, ['order', 'name'])) {
            $query->orderBy($sortBy, $sortOrder === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy('order', 'asc')->orderBy('name', 'asc');
        }

        $perPage = $request->integer('per_page', 50);
        $perPage = min($perPage, 100);

        $cities = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData(CityResource::collection($cities))
            ->toApiResponse();
    }

    /**
     * عرض مدينة محددة
     *
     * @group Location
     */
    public function show($id)
    {
        $city = City::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with(['country', 'state'])
            ->findOrFail($id);

        return $this
            ->httpResponse()
            ->setData(new CityResource($city))
            ->toApiResponse();
    }

    /**
     * البحث في المدن
     *
     * @group Location
     */
    public function search(Request $request)
    {
        $request->validate([
            'q' => 'required|string|min:2|max:255',
            'country_id' => 'nullable|exists:countries,id',
            'state_id' => 'nullable|exists:states,id',
            'per_page' => 'nullable|integer|min:1|max:100'
        ]);

        $query = City::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with(['country', 'state']);

        $searchTerm = $request->input('q');
        
        // البحث في اسم المدينة
        $query->where('name', 'LIKE', "%{$searchTerm}%");

        // فلترة حسب الدولة إذا تم تحديدها
        if ($request->has('country_id') && $request->input('country_id')) {
            $query->where('country_id', $request->input('country_id'));
        }

        // فلترة حسب الولاية إذا تم تحديدها
        if ($request->has('state_id') && $request->input('state_id')) {
            $query->where('state_id', $request->input('state_id'));
        }

        // ترتيب النتائج حسب الصلة
        $query->orderByRaw("CASE WHEN name LIKE '{$searchTerm}%' THEN 1 ELSE 2 END")
              ->orderBy('order', 'asc')
              ->orderBy('name', 'asc');

        $perPage = $request->integer('per_page', 20);
        $cities = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData([
                'search_term' => $searchTerm,
                'total_results' => $cities->total(),
                'cities' => CityResource::collection($cities)
            ])
            ->toApiResponse();
    }
}
