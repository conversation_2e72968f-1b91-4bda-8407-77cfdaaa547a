<?php

namespace <PERSON><PERSON><PERSON>\Ecommerce\Providers;

use <PERSON><PERSON>ble\Ecommerce\Commands\CancelExpiredDeletionRequests;
use Bo<PERSON>ble\Ecommerce\Commands\ImportProductsFromCsvCommand;
use Botble\Ecommerce\Commands\SendAbandonedCartsEmailCommand;
use Bo<PERSON>ble\Ecommerce\Console\DebugShortcodeCommand;
use Botble\Ecommerce\Models\SharedWishlist;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\ServiceProvider;

class CommandServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (! $this->app->runningInConsole()) {
            return;
        }

        $this->commands([
            SendAbandonedCartsEmailCommand::class,
            CancelExpiredDeletionRequests::class,
            DebugShortcodeCommand::class,
            ImportProductsFromCsvCommand::class,
        ]);

        $this->app->afterResolving(Schedule::class, function (Schedule $schedule): void {
            $schedule->command(SendAbandonedCartsEmailCommand::class)->weekly();
            $schedule->command(CancelExpiredDeletionRequests::class)->daily();
            $schedule->command('model:prune', [
                '--model' => [SharedWishlist::class],
            ])->daily();
        });
    }
}
