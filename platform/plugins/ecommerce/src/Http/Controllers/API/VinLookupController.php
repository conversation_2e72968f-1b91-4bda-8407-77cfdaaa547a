<?php

namespace Botble\Ecommerce\Http\Controllers\API;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Ecommerce\Models\CarMake;
use Botble\Ecommerce\Models\CarModel;
use Botble\Ecommerce\Models\CarYear;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class VinLookupController extends BaseController
{
    public function lookup(Request $request)
    {
        $vin = $request->input('vin');

        if (! $vin) {
            return response()->json([
                'success' => false,
                'message' => 'VIN is required',
            ], 400);
        }

        try {
            // هذا مجرد مثال - في الواقع ستحتاج إلى الاتصال بخدمة API للحصول على بيانات VIN
            // $response = Http::get('https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVinValues/' . $vin . '?format=json');
            // $data = $response->json();

            // لأغراض العرض، سنستخدم بيانات وهمية بدلاً من API حقيقي
            // في نظام حقيقي، استبدل هذا بطلب API فعلي
            
            // نقوم بفحص الحد الأدنى من بيانات VIN
            if (strlen($vin) < 10) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid VIN format. VIN should be at least 10 characters',
                ], 400);
            }

            // استخراج رمز الشركة المصنعة من VIN (المثال هنا مبسط)
            $makeCode = substr($vin, 0, 3);
            
            // بحث وهمي عن الشركة المصنعة (في نظام حقيقي، سيتم استبدال هذا ببيانات من API)
            $make = null;
            if ($makeCode == 'JTD') {
                $make = CarMake::where('name', 'LIKE', '%Toyota%')->first();
            } elseif ($makeCode == 'WVW') {
                $make = CarMake::where('name', 'LIKE', '%Volkswagen%')->first();
            } elseif ($makeCode == '1N4') {
                $make = CarMake::where('name', 'LIKE', '%Nissan%')->first();
            } else {
                // افتراضي - استرجاع أول شركة مصنعة للعرض فقط
                $make = CarMake::first();
            }
            
            if (! $make) {
                return response()->json([
                    'success' => false,
                    'message' => 'Could not identify car make from VIN',
                ], 404);
            }
            
            // الحصول على أول موديل للشركة المصنعة
            $model = CarModel::where('make_id', $make->id)->first();
            
            if (! $model) {
                return response()->json([
                    'success' => false,
                    'message' => 'No models found for identified make',
                ], 404);
            }
            
            // استخراج سنة الصنع من رقم VIN (المثال هنا مبسط)
            $yearCode = substr($vin, 9, 1);
            
            // تحويل رمز السنة إلى سنة حقيقية (في نظام حقيقي، سيكون هذا أكثر تعقيدًا)
            $yearMapping = [
                'A' => 2010, 'B' => 2011, 'C' => 2012, 'D' => 2013, 'E' => 2014,
                'F' => 2015, 'G' => 2016, 'H' => 2017, 'J' => 2018, 'K' => 2019,
                'L' => 2020, 'M' => 2021, 'N' => 2022, 'P' => 2023,
            ];
            
            $manufacturingYear = $yearMapping[$yearCode] ?? 2020;
            
            // الحصول على سنة الصنع المطابقة
            $year = CarYear::where('model_id', $model->id)
                ->where('year', $manufacturingYear)
                ->first();
            
            if (! $year) {
                // إذا لم يتم العثور على السنة المحددة، استخدم أول سنة متاحة
                $year = CarYear::where('model_id', $model->id)->first();
            }
            
            return response()->json([
                'success' => true,
                'vin' => $vin,
                'car_make_id' => $make->id,
                'car_make_name' => $make->name,
                'car_model_id' => $model->id,
                'car_model_name' => $model->name,
                'car_year_id' => $year ? $year->id : null,
                'car_year' => $year ? $year->year : null,
            ]);
        } catch (\Exception $e) {
            Log::error('VIN lookup error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Error during VIN lookup: ' . $e->getMessage(),
            ], 500);
        }
    }
} 