<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Controllers\Customers;

use Bo<PERSON>ble\ACL\Traits\RegistersUsers;
use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Ecommerce\Events\CustomerEmailVerified;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Botble\Ecommerce\Forms\Fronts\Auth\RegisterForm;
use Bo<PERSON>ble\Ecommerce\Http\Requests\RegisterRequest;
use Botble\Ecommerce\Models\Address;
use Botble\Ecommerce\Models\Customer;
use Botble\JsValidation\Facades\JsValidator;
use Botble\SeoHelper\Facades\SeoHelper;
use Botble\Theme\Facades\Theme;
use Carbon\Carbon;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\URL;
use Bo<PERSON>ble\Base\Facades\EmailHandler;
use App\Services\OtpService;

class RegisterController extends BaseController
{
    use RegistersUsers;

    protected OtpService $otpService;
    protected string $redirectTo = '/';

    public function __construct(OtpService $otpService)
    {
        $this->otpService = $otpService;
        $this->middleware('customer.guest');
    }

    public function showRegistrationForm()
    {
        if (! EcommerceHelper::isCustomerRegistrationEnabled()) {
            abort(404);
        }

        SeoHelper::setTitle(__('Register'));

        Theme::breadcrumb()->add(__('Register'), route('customer.register'));

        if (! session()->has('url.intended') &&
            ! in_array(url()->previous(), [route('customer.login'), route('customer.register')])
        ) {
            session(['url.intended' => url()->previous()]);
        }

        Theme::asset()
            ->container('footer')
            ->usePath(false)
            ->add('js-validation', 'vendor/core/core/js-validation/js/js-validation.js', ['jquery'], version: '1.0.1');

        add_filter(THEME_FRONT_FOOTER, function ($html) {
            return $html . JsValidator::formRequest(RegisterRequest::class)->render();
        });

        return Theme::scope(
            'ecommerce.customers.register',
            ['form' => RegisterForm::create()],
            'plugins/ecommerce::themes.customers.register'
        )->render();
    }

    public function register(RegisterRequest $request)
    {
        if (! EcommerceHelper::isCustomerRegistrationEnabled()) {
            abort(404);
        }

        do_action('customer_register_validation', $request);

        /**
         * @var Customer $customer
         */
        $customer = $this->create($request->input());

        event(new Registered($customer));

        // حفظ العنوان إذا كان عميل جملة
        if ($request->input('request_wholesale') && $request->filled('address')) {
            Address::query()->create([
                'name' => $customer->name,
                'phone' => $customer->phone,
                'email' => $customer->email,
                'address' => BaseHelper::clean($request->input('address')),
                'city' => BaseHelper::clean($request->input('city')),
                'state' => BaseHelper::clean($request->input('state')),
                'zip_code' => BaseHelper::clean($request->input('zip_code')),
                'customer_id' => $customer->id,
                'is_default' => true,
            ]);
        }

        // إرسال إشعار للمسؤول عن طلب عميل جديد
        if (setting('admin_email')) {
            $emailAddress = setting('admin_email');
            $needsApproval = $request->input('request_wholesale') || !EcommerceHelper::isEnableEmailVerification();

            if ($needsApproval) {
                $emailSubject = 'تسجيل عميل جديد يحتاج للموافقة';
                $emailContent = 'تم تسجيل عميل جديد وينتظر الموافقة:' . PHP_EOL . PHP_EOL .
                                'الاسم: ' . $customer->name . PHP_EOL .
                                'البريد الإلكتروني: ' . $customer->email . PHP_EOL .
                                'رقم الهاتف: ' . $customer->phone;

                // إضافة معلومة إذا كان طلب عميل جملة
                if ($request->input('request_wholesale')) {
                    $emailContent .= PHP_EOL . PHP_EOL . 'ملاحظة: هذا العميل طلب تسجيله كعميل جملة.';

                    // إضافة معلومات العنوان إذا كانت متوفرة
                    if ($request->filled('address')) {
                        $emailContent .= PHP_EOL . 'العنوان: ' . $request->input('address');
                        $emailContent .= PHP_EOL . 'المدينة: ' . $request->input('city');
                        $emailContent .= PHP_EOL . 'المنطقة: ' . $request->input('state');
                        if ($request->filled('zip_code')) {
                            $emailContent .= PHP_EOL . 'الرمز البريدي: ' . $request->input('zip_code');
                        }
                    }
                }

                EmailHandler::send($emailContent, $emailSubject, $emailAddress);
            }
        }

        // التحقق من إعدادات OTP
        if (config('otp.enabled', true) && $customer->phone) {
            // إرسال OTP للتأكيد
            $otpResult = $this->otpService->generateAndSendOtp(
                $customer->phone,
                'registration',
                $customer->id,
                $customer->email
            );

            if ($otpResult['success']) {
                $message = __('تم إرسال رمز التأكيد إلى رقم هاتفك. يرجى إدخال الرمز لتأكيد حسابك.');

                // إضافة رسالة إذا كان العميل طلب أن يكون عميل جملة
                if ($request->input('request_wholesale')) {
                    $message .= ' ' . __('تم استلام طلبك للتسجيل كعميل جملة، وسيتم عرض أسعار الجملة بعد الموافقة على طلبك من قبل الإدارة.');
                }

                return $this
                    ->httpResponse()
                    ->setNextUrl(route('customer.otp.verify', [
                        'phone' => $customer->phone,
                        'type' => 'registration',
                        'email' => $customer->email
                    ]))
                    ->setMessage($message);
            }
        }

        if (EcommerceHelper::isEnableEmailVerification()) {
            $this->registered($request, $customer);

            $message = __('We have sent you an email to verify your email. Please check and confirm your email address!');

            // إضافة رسالة إذا كان العميل طلب أن يكون عميل جملة
            if ($request->input('request_wholesale')) {
                $message .= ' ' . __('تم استلام طلبك للتسجيل كعميل جملة، وسيتم عرض أسعار الجملة بعد الموافقة على طلبك من قبل الإدارة.');
            }

            return $this
                ->httpResponse()
                ->setNextUrl(route('customer.login'))
                ->with(['auth_warning_message' => $message])
                ->setMessage($message);
        }

        $customer->confirmed_at = Carbon::now();
        $customer->save();

        $this->guard()->login($customer);

        $message = __('Registered successfully!');

        // إضافة رسالة إذا كان العميل طلب أن يكون عميل جملة
        if ($request->input('request_wholesale')) {
            $message .= ' ' . __('تم استلام طلبك للتسجيل كعميل جملة، وسيتم عرض أسعار الجملة بعد الموافقة على طلبك من قبل الإدارة.');
        }

        return $this
            ->httpResponse()
            ->setNextUrl($this->redirectPath())
            ->setMessage($message);
    }

    protected function create(array $data)
    {
        return Customer::query()->create([
            'name' => BaseHelper::clean($data['name']),
            'email' => BaseHelper::clean($data['email'] ?? null),
            'phone' => BaseHelper::clean($data['phone'] ?? null),
            'password' => Hash::make($data['password']),
            'is_approved' => false,
            'is_wholesale' => isset($data['request_wholesale']) && $data['request_wholesale'] ? false : null,
        ]);
    }

    protected function guard()
    {
        return auth('customer');
    }

    public function confirm(int|string $id, Request $request)
    {
        abort_unless(URL::hasValidSignature($request), 404);

        /**
         * @var Customer $customer
         */
        $customer = Customer::query()->findOrFail($id);

        $customer->confirmed_at = Carbon::now();
        $customer->save();

        $this->guard()->login($customer);

        CustomerEmailVerified::dispatch($customer);

        return $this
            ->httpResponse()
            ->setNextUrl(route('customer.overview'))
            ->setMessage(__('You successfully confirmed your email address.'));
    }

    public function resendConfirmation(Request $request)
    {
        /**
         * @var Customer $customer
         */
        $customer = Customer::query()->where('email', $request->input('email'))->first();

        if (! $customer) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__('Cannot find this customer!'));
        }

        $customer->sendEmailVerificationNotification();

        return $this
            ->httpResponse()
            ->setMessage(__('We sent you another confirmation email. You should receive it shortly.'));
    }
}
