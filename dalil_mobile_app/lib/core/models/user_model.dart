class UserModel {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final String? address;
  final String userType;
  final bool isWholesale;
  final bool emailVerified;
  final String? avatar;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.address,
    required this.userType,
    this.isWholesale = false,
    this.emailVerified = false,
    this.avatar,
    this.createdAt,
    this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as int,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String?,
      address: json['address'] as String?,
      userType: json['user_type'] as String? ?? 'customer',
      isWholesale: json['is_wholesale'] as bool? ?? false,
      emailVerified: json['email_verified'] as bool? ?? false,
      avatar: json['avatar'] as String?,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'user_type': userType,
      'is_wholesale': isWholesale,
      'email_verified': emailVerified,
      'avatar': avatar,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  UserModel copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? userType,
    bool? isWholesale,
    bool? emailVerified,
    String? avatar,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      userType: userType ?? this.userType,
      isWholesale: isWholesale ?? this.isWholesale,
      emailVerified: emailVerified ?? this.emailVerified,
      avatar: avatar ?? this.avatar,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // الحصول على الاسم المختصر
  String get initials {
    final names = name.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names[0][0].toUpperCase();
    }
    return 'U';
  }

  // الحصول على نوع المستخدم بالعربية
  String get userTypeArabic {
    switch (userType) {
      case 'customer':
        return 'عميل';
      case 'wholesale':
        return 'تاجر جملة';
      case 'admin':
        return 'مدير';
      default:
        return 'مستخدم';
    }
  }

  // التحقق من صحة البيانات
  bool get isValid {
    return name.isNotEmpty && email.isNotEmpty;
  }

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, userType: $userType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
