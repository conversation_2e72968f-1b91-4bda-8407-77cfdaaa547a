import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../features/home/<USER>/product_model.dart';
import '../providers/favorites_provider.dart';
import '../providers/cart_provider.dart';
import '../providers/theme_provider.dart';
import 'loading_widget.dart';

class ProductCard extends StatelessWidget {
  final ProductModel product;
  final VoidCallback? onTap;
  final bool showFavoriteButton;
  final bool showAddToCartButton;
  final bool isCompact;

  const ProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.showFavoriteButton = true,
    this.showAddToCartButton = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer3<FavoritesProvider, CartProvider, ThemeProvider>(
      builder: (context, favoritesProvider, cartProvider, themeProvider, child) {
        final isDark = themeProvider.isDarkMode;
        final isFavorite = favoritesProvider.isFavorite(product.id);
        final isInCart = cartProvider.isInCart(product.id);
        
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: isDark ? Colors.grey[850] : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(12),
              child: isCompact ? _buildCompactLayout(isDark, isFavorite, isInCart) : _buildFullLayout(isDark, isFavorite, isInCart),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFullLayout(bool isDark, bool isFavorite, bool isInCart) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // صورة المنتج مع الأزرار
        _buildProductImage(isDark, isFavorite, isInCart),
        
        // معلومات المنتج
        Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // اسم المنتج
              Text(
                product.name,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 4),
              
              // وصف مختصر
              if (product.description?.isNotEmpty == true)
                Text(
                  product.description!,
                  style: TextStyle(
                    fontSize: 12,
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              
              const SizedBox(height: 8),
              
              // التقييم والمراجعات
              _buildRatingRow(isDark),
              
              const SizedBox(height: 8),
              
              // السعر والحالة
              _buildPriceAndStatus(isDark),
              
              if (showAddToCartButton) ...[
                const SizedBox(height: 12),
                _buildActionButtons(isInCart),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCompactLayout(bool isDark, bool isFavorite, bool isInCart) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // صورة صغيرة
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: SizedBox(
              width: 80,
              height: 80,
              child: _buildImage(),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // معلومات المنتج
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product.name,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: 4),
                
                _buildRatingRow(isDark, compact: true),
                
                const SizedBox(height: 4),
                
                _buildPriceAndStatus(isDark, compact: true),
              ],
            ),
          ),
          
          // أزرار الإجراء
          Column(
            children: [
              if (showFavoriteButton)
                _buildFavoriteButton(isFavorite),
              
              if (showAddToCartButton) ...[
                const SizedBox(height: 8),
                _buildQuickAddButton(isInCart),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductImage(bool isDark, bool isFavorite, bool isInCart) {
    return Stack(
      children: [
        // الصورة الرئيسية
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
          child: AspectRatio(
            aspectRatio: 16 / 12,
            child: _buildImage(),
          ),
        ),
        
        // شارات الحالة
        Positioned(
          top: 8,
          left: 8,
          child: Column(
            children: [
              if (!product.inStock)
                _buildStatusBadge('نفد المخزون', Colors.red),
              
              if (product.isOnSale)
                _buildStatusBadge('خصم', Colors.orange),
              
              if (product.isFeatured)
                _buildStatusBadge('مميز', Colors.green),
            ],
          ),
        ),
        
        // أزرار الإجراء
        Positioned(
          top: 8,
          right: 8,
          child: Column(
            children: [
              if (showFavoriteButton)
                _buildFavoriteButton(isFavorite),
              
              if (isInCart) ...[
                const SizedBox(height: 8),
                _buildInCartIndicator(),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildImage() {
    final imageUrl = product.imageUrl.isNotEmpty 
        ? product.imageUrl 
        : (product.images?.isNotEmpty == true ? product.images!.first : '');

    if (imageUrl.isEmpty) {
      return Container(
        color: Colors.grey[200],
        child: const Center(
          child: Icon(Icons.image_not_supported, size: 40, color: Colors.grey),
        ),
      );
    }

    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => const LoadingWidget(),
      errorWidget: (context, url, error) => Container(
        color: Colors.grey[200],
        child: const Icon(Icons.error, size: 40),
      ),
    );
  }

  Widget _buildStatusBadge(String text, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildFavoriteButton(bool isFavorite) {
    return Consumer<FavoritesProvider>(
      builder: (context, favoritesProvider, child) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: Icon(
              isFavorite ? Icons.favorite : Icons.favorite_border,
              color: isFavorite ? Colors.red : Colors.grey[600],
              size: 20,
            ),
            onPressed: () => favoritesProvider.toggleFavorite(product),
          ),
        );
      },
    );
  }

  Widget _buildInCartIndicator() {
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.9),
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.shopping_cart,
        color: Colors.white,
        size: 16,
      ),
    );
  }

  Widget _buildRatingRow(bool isDark, {bool compact = false}) {
    return Row(
      children: [
        Icon(
          Icons.star,
          color: Colors.amber,
          size: compact ? 14 : 16,
        ),
        const SizedBox(width: 4),
        Text(
          product.rating.toStringAsFixed(1),
          style: TextStyle(
            fontSize: compact ? 12 : 14,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '(${product.reviewsCount})',
          style: TextStyle(
            fontSize: compact ? 10 : 12,
            color: isDark ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildPriceAndStatus(bool isDark, {bool compact = false}) {
    return Row(
      children: [
        // السعر
        if (product.salePrice != null) ...[
          Text(
            '${product.salePrice} ريال',
            style: TextStyle(
              fontSize: compact ? 12 : 14,
              decoration: TextDecoration.lineThrough,
              color: isDark ? Colors.grey[400] : Colors.grey[500],
            ),
          ),
          const SizedBox(width: 8),
        ],
        
        Text(
          '${product.price ?? 0} ريال',
          style: TextStyle(
            fontSize: compact ? 14 : 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        
        const Spacer(),
        
        // حالة التوفر
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: product.inStock ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: product.inStock ? Colors.green : Colors.red,
              width: 1,
            ),
          ),
          child: Text(
            product.inStock ? 'متوفر' : 'نفد',
            style: TextStyle(
              fontSize: compact ? 10 : 12,
              fontWeight: FontWeight.bold,
              color: product.inStock ? Colors.green : Colors.red,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(bool isInCart) {
    return Consumer<CartProvider>(
      builder: (context, cartProvider, child) {
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: product.inStock 
                ? () => _addToCart(context, cartProvider)
                : null,
            icon: Icon(
              isInCart ? Icons.shopping_cart : Icons.add_shopping_cart,
              size: 16,
            ),
            label: Text(
              isInCart ? 'في السلة' : 'أضف للسلة',
              style: const TextStyle(fontSize: 14),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: isInCart 
                  ? Colors.green 
                  : Theme.of(context).primaryColor,
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickAddButton(bool isInCart) {
    return Consumer<CartProvider>(
      builder: (context, cartProvider, child) {
        return Container(
          decoration: BoxDecoration(
            color: isInCart 
                ? Colors.green.withOpacity(0.9)
                : Theme.of(context).primaryColor.withOpacity(0.9),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: Icon(
              isInCart ? Icons.shopping_cart : Icons.add_shopping_cart,
              color: Colors.white,
              size: 16,
            ),
            onPressed: product.inStock 
                ? () => _addToCart(context, cartProvider)
                : null,
          ),
        );
      },
    );
  }

  void _addToCart(BuildContext context, CartProvider cartProvider) {
    cartProvider.addToCart(product);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة ${product.name} للسلة'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'عرض السلة',
          textColor: Colors.white,
          onPressed: () => Navigator.pushNamed(context, '/cart'),
        ),
      ),
    );
  }
}
