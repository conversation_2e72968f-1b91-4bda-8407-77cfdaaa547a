<?php

namespace Botble\RevoWoocommerce\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;
use Illuminate\Contracts\View\View;
use Botble\Setting\Facades\Setting;

class IntroPagesController extends BaseController
{
    public function index(): View
    {
        $this->pageTitle('إدارة صفحات التعريف');

        $introPages = $this->getIntroPages();

        return view('plugins/revo-woocommerce-plugin-7.0.3::intro-pages.index', compact('introPages'));
    }

    public function create(): View
    {
        $this->pageTitle('إضافة صفحة تعريف جديدة');

        return view('plugins/revo-woocommerce-plugin-7.0.3::intro-pages.create');
    }

    public function store(Request $request, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'image' => 'required|string',
            'order' => 'nullable|integer|min:0',
        ], [
            'title.required' => 'عنوان الصفحة مطلوب',
            'description.required' => 'وصف الصفحة مطلوب',
            'description.max' => 'الوصف يجب أن يكون أقل من 1000 حرف',
            'image.required' => 'صورة الصفحة مطلوبة',
            'order.integer' => 'الترتيب يجب أن يكون رقم صحيح',
        ]);

        $this->storeIntroPage($request->all());

        return $response
            ->setMessage('تم إضافة صفحة التعريف بنجاح!')
            ->setNextUrl(route('revo-woocommerce.intro-pages.index'));
    }

    public function edit($id): View
    {
        $this->pageTitle('تعديل صفحة التعريف');

        $introPage = $this->getIntroPage($id);

        return view('plugins/revo-woocommerce-plugin-7.0.3::intro-pages.edit', compact('introPage'));
    }

    public function update(Request $request, $id, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'image' => 'required|string',
            'order' => 'nullable|integer|min:0',
        ], [
            'title.required' => 'عنوان الصفحة مطلوب',
            'description.required' => 'وصف الصفحة مطلوب',
            'description.max' => 'الوصف يجب أن يكون أقل من 1000 حرف',
            'image.required' => 'صورة الصفحة مطلوبة',
            'order.integer' => 'الترتيب يجب أن يكون رقم صحيح',
        ]);

        $this->updateIntroPage($id, $request->all());

        return $response
            ->setMessage('تم تحديث صفحة التعريف بنجاح!')
            ->setNextUrl(route('revo-woocommerce.intro-pages.index'));
    }

    public function destroy($id, BaseHttpResponse $response): BaseHttpResponse
    {
        $this->deleteIntroPage($id);

        return $response
            ->setMessage('تم حذف صفحة التعريف بنجاح!');
    }

    protected function getIntroPages(): array
    {
        // Get intro pages from settings
        $pagesJson = Setting::get('revo_intro_pages', '[]');
        $pages = json_decode($pagesJson, true) ?: [];

        // If no pages exist, return default data
        if (empty($pages)) {
            return [
                [
                    'id' => 1,
                    'title' => 'مرحباً بك في تطبيقنا',
                    'description' => 'اكتشف تجربة تسوق فريدة مع مجموعة واسعة من المنتجات عالية الجودة.',
                    'image' => 'https://via.placeholder.com/400x300/007bff/ffffff?text=Welcome',
                    'order' => 1,
                    'status' => true,
                    'created_at' => '2025-07-25 10:00:00',
                ],
                [
                    'id' => 2,
                    'title' => 'تسوق بسهولة',
                    'description' => 'واجهة سهلة الاستخدام تجعل التسوق أمراً ممتعاً وسريعاً.',
                    'image' => 'https://via.placeholder.com/400x300/28a745/ffffff?text=Easy+Shopping',
                    'order' => 2,
                    'status' => true,
                    'created_at' => '2025-07-25 10:05:00',
                ],
                [
                    'id' => 3,
                    'title' => 'شحن سريع وآمن',
                    'description' => 'نوصل طلباتك بسرعة وأمان إلى باب منزلك.',
                    'image' => 'https://via.placeholder.com/400x300/ffc107/ffffff?text=Fast+Delivery',
                    'order' => 3,
                    'status' => true,
                    'created_at' => '2025-07-25 10:10:00',
                ],
                [
                    'id' => 4,
                    'title' => 'دعم عملاء متميز',
                    'description' => 'فريق دعم العملاء متاح على مدار الساعة لمساعدتك.',
                    'image' => 'https://via.placeholder.com/400x300/dc3545/ffffff?text=Customer+Support',
                    'order' => 4,
                    'status' => false,
                    'created_at' => '2025-07-25 10:15:00',
                ],
            ];
        }

        return $pages;
    }

    protected function getIntroPage($id): array
    {
        $introPages = $this->getIntroPages();
        return collect($introPages)->firstWhere('id', $id) ?? [];
    }

    protected function storeIntroPage(array $data): void
    {
        // Store intro page data in settings
        $pages = $this->getIntroPages();
        $newId = count($pages) + 1;

        $newPage = [
            'id' => $newId,
            'title' => $data['title'],
            'description' => $data['description'],
            'image' => $data['image'],
            'order' => $data['order'] ?? $newId,
            'status' => true,
            'created_at' => now()->toDateTimeString(),
        ];

        $pages[] = $newPage;
        Setting::set('revo_intro_pages', json_encode($pages));
        Setting::save();
    }

    protected function updateIntroPage($id, array $data): void
    {
        // Update intro page data in settings
        $pages = $this->getIntroPages();

        foreach ($pages as $key => $page) {
            if ($page['id'] == $id) {
                $pages[$key] = [
                    'id' => $id,
                    'title' => $data['title'],
                    'description' => $data['description'],
                    'image' => $data['image'],
                    'order' => $data['order'] ?? $page['order'],
                    'status' => $page['status'],
                    'created_at' => $page['created_at'],
                ];
                break;
            }
        }

        Setting::set('revo_intro_pages', json_encode($pages));
        Setting::save();
    }

    protected function deleteIntroPage($id): void
    {
        // Delete intro page from settings
        $pages = $this->getIntroPages();

        $pages = array_filter($pages, function($page) use ($id) {
            return $page['id'] != $id;
        });

        Setting::set('revo_intro_pages', json_encode(array_values($pages)));
        Setting::save();
    }
}
