import 'package:flutter/material.dart';
import '../models/cart.dart';

class CartSummaryWidget extends StatelessWidget {
  final Cart cart;
  final bool isWholesaleCustomer;
  final bool showCouponField;
  final Function(String)? onCouponApplied;
  final VoidCallback? onCouponRemoved;

  const CartSummaryWidget({
    super.key,
    required this.cart,
    required this.isWholesaleCustomer,
    this.showCouponField = true,
    this.onCouponApplied,
    this.onCouponRemoved,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الملخص
          Text(
            'ملخص الطلب',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // تفاصيل التكلفة
          _buildCostBreakdown(),
          
          const SizedBox(height: 16),
          
          // كوبون الخصم
          if (showCouponField) ...[
            _buildCouponSection(),
            const SizedBox(height: 16),
          ],
          
          // معلومات الشحن المجاني
          _buildFreeShippingInfo(),
          
          const SizedBox(height: 16),
          
          const Divider(),
          
          const SizedBox(height: 16),
          
          // الإجمالي النهائي
          _buildTotalSection(),
          
          // معلومات إضافية للعملاء الجملة
          if (isWholesaleCustomer && cart.hasWholesaleDiscount) ...[
            const SizedBox(height: 12),
            _buildWholesaleSavings(),
          ],
        ],
      ),
    );
  }

  Widget _buildCostBreakdown() {
    return Column(
      children: [
        _buildCostRow('المجموع الفرعي', cart.formattedSubtotal),
        
        if (cart.tax > 0)
          _buildCostRow('الضرائب', cart.formattedTax),
        
        if (cart.shipping > 0)
          _buildCostRow('الشحن', cart.formattedShipping)
        else
          _buildCostRow('الشحن', 'مجاني', color: Colors.green),
        
        if (cart.hasDiscount)
          _buildCostRow('الخصم', '-${cart.formattedDiscount}', color: Colors.green),
      ],
    );
  }

  Widget _buildCostRow(String label, String value, {Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 15,
              color: Colors.grey[700],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: color ?? Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCouponSection() {
    if (cart.hasCoupon) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green[200]!),
        ),
        child: Row(
          children: [
            Icon(Icons.local_offer, color: Colors.green[700], size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'كوبون الخصم: ${cart.couponCode}',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.green[700],
                    ),
                  ),
                  Text(
                    'تم توفير ${cart.formattedDiscount}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green[600],
                    ),
                  ),
                ],
              ),
            ),
            if (onCouponRemoved != null)
              IconButton(
                onPressed: onCouponRemoved,
                icon: Icon(Icons.close, color: Colors.green[700], size: 18),
                constraints: const BoxConstraints(
                  minWidth: 32,
                  minHeight: 32,
                ),
              ),
          ],
        ),
      );
    }

    return CouponInputWidget(
      onCouponApplied: onCouponApplied,
    );
  }

  Widget _buildFreeShippingInfo() {
    const freeShippingThreshold = 100000.0;
    final amountNeeded = freeShippingThreshold - cart.subtotal;

    if (cart.subtotal >= freeShippingThreshold) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green[200]!),
        ),
        child: Row(
          children: [
            Icon(Icons.local_shipping, color: Colors.green[700], size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'تهانينا! حصلت على شحن مجاني',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.green[700],
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.blue[200]!),
        ),
        child: Row(
          children: [
            Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'أضف ${_formatPrice(amountNeeded)} للحصول على شحن مجاني',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: Colors.blue[700],
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildTotalSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'الإجمالي النهائي',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              isWholesaleCustomer && cart.hasWholesaleDiscount
                  ? cart.formattedWholesaleTotal
                  : cart.formattedTotal,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1976D2),
              ),
            ),
            Text(
              'شامل الضريبة',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWholesaleSavings() {
    final savings = cart.total - cart.wholesaleTotal;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.savings, color: Colors.green[700], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'توفير عميل الجملة',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.green[700],
                  ),
                ),
                Text(
                  'وفرت ${_formatPrice(savings)} بفضل أسعار الجملة',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatPrice(double price) {
    return '${price.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} د.ع';
  }
}

// ويدجت إدخال كوبون الخصم
class CouponInputWidget extends StatefulWidget {
  final Function(String)? onCouponApplied;

  const CouponInputWidget({
    super.key,
    this.onCouponApplied,
  });

  @override
  State<CouponInputWidget> createState() => _CouponInputWidgetState();
}

class _CouponInputWidgetState extends State<CouponInputWidget> {
  final TextEditingController _couponController = TextEditingController();
  bool _isApplying = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'كوبون الخصم',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _couponController,
                  decoration: const InputDecoration(
                    hintText: 'أدخل كود الخصم',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  textInputAction: TextInputAction.done,
                  onSubmitted: _applyCoupon,
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _isApplying ? null : () => _applyCoupon(_couponController.text),
                child: _isApplying
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('تطبيق'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _applyCoupon(String couponCode) {
    if (couponCode.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال كود الخصم'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() => _isApplying = true);

    // محاكاة تطبيق الكوبون
    Future.delayed(const Duration(seconds: 1), () {
      setState(() => _isApplying = false);
      
      if (widget.onCouponApplied != null) {
        widget.onCouponApplied!(couponCode.trim());
      }
      
      _couponController.clear();
    });
  }

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }
}

// ويدجت ملخص مبسط للسلة
class SimpleCartSummaryWidget extends StatelessWidget {
  final Cart cart;
  final bool isWholesaleCustomer;

  const SimpleCartSummaryWidget({
    super.key,
    required this.cart,
    required this.isWholesaleCustomer,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'عدد المنتجات: ${cart.itemsCount}',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              Text(
                cart.formattedSubtotal,
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
            ],
          ),
          
          if (cart.hasDiscount) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('الخصم:'),
                Text(
                  '-${cart.formattedDiscount}',
                  style: const TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
          
          const Divider(),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'الإجمالي:',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                cart.formattedTotal,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1976D2),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
