@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">اختبار تحديث السلايدر</h4>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif
                    
                    @if(session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif
                    
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <ul>
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    
                    <form action="{{ route('revo-woocommerce.home-slider.update', 1) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="form-group mb-3">
                            <label for="title">العنوان</label>
                            <input type="text" class="form-control" id="title" name="title" value="اختبار التحديث {{ now()->format('H:i:s') }}">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="image">الصورة</label>
                            <input type="text" class="form-control" id="image" name="image" value="/test/update-{{ rand(1,100) }}.jpg" required>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="link">الرابط</label>
                            <input type="url" class="form-control" id="link" name="link" value="https://update{{ rand(1,100) }}.com">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="order">الترتيب</label>
                            <input type="number" class="form-control" id="order" name="order" value="{{ rand(1,10) }}">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">تحديث الاختبار</button>
                    </form>
                    
                    <hr>
                    
                    <h5>البيانات المحفوظة حالياً:</h5>
                    <pre>{{ json_encode(json_decode(\Botble\Setting\Facades\Setting::get('revo_home_sliders', '[]'), true), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                    
                    <h5>آخر تحديث:</h5>
                    <p>{{ now()->format('Y-m-d H:i:s') }}</p>
                </div>
            </div>
        </div>
    </div>
@endsection
