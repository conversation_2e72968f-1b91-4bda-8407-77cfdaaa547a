<?php

use ArchiElite\Announcement\Http\Controllers\API\AnnouncementController;
use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => ['api', 'throttle:api'],
    'prefix' => 'api/v1/announcements',
    'namespace' => 'ArchiElite\Announcement\Http\Controllers\API',
], function (): void {
    // قائمة الإعلانات المتاحة
    Route::get('/', [AnnouncementController::class, 'index']);
    
    // إعلان محدد
    Route::get('/{id}', [AnnouncementController::class, 'show'])->wherePrimaryKey();
    
    // الإعلانات النشطة حالياً
    Route::get('/active', [AnnouncementController::class, 'getActiveAnnouncements']);
    
    // إحصائيات الإعلانات
    Route::get('/stats', [AnnouncementController::class, 'getStats']);
});
