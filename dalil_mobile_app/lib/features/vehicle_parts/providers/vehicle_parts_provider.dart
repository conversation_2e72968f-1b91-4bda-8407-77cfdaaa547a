import 'package:flutter/material.dart';
import '../../../core/services/api_service.dart';
import '../../../core/services/storage_service.dart';
import '../../../services/vehicle_parts_service.dart';
import '../models/vehicle_category_model.dart';
import '../models/saved_vehicle_model.dart';
import '../../home/<USER>/product_model.dart';

class VehiclePartsProvider extends ChangeNotifier {
  final VehiclePartsService _vehiclePartsService = VehiclePartsService();

  // حالة التحميل
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // حالة الخطأ
  bool _hasError = false;
  bool get hasError => _hasError;
  String _errorMessage = '';
  String get errorMessage => _errorMessage;

  // أنواع المركبات
  List<VehicleCategoryModel> _vehicleTypes = [];
  List<VehicleCategoryModel> get vehicleTypes => _vehicleTypes;

  // الماركات
  List<VehicleCategoryModel> _makes = [];
  List<VehicleCategoryModel> get makes => _makes;

  // الموديلات
  List<VehicleCategoryModel> _models = [];
  List<VehicleCategoryModel> get models => _models;

  // السنوات
  List<VehicleCategoryModel> _years = [];
  List<VehicleCategoryModel> get years => _years;

  // الاختيارات الحالية
  VehicleCategoryModel? _selectedVehicleType;
  VehicleCategoryModel? get selectedVehicleType => _selectedVehicleType;

  VehicleCategoryModel? _selectedMake;
  VehicleCategoryModel? get selectedMake => _selectedMake;

  VehicleCategoryModel? _selectedModel;
  VehicleCategoryModel? get selectedModel => _selectedModel;

  VehicleCategoryModel? _selectedYear;
  VehicleCategoryModel? get selectedYear => _selectedYear;

  // السيارات المحفوظة
  List<SavedVehicleModel> _savedVehicles = [];
  List<SavedVehicleModel> get savedVehicles => _savedVehicles;

  // قطع الغيار الشائعة
  List<ProductModel> _popularParts = [];
  List<ProductModel> get popularParts => _popularParts;

  // نتائج البحث السريع
  List<VehicleCategoryModel> _quickSearchResults = [];
  List<VehicleCategoryModel> get quickSearchResults => _quickSearchResults;

  VehiclePartsProvider() {
    _loadSavedVehicles();
  }

  // تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    await loadVehicleTypes();
    // تحميل قطع الغيار الشائعة سيتم عند اختيار فئة معينة
  }

  // تحميل أنواع المركبات
  Future<void> loadVehicleTypes() async {
    _setLoading(true);
    _setError(false, '');

    try {
      final response = await _vehiclePartsService.getRootCategories();

      if (response.isSuccess && response.data != null) {
        _vehicleTypes = response.data!.map((category) =>
          VehicleCategoryModel.fromJson({
            'id': category.id,
            'name': category.name,
            'slug': category.name.toLowerCase().replaceAll(' ', '-'),
            'description': category.description,
            'image_url': category.icon,
            'parent_id': 0,
            'has_children': category.children?.isNotEmpty ?? false,
            'children_count': category.children?.length ?? 0,
            'order': 0,
          })
        ).toList();

        notifyListeners();
      } else {
        _setError(true, response.error ?? 'فشل في تحميل أنواع المركبات');
      }
    } catch (e) {
      _setError(true, 'حدث خطأ أثناء تحميل أنواع المركبات');
      debugPrint('Error loading vehicle types: $e');
    } finally {
      _setLoading(false);
    }
  }

  // تحميل الماركات
  Future<void> loadMakes() async {
    _setLoading(true);
    _setError(false, '');

    try {
      final response = await _vehiclePartsService.getVehicleMakes();

      if (response.isSuccess && response.data != null) {
        _makes = response.data!.map((make) =>
          VehicleCategoryModel.fromJson({
            'id': make.id,
            'name': make.name,
            'slug': make.name.toLowerCase().replaceAll(' ', '-'),
            'description': make.name, // استخدام الاسم بدلاً من description
            'image_url': make.logo ?? '', // استخدام logo بدلاً من icon
            'parent_id': 0,
            'has_children': false,
            'children_count': 0,
            'order': 0,
          })
        ).toList();

        // مسح الاختيارات التابعة
        _selectedMake = null;
        _selectedModel = null;
        _selectedYear = null;
        _models.clear();
        _years.clear();

        notifyListeners();
      } else {
        _setError(true, response.error ?? 'فشل في تحميل الماركات');
      }
    } catch (e) {
      _setError(true, 'حدث خطأ أثناء تحميل الماركات');
      debugPrint('Error loading makes: $e');
    } finally {
      _setLoading(false);
    }
  }

  // تحميل الموديلات
  Future<void> loadModels(int makeId) async {
    _setLoading(true);
    _setError(false, '');

    try {
      final response = await ApiService.instance.get('/vehicle-parts/makes/$makeId/models');
      
      if (response.success && response.data != null) {
        final data = response.data as List;
        _models = data.map((json) => VehicleCategoryModel.fromJson(json)).toList();
        
        // مسح الاختيارات التابعة
        _selectedModel = null;
        _selectedYear = null;
        _years.clear();
      } else {
        _setError(true, response.message ?? 'فشل في تحميل الموديلات');
      }
    } catch (e) {
      _setError(true, 'حدث خطأ أثناء تحميل الموديلات');
      debugPrint('Error loading models: $e');
    } finally {
      _setLoading(false);
    }
  }

  // تحميل السنوات
  Future<void> loadYears(int modelId) async {
    _setLoading(true);
    _setError(false, '');

    try {
      final response = await ApiService.instance.get('/vehicle-parts/models/$modelId/years');
      
      if (response.success && response.data != null) {
        final data = response.data as List;
        _years = data.map((json) => VehicleCategoryModel.fromJson(json)).toList();
        
        // مسح اختيار السنة
        _selectedYear = null;
      } else {
        _setError(true, response.message ?? 'فشل في تحميل السنوات');
      }
    } catch (e) {
      _setError(true, 'حدث خطأ أثناء تحميل السنوات');
      debugPrint('Error loading years: $e');
    } finally {
      _setLoading(false);
    }
  }

  // البحث السريع
  Future<void> quickSearch(String query) async {
    if (query.trim().isEmpty) {
      _quickSearchResults.clear();
      notifyListeners();
      return;
    }

    try {
      final response = await ApiService.instance.get(
        '/vehicle-parts/quick-search',
        queryParameters: {
          'q': query,
          'limit': 10,
        },
      );
      
      if (response.success && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List;
        _quickSearchResults = results.map((json) => VehicleCategoryModel.fromJson(json)).toList();
      } else {
        _quickSearchResults.clear();
      }
    } catch (e) {
      _quickSearchResults.clear();
      debugPrint('Error in quick search: $e');
    }
    
    notifyListeners();
  }

  // تحميل قطع الغيار الشائعة (تم نقلها إلى loadPopularPartsForCategory)

  // تحديد نوع المركبة
  void selectVehicleType(VehicleCategoryModel vehicleType) {
    _selectedVehicleType = vehicleType;
    _selectedMake = null;
    _selectedModel = null;
    _selectedYear = null;
    _makes.clear();
    _models.clear();
    _years.clear();
    
    notifyListeners();
    
    // تحميل الماركات
    loadMakes();
  }

  // تحديد الماركة
  void selectMake(VehicleCategoryModel make) {
    _selectedMake = make;
    _selectedModel = null;
    _selectedYear = null;
    _models.clear();
    _years.clear();
    
    notifyListeners();
    
    // تحميل الموديلات
    loadModels(make.id);
  }

  // تحديد الموديل
  void selectModel(VehicleCategoryModel model) {
    _selectedModel = model;
    _selectedYear = null;
    _years.clear();
    
    notifyListeners();
    
    // تحميل السنوات
    loadYears(model.id);
  }

  // تحديد السنة
  void selectYear(VehicleCategoryModel year) {
    _selectedYear = year;
    notifyListeners();
  }

  // التحقق من إمكانية البحث
  bool get canSearch {
    return _selectedVehicleType != null &&
           _selectedMake != null &&
           _selectedModel != null &&
           _selectedYear != null;
  }

  // تحميل السيارات المحفوظة
  Future<void> _loadSavedVehicles() async {
    try {
      final vehiclesData = await StorageService.getSavedVehicles();
      _savedVehicles = vehiclesData.map((json) => SavedVehicleModel.fromJson(json)).toList();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading saved vehicles: $e');
    }
  }

  // إضافة سيارة محفوظة
  Future<void> addSavedVehicle(String name) async {
    if (!canSearch) return;

    final vehicle = SavedVehicleModel(
      id: DateTime.now().millisecondsSinceEpoch,
      name: name,
      vehicleType: _selectedVehicleType!,
      make: _selectedMake!,
      model: _selectedModel!,
      year: _selectedYear!,
      createdAt: DateTime.now(),
    );

    _savedVehicles.add(vehicle);
    
    // حفظ في التخزين المحلي
    final vehiclesData = _savedVehicles.map((v) => v.toJson()).toList();
    await StorageService.saveSavedVehicles(vehiclesData);
    
    notifyListeners();
  }

  // حذف سيارة محفوظة
  Future<void> removeSavedVehicle(int vehicleId) async {
    _savedVehicles.removeWhere((vehicle) => vehicle.id == vehicleId);
    
    // حفظ في التخزين المحلي
    final vehiclesData = _savedVehicles.map((v) => v.toJson()).toList();
    await StorageService.saveSavedVehicles(vehiclesData);
    
    notifyListeners();
  }

  // تحديد سيارة محفوظة
  void selectSavedVehicle(SavedVehicleModel vehicle) {
    _selectedVehicleType = vehicle.vehicleType;
    _selectedMake = vehicle.make;
    _selectedModel = vehicle.model;
    _selectedYear = vehicle.year;
    
    notifyListeners();
  }

  // مسح الاختيارات
  void clearSelections() {
    _selectedVehicleType = null;
    _selectedMake = null;
    _selectedModel = null;
    _selectedYear = null;
    _makes.clear();
    _models.clear();
    _years.clear();
    
    notifyListeners();
  }

  // البحث عن قطع الغيار
  Future<List<ProductModel>> searchVehicleParts({
    List<int>? categoryIds,
    String? keyword,
    int page = 1,
    int perPage = 12,
  }) async {
    try {
      final response = await _vehiclePartsService.searchVehicleParts(
        categoryIds: categoryIds,
        keyword: keyword,
        page: page,
        perPage: perPage,
      );

      if (response.isSuccess && response.data != null) {
        final products = response.data!['products'] as List?;
        if (products != null) {
          return products.map((json) => ProductModel.fromJson(json)).toList();
        }
      }
      return [];
    } catch (e) {
      debugPrint('Error searching vehicle parts: $e');
      return [];
    }
  }

  // البحث السريع
  Future<void> performQuickSearch(String keyword) async {
    if (keyword.isEmpty) {
      _quickSearchResults.clear();
      notifyListeners();
      return;
    }

    try {
      final response = await _vehiclePartsService.quickSearch(keyword);

      if (response.isSuccess && response.data != null) {
        // معالجة نتائج البحث السريع
        _quickSearchResults.clear();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error performing quick search: $e');
    }
  }

  // الحصول على قطع الغيار الشائعة
  Future<void> loadPopularPartsForCategory(int categoryId) async {
    try {
      final response = await _vehiclePartsService.getPopularParts(categoryId);

      if (response.isSuccess && response.data != null) {
        final parts = response.data!['popular_parts'] as List?;
        if (parts != null) {
          _popularParts = parts.map((json) => ProductModel.fromJson(json)).toList();
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Error loading popular parts: $e');
    }
  }

  // تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // تحديث حالة الخطأ
  void _setError(bool hasError, String message) {
    _hasError = hasError;
    _errorMessage = message;
    notifyListeners();
  }
}
