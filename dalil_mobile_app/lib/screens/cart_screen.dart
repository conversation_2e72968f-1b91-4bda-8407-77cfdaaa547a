import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/cart.dart';
import '../services/cart_service.dart';
import '../services/auth_service_new.dart';
import '../widgets/cart_item_widget.dart';
import '../widgets/cart_summary_widget.dart';
import '../providers/cart_provider.dart';
import '../providers/theme_provider.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final CartService _cartService = CartService();
  final AuthServiceNew _authService = AuthServiceNew();
  
  Cart? _cart;
  bool _isLoading = true;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    _loadCart();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _cart == null || _cart!.items.isEmpty
              ? _buildEmptyCart()
              : _buildCartContent(),
      bottomNavigationBar: _cart != null && _cart!.items.isNotEmpty
          ? _buildCheckoutBar()
          : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('سلة التسوق${_cart != null ? ' (${_cart!.itemsCount})' : ''}'),
      actions: [
        if (_cart != null && _cart!.items.isNotEmpty)
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: _showClearCartDialog,
            tooltip: 'مسح السلة',
          ),
      ],
    );
  }

  Widget _buildCartContent() {
    return Column(
      children: [
        // رسالة ترحيبية للعملاء المسجلين
        if (_authService.isLoggedIn)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1976D2).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF1976D2).withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.person,
                  color: const Color(0xFF1976D2),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'مرحباً ${_authService.currentUser?.displayName}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                ),
                if (_authService.isWholesaleCustomer)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'عميل جملة',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),

        // قائمة عناصر السلة
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _cart!.items.length,
            itemBuilder: (context, index) {
              final item = _cart!.items[index];
              return CartItemWidget(
                item: item,
                isWholesaleCustomer: _authService.isWholesaleCustomer,
                onQuantityChanged: (newQuantity) {
                  _updateItemQuantity(item.id, newQuantity);
                },
                onRemove: () {
                  _removeItem(item.id);
                },
                onToggleFavorite: () {
                  _toggleItemFavorite(item.productId);
                },
              );
            },
          ),
        ),

        // ملخص السلة
        CartSummaryWidget(
          cart: _cart!,
          isWholesaleCustomer: _authService.isWholesaleCustomer,
        ),
      ],
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 120,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 24),
            Text(
              'سلة التسوق فارغة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'ابدأ بإضافة المنتجات التي تحتاجها',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: const Icon(Icons.shopping_bag),
              label: const Text('تصفح المنتجات'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextButton.icon(
              onPressed: () {
                // الانتقال لصفحة Vehicle Parts Finder
              },
              icon: const Icon(Icons.search),
              label: const Text('البحث عن قطع الغيار'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCheckoutBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // إجمالي السعر
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الإجمالي:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      _cart!.formattedTotal,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1976D2),
                      ),
                    ),
                    if (_authService.isWholesaleCustomer && _cart!.hasWholesaleDiscount)
                      Text(
                        'وفرت: ${_cart!.formattedSavings}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.green,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // أزرار الإجراءات
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _saveForLater,
                    icon: const Icon(Icons.bookmark_border),
                    label: const Text('حفظ للاحقاً'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: ElevatedButton.icon(
                    onPressed: _isUpdating ? null : _proceedToCheckout,
                    icon: _isUpdating
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.payment),
                    label: Text(_isUpdating ? 'جاري التحديث...' : 'إتمام الطلب'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // === دوال الأحداث ===

  Future<void> _loadCart() async {
    setState(() => _isLoading = true);

    try {
      final response = await _cartService.getCart();
      if (response.isSuccess) {
        setState(() {
          _cart = response.data;
        });
      } else {
        _showError(response.error ?? 'فشل في تحميل السلة');
      }
    } catch (e) {
      _showError('خطأ في تحميل السلة: ${e.toString()}');
    }

    setState(() => _isLoading = false);
  }

  Future<void> _updateItemQuantity(int itemId, int newQuantity) async {
    if (newQuantity <= 0) {
      _removeItem(itemId);
      return;
    }

    setState(() => _isUpdating = true);

    try {
      final response = await _cartService.updateItemQuantity(itemId, newQuantity);
      if (response.isSuccess) {
        setState(() {
          _cart = response.data;
        });
        _showSuccess('تم تحديث الكمية');
      } else {
        _showError(response.error ?? 'فشل في تحديث الكمية');
      }
    } catch (e) {
      _showError('خطأ في تحديث الكمية: ${e.toString()}');
    }

    setState(() => _isUpdating = false);
  }

  Future<void> _removeItem(int itemId) async {
    setState(() => _isUpdating = true);

    try {
      final response = await _cartService.removeItem(itemId);
      if (response.isSuccess) {
        setState(() {
          _cart = response.data;
        });
        _showSuccess('تم حذف المنتج من السلة');
      } else {
        _showError(response.error ?? 'فشل في حذف المنتج');
      }
    } catch (e) {
      _showError('خطأ في حذف المنتج: ${e.toString()}');
    }

    setState(() => _isUpdating = false);
  }

  Future<void> _toggleItemFavorite(int productId) async {
    try {
      // إضافة/حذف من المفضلة
      _showSuccess('تم تحديث المفضلة');
    } catch (e) {
      _showError('خطأ في تحديث المفضلة');
    }
  }

  void _showClearCartDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح السلة'),
        content: const Text('هل أنت متأكد من رغبتك في مسح جميع المنتجات من السلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _clearCart();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  Future<void> _clearCart() async {
    setState(() => _isUpdating = true);

    try {
      final response = await _cartService.clearCart();
      if (response.isSuccess) {
        setState(() {
          _cart = Cart.empty();
        });
        _showSuccess('تم مسح السلة');
      } else {
        _showError(response.error ?? 'فشل في مسح السلة');
      }
    } catch (e) {
      _showError('خطأ في مسح السلة: ${e.toString()}');
    }

    setState(() => _isUpdating = false);
  }

  void _saveForLater() {
    _showSuccess('تم حفظ السلة للاحقاً');
  }

  void _proceedToCheckout() {
    if (!_authService.isLoggedIn) {
      _showLoginDialog();
      return;
    }

    // الانتقال لصفحة الدفع
    _showSuccess('الانتقال لصفحة الدفع...');
  }

  void _showLoginDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الدخول مطلوب'),
        content: const Text('يجب تسجيل الدخول أولاً لإتمام عملية الشراء'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // الانتقال لصفحة تسجيل الدخول
            },
            child: const Text('تسجيل الدخول'),
          ),
        ],
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
