<?php

use Botble\Base\Facades\BaseHelper;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Botble\Ecommerce\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(['prefix' => BaseHelper::getAdminPrefix(), 'middleware' => 'auth'], function () {
        Route::group(['prefix' => 'ecommerce/car-models', 'as' => 'car-models.'], function () {
            Route::resource('', 'CarModelController')->parameters(['' => 'car-model']);

            Route::delete('items/destroy', [
                'as' => 'deletes',
                'uses' => 'CarModelController@deletes',
                'permission' => 'car-models.destroy',
            ]);
        });
    });
});
