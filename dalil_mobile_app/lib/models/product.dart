import '../config/app_config.dart';

class Product {
  final int id;
  final String name;
  final String slug;
  final String? description;
  final String? shortDescription;
  final String? sku;
  final double price;
  final double? salePrice;
  final double? wholesalePrice;
  final bool onSale;
  final bool inStock;
  final int? stockQuantity;
  final String? stockStatus;
  final List<String> images;
  final String? featuredImage;
  final List<Category> categories;
  final Brand? brand;
  final double? rating;
  final int reviewCount;
  final Map<String, dynamic>? attributes;
  final List<ProductVariation>? variations;
  final bool isFeatured;
  final bool isNew;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Product({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.shortDescription,
    this.sku,
    required this.price,
    this.salePrice,
    this.wholesalePrice,
    this.onSale = false,
    this.inStock = true,
    this.stockQuantity,
    this.stockStatus,
    this.images = const [],
    this.featuredImage,
    this.categories = const [],
    this.brand,
    this.rating,
    this.reviewCount = 0,
    this.attributes,
    this.variations,
    this.isFeatured = false,
    this.isNew = false,
    this.createdAt,
    this.updatedAt,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      slug: json['slug'] ?? '',
      description: json['description'],
      shortDescription: json['short_description'],
      sku: json['sku'],
      price: _parsePrice(json['price']),
      salePrice: _parsePrice(json['sale_price']),
      wholesalePrice: _parsePrice(json['wholesale_price']),
      onSale: json['on_sale'] ?? false,
      inStock: json['in_stock'] ?? true,
      stockQuantity: json['stock_quantity'],
      stockStatus: json['stock_status'],
      images: _parseImages(json['images']),
      featuredImage: json['featured_image'],
      categories: _parseCategories(json['categories']),
      brand: json['brand'] != null ? Brand.fromJson(json['brand']) : null,
      rating: _parseRating(json['rating']),
      reviewCount: json['review_count'] ?? 0,
      attributes: json['attributes'],
      variations: _parseVariations(json['variations']),
      isFeatured: json['is_featured'] ?? false,
      isNew: json['is_new'] ?? false,
      createdAt: _parseDateTime(json['created_at']),
      updatedAt: _parseDateTime(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'short_description': shortDescription,
      'sku': sku,
      'price': price,
      'sale_price': salePrice,
      'wholesale_price': wholesalePrice,
      'on_sale': onSale,
      'in_stock': inStock,
      'stock_quantity': stockQuantity,
      'stock_status': stockStatus,
      'images': images,
      'featured_image': featuredImage,
      'categories': categories.map((c) => c.toJson()).toList(),
      'brand': brand?.toJson(),
      'rating': rating,
      'review_count': reviewCount,
      'attributes': attributes,
      'variations': variations?.map((v) => v.toJson()).toList(),
      'is_featured': isFeatured,
      'is_new': isNew,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Getters مساعدة
  double get currentPrice => salePrice ?? price;
  double get discountAmount => onSale && salePrice != null ? price - salePrice! : 0;
  double get discountPercentage => onSale && salePrice != null ? ((price - salePrice!) / price) * 100 : 0;
  
  String get formattedPrice => AppConfig.formatPrice(currentPrice);
  String get formattedOriginalPrice => AppConfig.formatPrice(price);
  String get formattedSalePrice => salePrice != null ? AppConfig.formatPrice(salePrice) : '';
  String get formattedWholesalePrice => wholesalePrice != null ? AppConfig.formatPrice(wholesalePrice) : '';
  
  String get mainImageUrl => AppConfig.getImageUrl(featuredImage ?? (images.isNotEmpty ? images.first : null));
  List<String> get imageUrls => images.map<String>((img) => AppConfig.getImageUrl(img)).toList();
  
  bool get hasDiscount => onSale && salePrice != null && salePrice! < price;
  bool get hasWholesalePrice => wholesalePrice != null && wholesalePrice! > 0;
  bool get hasVariations => variations != null && variations!.isNotEmpty;
  bool get hasRating => rating != null && rating! > 0;
  
  String get stockStatusText {
    if (!inStock) return 'غير متوفر';
    if (stockQuantity != null) {
      if (stockQuantity! <= 0) return 'غير متوفر';
      if (stockQuantity! <= 5) return 'كمية محدودة';
    }
    return 'متوفر';
  }

  // دوال مساعدة للتحويل
  static double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is double) return price;
    if (price is int) return price.toDouble();
    if (price is String) return double.tryParse(price) ?? 0.0;
    return 0.0;
  }

  static double? _parseRating(dynamic rating) {
    if (rating == null) return null;
    if (rating is double) return rating;
    if (rating is int) return rating.toDouble();
    if (rating is String) return double.tryParse(rating);
    return null;
  }

  static List<String> _parseImages(dynamic images) {
    if (images == null) return [];
    if (images is List) return images.map((img) => img.toString()).toList();
    if (images is String) return [images];
    return [];
  }

  static List<Category> _parseCategories(dynamic categories) {
    if (categories == null) return [];
    if (categories is List) {
      return categories
          .map((cat) => Category.fromJson(cat as Map<String, dynamic>))
          .toList();
    }
    return [];
  }

  static List<ProductVariation>? _parseVariations(dynamic variations) {
    if (variations == null) return null;
    if (variations is List) {
      return variations
          .map((var_) => ProductVariation.fromJson(var_ as Map<String, dynamic>))
          .toList();
    }
    return null;
  }

  static DateTime? _parseDateTime(dynamic dateTime) {
    if (dateTime == null) return null;
    if (dateTime is String) return DateTime.tryParse(dateTime);
    return null;
  }

  @override
  String toString() {
    return 'Product(id: $id, name: $name, price: $formattedPrice)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class ProductVariation {
  final int id;
  final int productId;
  final String? sku;
  final double price;
  final double? salePrice;
  final bool inStock;
  final int? stockQuantity;
  final Map<String, dynamic> attributes;
  final String? image;

  ProductVariation({
    required this.id,
    required this.productId,
    this.sku,
    required this.price,
    this.salePrice,
    this.inStock = true,
    this.stockQuantity,
    this.attributes = const {},
    this.image,
  });

  factory ProductVariation.fromJson(Map<String, dynamic> json) {
    return ProductVariation(
      id: json['id'] ?? 0,
      productId: json['product_id'] ?? 0,
      sku: json['sku'],
      price: Product._parsePrice(json['price']),
      salePrice: Product._parsePrice(json['sale_price']),
      inStock: json['in_stock'] ?? true,
      stockQuantity: json['stock_quantity'],
      attributes: json['attributes'] ?? {},
      image: json['image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'sku': sku,
      'price': price,
      'sale_price': salePrice,
      'in_stock': inStock,
      'stock_quantity': stockQuantity,
      'attributes': attributes,
      'image': image,
    };
  }

  double get currentPrice => salePrice ?? price;
  String get formattedPrice => AppConfig.formatPrice(currentPrice);
  String get imageUrl => AppConfig.getImageUrl(image);
}

class Category {
  final int id;
  final String name;
  final String slug;
  final String? description;
  final String? image;
  final String? icon;
  final int? parentId;
  final List<Category>? children;
  final int productCount;
  final bool isActive;

  Category({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.image,
    this.icon,
    this.parentId,
    this.children,
    this.productCount = 0,
    this.isActive = true,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      slug: json['slug'] ?? '',
      description: json['description'],
      image: json['image'],
      icon: json['icon'],
      parentId: json['parent_id'],
      children: json['children'] != null
          ? (json['children'] as List)
              .map((child) => Category.fromJson(child))
              .toList()
          : null,
      productCount: json['product_count'] ?? 0,
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'image': image,
      'icon': icon,
      'parent_id': parentId,
      'children': children?.map((c) => c.toJson()).toList(),
      'product_count': productCount,
      'is_active': isActive,
    };
  }

  String get imageUrl => AppConfig.getCategoryImageUrl(image);
  String get iconUrl => AppConfig.getImageUrl(icon);
  bool get hasChildren => children != null && children!.isNotEmpty;
  bool get isParent => parentId == null;

  @override
  String toString() {
    return 'Category(id: $id, name: $name, productCount: $productCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class Brand {
  final int id;
  final String name;
  final String slug;
  final String? description;
  final String? logo;
  final String? website;
  final int productCount;
  final bool isActive;

  Brand({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.logo,
    this.website,
    this.productCount = 0,
    this.isActive = true,
  });

  factory Brand.fromJson(Map<String, dynamic> json) {
    return Brand(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      slug: json['slug'] ?? '',
      description: json['description'],
      logo: json['logo'],
      website: json['website'],
      productCount: json['product_count'] ?? 0,
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'logo': logo,
      'website': website,
      'product_count': productCount,
      'is_active': isActive,
    };
  }

  String get logoUrl => AppConfig.getImageUrl(logo);

  @override
  String toString() {
    return 'Brand(id: $id, name: $name, productCount: $productCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Brand && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
