<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Http\Controllers;

use Bo<PERSON>ble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Botble\Base\Forms\FormBuilder;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Ecommerce\Forms\CarMakeForm;
use Botble\Ecommerce\Http\Requests\CarMakeRequest;
use Botble\Ecommerce\Models\CarMake;
use Bo<PERSON>ble\Ecommerce\Tables\CarMakeTable;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CarMakeController extends BaseController
{
    protected const CAR_MAKE_MODULE_SCREEN_NAME = 'car-make';

    public function index(CarMakeTable $table)
    {
        PageTitle::setTitle(trans('plugins/ecommerce::car-makes.name'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/ecommerce::car-makes.create'));

        return $formBuilder->create(CarMakeForm::class)->renderForm();
    }

    public function store(CarMakeRequest $request, BaseHttpResponse $response)
    {
        $data = $request->input();

        // التحقق من فرادة الـ slug وإضافة رمز عشوائي إذا كان مكرراً
        $slug = $data['slug'] ?? Str::slug($data['name']);
        $originalSlug = $slug;

        $count = 0;
        while (CarMake::query()->where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . ++$count;
        }

        $data['slug'] = $slug;

        $carMake = CarMake::query()->create($data);

        event(new CreatedContentEvent(self::CAR_MAKE_MODULE_SCREEN_NAME, $request, $carMake));

        return $response
            ->setPreviousUrl(route('car-makes.index'))
            ->setNextUrl(route('car-makes.edit', $carMake->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(CarMake $carMake, FormBuilder $formBuilder, Request $request)
    {
        event(new BeforeEditContentEvent($request, $carMake));

        PageTitle::setTitle(trans('plugins/ecommerce::car-makes.edit') . ' "' . $carMake->name . '"');

        return $formBuilder->create(CarMakeForm::class, ['model' => $carMake])->renderForm();
    }

    public function update(CarMake $carMake, CarMakeRequest $request, BaseHttpResponse $response)
    {
        $data = $request->input();

        // التحقق من فرادة الـ slug وإضافة رمز عشوائي إذا كان مكرراً
        if ($request->input('slug') != $carMake->slug) {
            $slug = $data['slug'] ?? Str::slug($data['name']);
            $originalSlug = $slug;

            $count = 0;
            while (CarMake::query()->where('slug', $slug)->where('id', '!=', $carMake->id)->exists()) {
                $slug = $originalSlug . '-' . ++$count;
            }

            $data['slug'] = $slug;
        }

        $carMake->fill($data);
        $carMake->save();

        event(new UpdatedContentEvent(self::CAR_MAKE_MODULE_SCREEN_NAME, $request, $carMake));

        return $response
            ->setPreviousUrl(route('car-makes.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(CarMake $carMake, Request $request, BaseHttpResponse $response)
    {
        try {
            $carMake->delete();

            event(new DeletedContentEvent(self::CAR_MAKE_MODULE_SCREEN_NAME, $request, $carMake));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function deletes(Request $request, BaseHttpResponse $response)
    {
        $ids = $request->input('ids');
        if (empty($ids)) {
            return $response
                ->setError()
                ->setMessage(trans('core/base::notices.no_select'));
        }

        foreach ($ids as $id) {
            $carMake = CarMake::query()->find($id);
            if ($carMake) {
                $carMake->delete();
                event(new DeletedContentEvent(self::CAR_MAKE_MODULE_SCREEN_NAME, $request, $carMake));
            }
        }

        return $response->setMessage(trans('core/base::notices.delete_success_message'));
    }
}
