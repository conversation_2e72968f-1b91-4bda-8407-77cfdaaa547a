<?php

use Botble\Analytics\Http\Controllers\API\AnalyticsController;
use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => ['api', 'throttle:api'],
    'prefix' => 'api/v1/analytics',
    'namespace' => 'Botble\Analytics\Http\Controllers\API',
], function (): void {
    // إحصائيات عامة
    Route::get('/general', [AnalyticsController::class, 'getGeneralStats']);
    
    // أكثر الصفحات زيارة
    Route::get('/top-pages', [AnalyticsController::class, 'getTopPages']);
    
    // أهم المصادر المرجعية
    Route::get('/top-referrers', [AnalyticsController::class, 'getTopReferrers']);
    
    // أهم المتصفحات
    Route::get('/top-browsers', [AnalyticsController::class, 'getTopBrowsers']);
    
    // إحصائيات الزوار حسب الفترة
    Route::get('/visitors', [AnalyticsController::class, 'getVisitorsStats']);
    
    // إحصائيات الدول
    Route::get('/countries', [AnalyticsController::class, 'getCountriesStats']);
    
    // إحصائيات مخصصة
    Route::post('/custom-query', [AnalyticsController::class, 'customQuery']);
});
