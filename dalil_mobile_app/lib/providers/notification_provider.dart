import 'package:flutter/material.dart';
import '../services/notification_service.dart';

class NotificationProvider extends ChangeNotifier {
  final NotificationService _notificationService = NotificationService();
  
  List<NotificationModel> _notifications = [];
  Map<String, bool> _settings = {};
  int _unreadCount = 0;
  bool _isLoading = false;

  List<NotificationModel> get notifications => List.unmodifiable(_notifications);
  Map<String, bool> get settings => Map.unmodifiable(_settings);
  int get unreadCount => _unreadCount;
  bool get isLoading => _isLoading;

  // تحميل البيانات الأولية
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _notificationService.loadSettings();
      _settings = _notificationService.settings;
      
      await loadNotifications();
      await updateUnreadCount();
    } catch (e) {
      debugPrint('Error initializing notifications: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل الإشعارات
  Future<void> loadNotifications() async {
    try {
      _notifications = await _notificationService.getNotifications();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading notifications: $e');
    }
  }

  // تحديث عدد الإشعارات غير المقروءة
  Future<void> updateUnreadCount() async {
    try {
      _unreadCount = await _notificationService.getUnreadCount();
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating unread count: $e');
    }
  }

  // إضافة إشعار جديد
  Future<void> addNotification(NotificationModel notification) async {
    try {
      await _notificationService.addNotification(notification);
      await loadNotifications();
      await updateUnreadCount();
    } catch (e) {
      debugPrint('Error adding notification: $e');
    }
  }

  // تمييز إشعار كمقروء
  Future<void> markAsRead(String notificationId) async {
    try {
      await _notificationService.markAsRead(notificationId);
      
      // تحديث الحالة محلياً
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index >= 0) {
        _notifications[index].isRead = true;
        _unreadCount = _notifications.where((n) => !n.isRead).length;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    }
  }

  // تمييز جميع الإشعارات كمقروءة
  Future<void> markAllAsRead() async {
    try {
      await _notificationService.markAllAsRead();
      
      // تحديث الحالة محلياً
      for (var notification in _notifications) {
        notification.isRead = true;
      }
      _unreadCount = 0;
      notifyListeners();
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
    }
  }

  // حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _notificationService.deleteNotification(notificationId);
      
      // تحديث الحالة محلياً
      _notifications.removeWhere((n) => n.id == notificationId);
      await updateUnreadCount();
    } catch (e) {
      debugPrint('Error deleting notification: $e');
    }
  }

  // مسح جميع الإشعارات
  Future<void> clearAllNotifications() async {
    try {
      await _notificationService.clearAllNotifications();
      _notifications.clear();
      _unreadCount = 0;
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing notifications: $e');
    }
  }

  // تحديث إعدادات الإشعارات
  Future<void> updateSetting(String key, bool value) async {
    try {
      await _notificationService.updateSetting(key, value);
      _settings[key] = value;
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating notification setting: $e');
    }
  }

  // إنشاء إشعارات تجريبية
  Future<void> createSampleNotifications() async {
    try {
      await _notificationService.createSampleNotifications();
      await loadNotifications();
      await updateUnreadCount();
    } catch (e) {
      debugPrint('Error creating sample notifications: $e');
    }
  }

  // إنشاء إشعار منتج جديد
  Future<void> createNewProductNotification(String productName, String productId) async {
    try {
      await _notificationService.createNewProductNotification(productName, productId);
      await loadNotifications();
      await updateUnreadCount();
    } catch (e) {
      debugPrint('Error creating new product notification: $e');
    }
  }

  // إنشاء إشعار عرض خاص
  Future<void> createOfferNotification(String title, String description, {Map<String, dynamic>? data}) async {
    try {
      await _notificationService.createOfferNotification(title, description, data: data);
      await loadNotifications();
      await updateUnreadCount();
    } catch (e) {
      debugPrint('Error creating offer notification: $e');
    }
  }

  // إنشاء إشعار تحديث الطلب
  Future<void> createOrderUpdateNotification(String orderId, String status) async {
    try {
      await _notificationService.createOrderUpdateNotification(orderId, status);
      await loadNotifications();
      await updateUnreadCount();
    } catch (e) {
      debugPrint('Error creating order update notification: $e');
    }
  }

  // إنشاء إشعار عام
  Future<void> createGeneralNotification(String title, String body, {Map<String, dynamic>? data}) async {
    try {
      await _notificationService.createGeneralNotification(title, body, data: data);
      await loadNotifications();
      await updateUnreadCount();
    } catch (e) {
      debugPrint('Error creating general notification: $e');
    }
  }

  // الحصول على الإشعارات حسب النوع
  List<NotificationModel> getNotificationsByType(String type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // الحصول على الإشعارات غير المقروءة
  List<NotificationModel> get unreadNotifications {
    return _notifications.where((n) => !n.isRead).toList();
  }

  // الحصول على آخر الإشعارات
  List<NotificationModel> getRecentNotifications([int limit = 5]) {
    return _notifications.take(limit).toList();
  }

  // البحث في الإشعارات
  List<NotificationModel> searchNotifications(String query) {
    if (query.isEmpty) return _notifications;
    
    final lowerQuery = query.toLowerCase();
    return _notifications.where((n) => 
      n.title.toLowerCase().contains(lowerQuery) ||
      n.body.toLowerCase().contains(lowerQuery)
    ).toList();
  }

  // تجميع الإشعارات حسب التاريخ
  Map<String, List<NotificationModel>> groupNotificationsByDate() {
    final Map<String, List<NotificationModel>> grouped = {};
    
    for (var notification in _notifications) {
      final date = DateTime(
        notification.createdAt.year,
        notification.createdAt.month,
        notification.createdAt.day,
      );
      
      String dateKey;
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));
      
      if (date == today) {
        dateKey = 'اليوم';
      } else if (date == yesterday) {
        dateKey = 'أمس';
      } else {
        dateKey = '${date.day}/${date.month}/${date.year}';
      }
      
      grouped[dateKey] ??= [];
      grouped[dateKey]!.add(notification);
    }
    
    return grouped;
  }
}
