<?php

namespace Botble\Ecommerce\Http\Controllers\API;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Ecommerce\Http\Requests\API\WholesaleApplicationRequest;
use Botble\Ecommerce\Http\Resources\API\CustomerResource;
use Botble\Ecommerce\Models\Customer;
use Botble\Ecommerce\Repositories\Interfaces\CustomerInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WholesaleController extends BaseController
{
    protected $customerRepository;

    public function __construct(CustomerInterface $customerRepository)
    {
        $this->customerRepository = $customerRepository;
    }

    /**
     * طلب تحويل لعميل جملة
     *
     * @group Wholesale
     */
    public function applyForWholesale(WholesaleApplicationRequest $request)
    {
        try {
            $customer = Auth::guard('customer')->user();
            
            if (!$customer) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setCode(401)
                    ->setMessage(__('Unauthenticated'));
            }

            if ($customer->is_wholesale) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setCode(400)
                    ->setMessage(__('You are already a wholesale customer.'));
            }

            // تحديث بيانات العميل مع طلب التحويل
            $customer->update([
                'company' => $request->input('company'),
                'tax_id' => $request->input('tax_id'),
                'business_license' => $request->input('business_license'),
                'wholesale_application_notes' => $request->input('notes'),
                'wholesale_application_status' => 'pending',
                'wholesale_application_date' => now(),
            ]);

            // إرسال إشعار للإدارة (يمكن إضافته لاحقاً)
            // event(new WholesaleApplicationSubmitted($customer));

            return $this
                ->httpResponse()
                ->setData(new CustomerResource($customer))
                ->setMessage(__('Your wholesale application has been submitted successfully. We will review it and contact you soon.'))
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while submitting your application.'));
        }
    }

    /**
     * حالة طلب التحويل لعميل جملة
     *
     * @group Wholesale
     */
    public function getApplicationStatus()
    {
        try {
            $customer = Auth::guard('customer')->user();
            
            if (!$customer) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setCode(401)
                    ->setMessage(__('Unauthenticated'));
            }

            $status = [
                'is_wholesale' => (bool) $customer->is_wholesale,
                'application_status' => $customer->wholesale_application_status ?? 'not_applied',
                'application_date' => $customer->wholesale_application_date,
                'approval_date' => $customer->wholesale_approval_date,
                'notes' => $customer->wholesale_application_notes,
            ];

            return $this
                ->httpResponse()
                ->setData($status)
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching application status.'));
        }
    }

    /**
     * الحصول على أسعار الجملة للمنتجات
     *
     * @group Wholesale
     */
    public function getWholesalePrices(Request $request)
    {
        try {
            $customer = Auth::guard('customer')->user();
            
            if (!$customer || !$customer->is_wholesale) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setCode(403)
                    ->setMessage(__('Access denied. Wholesale customers only.'));
            }

            $productIds = $request->input('product_ids', []);
            
            if (empty($productIds)) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setCode(422)
                    ->setMessage(__('Product IDs are required.'));
            }

            $products = \Botble\Ecommerce\Models\Product::query()
                ->whereIn('id', $productIds)
                ->whereNotNull('wholesale_price')
                ->where('wholesale_price', '>', 0)
                ->select(['id', 'name', 'sku', 'price', 'wholesale_price'])
                ->get();

            $wholesalePrices = $products->map(function ($product) {
                return [
                    'product_id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'retail_price' => $product->price,
                    'wholesale_price' => $product->wholesale_price,
                    'savings' => $product->price - $product->wholesale_price,
                    'savings_percentage' => round((($product->price - $product->wholesale_price) / $product->price) * 100, 2),
                ];
            });

            return $this
                ->httpResponse()
                ->setData($wholesalePrices)
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching wholesale prices.'));
        }
    }

    /**
     * إحصائيات عميل الجملة
     *
     * @group Wholesale
     */
    public function getWholesaleStats()
    {
        try {
            $customer = Auth::guard('customer')->user();
            
            if (!$customer || !$customer->is_wholesale) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setCode(403)
                    ->setMessage(__('Access denied. Wholesale customers only.'));
            }

            // إحصائيات الطلبات
            $orders = $customer->orders();
            $totalOrders = $orders->count();
            $totalSpent = $orders->sum('amount');
            $averageOrderValue = $totalOrders > 0 ? $totalSpent / $totalOrders : 0;

            // إحصائيات الوفورات (تقديرية)
            $recentOrders = $orders->with('products')->take(10)->get();
            $totalSavings = 0;
            
            foreach ($recentOrders as $order) {
                foreach ($order->products as $product) {
                    if ($product->wholesale_price && $product->wholesale_price < $product->price) {
                        $savings = ($product->price - $product->wholesale_price) * $product->pivot->qty;
                        $totalSavings += $savings;
                    }
                }
            }

            $stats = [
                'customer_since' => $customer->created_at,
                'wholesale_since' => $customer->wholesale_approval_date,
                'total_orders' => $totalOrders,
                'total_spent' => $totalSpent,
                'total_spent_formatted' => format_price($totalSpent),
                'average_order_value' => $averageOrderValue,
                'average_order_value_formatted' => format_price($averageOrderValue),
                'estimated_total_savings' => $totalSavings,
                'estimated_total_savings_formatted' => format_price($totalSavings),
                'last_order_date' => $orders->latest()->first()?->created_at,
            ];

            return $this
                ->httpResponse()
                ->setData($stats)
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching wholesale statistics.'));
        }
    }

    /**
     * قائمة المنتجات مع أسعار الجملة فقط
     *
     * @group Wholesale
     */
    public function getWholesaleProducts(Request $request)
    {
        try {
            $customer = Auth::guard('customer')->user();
            
            if (!$customer || !$customer->is_wholesale) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setCode(403)
                    ->setMessage(__('Access denied. Wholesale customers only.'));
            }

            $query = \Botble\Ecommerce\Models\Product::query()
                ->whereNotNull('wholesale_price')
                ->where('wholesale_price', '>', 0)
                ->where('status', 'published');

            // البحث
            if ($request->has('search') && $request->input('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('sku', 'LIKE', "%{$search}%");
                });
            }

            // فلترة حسب الفئة
            if ($request->has('category_id') && $request->input('category_id')) {
                $query->whereHas('categories', function ($q) use ($request) {
                    $q->where('ec_product_categories.id', $request->input('category_id'));
                });
            }

            $perPage = $request->integer('per_page', 12);
            $perPage = min($perPage, 50);

            $products = $query->with(['categories', 'brand'])
                ->paginate($perPage);

            $productsData = $products->getCollection()->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'description' => $product->description,
                    'retail_price' => $product->price,
                    'wholesale_price' => $product->wholesale_price,
                    'savings' => $product->price - $product->wholesale_price,
                    'savings_percentage' => round((($product->price - $product->wholesale_price) / $product->price) * 100, 2),
                    'quantity' => $product->quantity,
                    'is_out_of_stock' => $product->isOutOfStock(),
                    'image_url' => $product->image ? \Botble\Media\Facades\RvMedia::getImageUrl($product->image, 'thumb') : null,
                    'categories' => $product->categories->pluck('name'),
                    'brand' => $product->brand?->name,
                ];
            });

            return $this
                ->httpResponse()
                ->setData([
                    'data' => $productsData,
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage(),
                    'per_page' => $products->perPage(),
                    'total' => $products->total(),
                ])
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching wholesale products.'));
        }
    }
}
