@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-slideshow me-2"></i>
                        إدارة السلايدر الرئيسي
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('revo-woocommerce.home-slider.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة سلايدر جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" role="alert">
                        <i class="ti ti-info-circle me-2"></i>
                        <strong>معلومة:</strong> السلايدر الرئيسي يظهر في أعلى الصفحة الرئيسية للتطبيق.
                    </div>

                    @if(count($sliders) > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="80">الترتيب</th>
                                        <th width="120">الصورة</th>
                                        <th>العنوان</th>
                                        <th>الرابط</th>
                                        <th width="100">الحالة</th>
                                        <th width="150">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sliders as $slider)
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary">{{ $slider['order'] }}</span>
                                            </td>
                                            <td>
                                                <img src="{{ $slider['image'] }}" alt="{{ $slider['title'] }}" 
                                                     class="img-thumbnail" style="width: 80px; height: 50px; object-fit: cover;">
                                            </td>
                                            <td>
                                                <strong>{{ $slider['title'] ?: 'بدون عنوان' }}</strong>
                                            </td>
                                            <td>
                                                @if($slider['link'])
                                                    <a href="{{ $slider['link'] }}" target="_blank" class="text-decoration-none">
                                                        <i class="ti ti-external-link me-1"></i>
                                                        عرض الرابط
                                                    </a>
                                                @else
                                                    <span class="text-muted">لا يوجد رابط</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($slider['status'])
                                                    <span class="badge bg-success">مفعل</span>
                                                @else
                                                    <span class="badge bg-danger">معطل</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('revo-woocommerce.home-slider.edit', $slider['id']) }}" 
                                                       class="btn btn-sm btn-warning">
                                                        <i class="ti ti-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" 
                                                            onclick="deleteSlider({{ $slider['id'] }})">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-slideshow" style="font-size: 4rem; color: #ccc;"></i>
                            <h5 class="mt-3">لا توجد سلايدرات</h5>
                            <p class="text-muted">ابدأ بإضافة أول سلايدر للصفحة الرئيسية</p>
                            <a href="{{ route('revo-woocommerce.home-slider.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-1"></i>
                                إضافة سلايدر جديد
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    function deleteSlider(id) {
        if (confirm('هل أنت متأكد من حذف هذا السلايدر؟')) {
            $.ajax({
                url: '/admin/revo-woocommerce/home-slider/' + id,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        location.reload();
                    }
                },
                error: function() {
                    Botble.showError('حدث خطأ أثناء حذف السلايدر');
                }
            });
        }
    }
</script>
@endpush
