import 'package:flutter/material.dart';

class CategoriesGridWidget extends StatelessWidget {
  const CategoriesGridWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final categories = _getCategories();
    
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          return _buildCategoryItem(context, categories[index]);
        },
      ),
    );
  }

  Widget _buildCategoryItem(BuildContext context, CategoryData category) {
    return GestureDetector(
      onTap: () => _onCategoryTap(context, category),
      child: Container(
        width: 90,
        margin: const EdgeInsets.only(left: 12),
        child: Column(
          children: [
            Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                color: category.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: category.color.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Icon(
                category.icon,
                color: category.color,
                size: 32,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              category.name,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  List<CategoryData> _getCategories() {
    return [
      CategoryData(
        name: 'محركات',
        icon: Icons.settings,
        color: const Color(0xFF1976D2),
      ),
      CategoryData(
        name: 'فرامل',
        icon: Icons.disc_full,
        color: const Color(0xFFE53935),
      ),
      CategoryData(
        name: 'إطارات',
        icon: Icons.tire_repair,
        color: const Color(0xFF43A047),
      ),
      CategoryData(
        name: 'بطاريات',
        icon: Icons.battery_full,
        color: const Color(0xFFFF9800),
      ),
      CategoryData(
        name: 'زيوت',
        icon: Icons.local_gas_station,
        color: const Color(0xFF8E24AA),
      ),
      CategoryData(
        name: 'فلاتر',
        icon: Icons.filter_alt,
        color: const Color(0xFF00ACC1),
      ),
    ];
  }

  void _onCategoryTap(BuildContext context, CategoryData category) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم اختيار فئة: ${category.name}'),
        backgroundColor: category.color,
      ),
    );
  }
}

class CategoryData {
  final String name;
  final IconData icon;
  final Color color;

  CategoryData({
    required this.name,
    required this.icon,
    required this.color,
  });
}
