import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/favorites_provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/product_card.dart';
import '../widgets/custom_button.dart';
import '../widgets/loading_widget.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'date';
  bool _ascending = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<FavoritesProvider>().loadFavorites();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<FavoritesProvider, ThemeProvider>(
      builder: (context, favoritesProvider, themeProvider, child) {
        final isDark = themeProvider.isDarkMode;
        
        return Scaffold(
          backgroundColor: isDark ? Colors.grey[900] : Colors.grey[50],
          appBar: AppBar(
            title: const Text('المفضلة'),
            backgroundColor: isDark ? Colors.grey[850] : Colors.white,
            foregroundColor: isDark ? Colors.white : Colors.black,
            actions: [
              if (favoritesProvider.isNotEmpty) ...[
                IconButton(
                  icon: const Icon(Icons.sort),
                  onPressed: () => _showSortOptions(favoritesProvider),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMenuAction(value, favoritesProvider),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'export',
                      child: Row(
                        children: [
                          Icon(Icons.share),
                          SizedBox(width: 8),
                          Text('مشاركة القائمة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'clear',
                      child: Row(
                        children: [
                          Icon(Icons.clear_all, color: Colors.red),
                          SizedBox(width: 8),
                          Text('مسح الكل'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
          body: favoritesProvider.isLoading
              ? const Center(child: LoadingWidget())
              : _buildBody(favoritesProvider, isDark),
        );
      },
    );
  }

  Widget _buildBody(FavoritesProvider provider, bool isDark) {
    if (provider.isEmpty) {
      return _buildEmptyState(isDark);
    }

    return Column(
      children: [
        // شريط البحث والإحصائيات
        _buildSearchAndStats(provider, isDark),
        
        // قائمة المفضلة
        Expanded(child: _buildFavoritesList(provider, isDark)),
      ],
    );
  }

  Widget _buildEmptyState(bool isDark) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 120,
            color: isDark ? Colors.grey[600] : Colors.grey[400],
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد منتجات مفضلة',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.grey[300] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'ابدأ بإضافة المنتجات التي تعجبك إلى المفضلة',
            style: TextStyle(
              fontSize: 16,
              color: isDark ? Colors.grey[400] : Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          CustomButton(
            text: 'تصفح المنتجات',
            onPressed: () => Navigator.pop(context),
            backgroundColor: Theme.of(context).primaryColor,
            icon: Icons.shopping_bag,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndStats(FavoritesProvider provider, bool isDark) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[850] : Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في المفضلة...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          
          const SizedBox(height: 16),
          
          // إحصائيات سريعة
          _buildQuickStats(provider, isDark),
        ],
      ),
    );
  }

  Widget _buildQuickStats(FavoritesProvider provider, bool isDark) {
    final stats = provider.getFavoritesStats();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            'المجموع',
            '${stats['totalCount']}',
            Icons.favorite,
            isDark,
          ),
          _buildStatItem(
            'متوفر',
            '${stats['inStockCount']}',
            Icons.check_circle,
            isDark,
            color: Colors.green,
          ),
          _buildStatItem(
            'متوسط السعر',
            '${stats['averagePrice'].toStringAsFixed(0)} ر.س',
            Icons.attach_money,
            isDark,
            color: Colors.blue,
          ),
          _buildStatItem(
            'التقييم',
            '${stats['averageRating'].toStringAsFixed(1)}',
            Icons.star,
            isDark,
            color: Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, bool isDark, {Color? color}) {
    return Column(
      children: [
        Icon(
          icon,
          color: color ?? (isDark ? Colors.grey[400] : Colors.grey[600]),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color ?? (isDark ? Colors.white : Colors.black),
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: isDark ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildFavoritesList(FavoritesProvider provider, bool isDark) {
    // تطبيق البحث والترتيب
    var favorites = _searchQuery.isEmpty
        ? provider.favorites
        : provider.searchFavorites(_searchQuery);

    if (favorites.isEmpty && _searchQuery.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: isDark ? Colors.grey[600] : Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج للبحث',
              style: TextStyle(
                fontSize: 16,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: favorites.length,
      itemBuilder: (context, index) {
        final favoriteItem = favorites[index];
        return _buildFavoriteCard(favoriteItem, provider, isDark);
      },
    );
  }

  Widget _buildFavoriteCard(FavoriteItem favoriteItem, FavoritesProvider provider, bool isDark) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // المنتج
          ProductCard(
            product: favoriteItem.product,
            onTap: () => _navigateToProduct(favoriteItem.product),
            showFavoriteButton: false,
          ),
          
          // معلومات إضافية
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  'أُضيف في ${_formatDate(favoriteItem.addedAt)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.favorite, color: Colors.red),
                  onPressed: () => _removeFavorite(favoriteItem, provider),
                  tooltip: 'إزالة من المفضلة',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSortOptions(FavoritesProvider provider) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ترتيب المفضلة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            _buildSortOption('التاريخ', 'date', provider),
            _buildSortOption('الاسم', 'name', provider),
            _buildSortOption('السعر', 'price', provider),
            _buildSortOption('التقييم', 'rating', provider),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Text('الترتيب: '),
                Radio<bool>(
                  value: true,
                  groupValue: _ascending,
                  onChanged: (value) {
                    setState(() {
                      _ascending = value ?? true;
                    });
                    provider.sortFavorites(_sortBy, ascending: _ascending);
                  },
                ),
                Text('تصاعدي'),
                Radio<bool>(
                  value: false,
                  groupValue: _ascending,
                  onChanged: (value) {
                    setState(() {
                      _ascending = value ?? false;
                    });
                    provider.sortFavorites(_sortBy, ascending: _ascending);
                  },
                ),
                Text('تنازلي'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOption(String label, String value, FavoritesProvider provider) {
    return RadioListTile<String>(
      title: Text(label),
      value: value,
      groupValue: _sortBy,
      onChanged: (newValue) {
        setState(() {
          _sortBy = newValue ?? 'date';
        });
        provider.sortFavorites(_sortBy, ascending: _ascending);
        Navigator.pop(context);
      },
    );
  }

  void _handleMenuAction(String action, FavoritesProvider provider) {
    switch (action) {
      case 'export':
        _exportFavorites(provider);
        break;
      case 'clear':
        _showClearDialog(provider);
        break;
    }
  }

  void _exportFavorites(FavoritesProvider provider) {
    final text = provider.exportFavoritesAsText();
    
    // يمكن استخدام share plugin لمشاركة النص
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('تم تصدير قائمة المفضلة'),
        action: SnackBarAction(
          label: 'عرض',
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('قائمة المفضلة'),
                content: SingleChildScrollView(
                  child: Text(text),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('إغلاق'),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _showClearDialog(FavoritesProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع المفضلة'),
        content: const Text('هل أنت متأكد من مسح جميع المنتجات المفضلة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              provider.clearFavorites();
              Navigator.pop(context);
            },
            child: const Text('مسح', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _removeFavorite(FavoriteItem favoriteItem, FavoritesProvider provider) {
    provider.removeFromFavorites(favoriteItem.product.id);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم حذف ${favoriteItem.product.name} من المفضلة'),
        action: SnackBarAction(
          label: 'تراجع',
          onPressed: () => provider.addToFavorites(favoriteItem.product),
        ),
      ),
    );
  }

  void _navigateToProduct(product) {
    Navigator.pushNamed(
      context,
      '/product-detail',
      arguments: product,
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
