<?php

namespace Botble\Faq\Http\Controllers\API;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Faq\Http\Resources\FaqResource;
use Botble\Faq\Models\Faq;
use Illuminate\Http\Request;

class FaqController extends BaseController
{
    /**
     * قائمة الأسئلة الشائعة
     *
     * @group FAQ
     */
    public function index(Request $request)
    {
        $query = Faq::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with('category');

        // فلترة حسب الفئة
        if ($request->has('category_id') && $request->input('category_id')) {
            $query->where('category_id', $request->input('category_id'));
        }

        // البحث في السؤال والجواب
        if ($request->has('search') && $request->input('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('question', 'LIKE', "%{$search}%")
                  ->orWhere('answer', 'LIKE', "%{$search}%");
            });
        }

        // ترتيب النتائج
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        
        if (in_array($sortBy, ['created_at', 'updated_at', 'question'])) {
            $query->orderBy($sortBy, $sortOrder === 'asc' ? 'asc' : 'desc');
        }

        $perPage = $request->integer('per_page', 10);
        $perPage = min($perPage, 50); // حد أقصى 50 عنصر

        $faqs = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData(FaqResource::collection($faqs))
            ->toApiResponse();
    }

    /**
     * عرض سؤال شائع محدد
     *
     * @group FAQ
     */
    public function show($id)
    {
        $faq = Faq::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with('category')
            ->findOrFail($id);

        return $this
            ->httpResponse()
            ->setData(new FaqResource($faq))
            ->toApiResponse();
    }

    /**
     * البحث في الأسئلة الشائعة
     *
     * @group FAQ
     */
    public function search(Request $request)
    {
        $request->validate([
            'q' => 'required|string|min:2|max:255',
            'category_id' => 'nullable|exists:faq_categories,id',
            'per_page' => 'nullable|integer|min:1|max:50'
        ]);

        $query = Faq::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with('category');

        $searchTerm = $request->input('q');
        
        // البحث في السؤال والجواب
        $query->where(function ($q) use ($searchTerm) {
            $q->where('question', 'LIKE', "%{$searchTerm}%")
              ->orWhere('answer', 'LIKE', "%{$searchTerm}%");
        });

        // فلترة حسب الفئة إذا تم تحديدها
        if ($request->has('category_id') && $request->input('category_id')) {
            $query->where('category_id', $request->input('category_id'));
        }

        // ترتيب النتائج حسب الصلة (الأسئلة التي تحتوي على المصطلح في العنوان أولاً)
        $query->orderByRaw("CASE WHEN question LIKE '%{$searchTerm}%' THEN 1 ELSE 2 END")
              ->orderBy('created_at', 'desc');

        $perPage = $request->integer('per_page', 10);
        $faqs = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData([
                'search_term' => $searchTerm,
                'total_results' => $faqs->total(),
                'faqs' => FaqResource::collection($faqs)
            ])
            ->toApiResponse();
    }
}
