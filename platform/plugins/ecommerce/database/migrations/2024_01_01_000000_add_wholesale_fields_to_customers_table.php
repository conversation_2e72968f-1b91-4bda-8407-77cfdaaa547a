<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ec_customers', function (Blueprint $table) {
            if (!Schema::hasColumn('ec_customers', 'company')) {
                $table->string('company')->nullable()->after('name');
            }
            
            if (!Schema::hasColumn('ec_customers', 'tax_id')) {
                $table->string('tax_id', 50)->nullable()->after('company');
            }
            
            if (!Schema::hasColumn('ec_customers', 'business_license')) {
                $table->string('business_license', 100)->nullable()->after('tax_id');
            }
            
            if (!Schema::hasColumn('ec_customers', 'wholesale_application_status')) {
                $table->enum('wholesale_application_status', ['not_applied', 'pending', 'approved', 'rejected'])
                      ->default('not_applied')
                      ->after('is_wholesale');
            }
            
            if (!Schema::hasColumn('ec_customers', 'wholesale_application_date')) {
                $table->timestamp('wholesale_application_date')->nullable()->after('wholesale_application_status');
            }
            
            if (!Schema::hasColumn('ec_customers', 'wholesale_approval_date')) {
                $table->timestamp('wholesale_approval_date')->nullable()->after('wholesale_application_date');
            }
            
            if (!Schema::hasColumn('ec_customers', 'wholesale_application_notes')) {
                $table->text('wholesale_application_notes')->nullable()->after('wholesale_approval_date');
            }
            
            if (!Schema::hasColumn('ec_customers', 'business_type')) {
                $table->string('business_type', 100)->nullable()->after('wholesale_application_notes');
            }
            
            if (!Schema::hasColumn('ec_customers', 'years_in_business')) {
                $table->integer('years_in_business')->nullable()->after('business_type');
            }
            
            if (!Schema::hasColumn('ec_customers', 'expected_monthly_volume')) {
                $table->decimal('expected_monthly_volume', 15, 2)->nullable()->after('years_in_business');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ec_customers', function (Blueprint $table) {
            $columns = [
                'company',
                'tax_id',
                'business_license',
                'wholesale_application_status',
                'wholesale_application_date',
                'wholesale_approval_date',
                'wholesale_application_notes',
                'business_type',
                'years_in_business',
                'expected_monthly_volume',
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('ec_customers', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
