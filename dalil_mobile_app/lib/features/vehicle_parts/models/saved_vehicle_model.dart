import 'vehicle_category_model.dart';

class SavedVehicleModel {
  final int id;
  final String name;
  final VehicleCategoryModel vehicleType;
  final VehicleCategoryModel make;
  final VehicleCategoryModel model;
  final VehicleCategoryModel year;
  final String? vin;
  final String? notes;
  final DateTime createdAt;

  SavedVehicleModel({
    required this.id,
    required this.name,
    required this.vehicleType,
    required this.make,
    required this.model,
    required this.year,
    this.vin,
    this.notes,
    required this.createdAt,
  });

  factory SavedVehicleModel.fromJson(Map<String, dynamic> json) {
    return SavedVehicleModel(
      id: json['id'] as int,
      name: json['name'] as String,
      vehicleType: VehicleCategoryModel.fromJson(json['vehicle_type'] as Map<String, dynamic>),
      make: VehicleCategoryModel.fromJson(json['make'] as Map<String, dynamic>),
      model: VehicleCategoryModel.fromJson(json['model'] as Map<String, dynamic>),
      year: VehicleCategoryModel.fromJson(json['year'] as Map<String, dynamic>),
      vin: json['vin'] as String?,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'vehicle_type': vehicleType.toJson(),
      'make': make.toJson(),
      'model': model.toJson(),
      'year': year.toJson(),
      'vin': vin,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // الحصول على الاسم الكامل
  String get fullName {
    return '${vehicleType.name} ${make.name} ${model.name} ${year.name}';
  }

  // الحصول على الاسم المختصر
  String get shortName {
    return '${make.name} ${model.name} ${year.name}';
  }

  // الحصول على الوصف
  String get description {
    String desc = fullName;
    if (vin != null && vin!.isNotEmpty) {
      desc += ' (VIN: $vin)';
    }
    return desc;
  }

  SavedVehicleModel copyWith({
    int? id,
    String? name,
    VehicleCategoryModel? vehicleType,
    VehicleCategoryModel? make,
    VehicleCategoryModel? model,
    VehicleCategoryModel? year,
    String? vin,
    String? notes,
    DateTime? createdAt,
  }) {
    return SavedVehicleModel(
      id: id ?? this.id,
      name: name ?? this.name,
      vehicleType: vehicleType ?? this.vehicleType,
      make: make ?? this.make,
      model: model ?? this.model,
      year: year ?? this.year,
      vin: vin ?? this.vin,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'SavedVehicleModel(id: $id, name: $name, fullName: $fullName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SavedVehicleModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
