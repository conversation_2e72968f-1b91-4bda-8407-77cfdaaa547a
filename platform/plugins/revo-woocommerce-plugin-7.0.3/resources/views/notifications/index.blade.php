@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-bell me-2"></i>
                        إدارة الإشعارات
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('revo-woocommerce.notifications.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إرسال إشعار جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="ti ti-brand-firebase me-2"></i>
                        <strong>تنبيه:</strong> تأكد من تكوين Firebase Server Key في الإعدادات لإرسال الإشعارات.
                    </div>

                    @if(count($notifications) > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>العنوان</th>
                                        <th>النوع</th>
                                        <th>الجمهور</th>
                                        <th>تم الإرسال</th>
                                        <th>تم الفتح</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($notifications as $notification)
                                        <tr>
                                            <td>
                                                <strong>{{ $notification['title'] }}</strong>
                                                <br>
                                                <small class="text-muted">{{ Str::limit($notification['message'], 50) }}</small>
                                            </td>
                                            <td>
                                                @php
                                                    $typeColors = [
                                                        'general' => 'primary',
                                                        'order' => 'success',
                                                        'promotion' => 'warning',
                                                        'system' => 'info'
                                                    ];
                                                    $typeNames = [
                                                        'general' => 'عام',
                                                        'order' => 'طلب',
                                                        'promotion' => 'ترويجي',
                                                        'system' => 'نظام'
                                                    ];
                                                @endphp
                                                <span class="badge bg-{{ $typeColors[$notification['type']] ?? 'secondary' }}">
                                                    {{ $typeNames[$notification['type']] ?? $notification['type'] }}
                                                </span>
                                            </td>
                                            <td>
                                                @php
                                                    $audienceNames = [
                                                        'all' => 'الجميع',
                                                        'customers' => 'العملاء',
                                                        'specific' => 'محدد'
                                                    ];
                                                @endphp
                                                {{ $audienceNames[$notification['target_audience']] ?? $notification['target_audience'] }}
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ number_format($notification['sent_count']) }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">{{ number_format($notification['opened_count']) }}</span>
                                                @if($notification['sent_count'] > 0)
                                                    <small class="text-muted d-block">
                                                        ({{ round(($notification['opened_count'] / $notification['sent_count']) * 100, 1) }}%)
                                                    </small>
                                                @endif
                                            </td>
                                            <td>
                                                @php
                                                    $statusColors = [
                                                        'sent' => 'success',
                                                        'scheduled' => 'warning',
                                                        'failed' => 'danger'
                                                    ];
                                                    $statusNames = [
                                                        'sent' => 'تم الإرسال',
                                                        'scheduled' => 'مجدول',
                                                        'failed' => 'فشل'
                                                    ];
                                                @endphp
                                                <span class="badge bg-{{ $statusColors[$notification['status']] ?? 'secondary' }}">
                                                    {{ $statusNames[$notification['status']] ?? $notification['status'] }}
                                                </span>
                                            </td>
                                            <td>
                                                <small>{{ date('Y-m-d H:i', strtotime($notification['created_at'])) }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('revo-woocommerce.notifications.show', $notification['id']) }}" 
                                                       class="btn btn-sm btn-info">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                    @if($notification['status'] === 'sent')
                                                        <button type="button" class="btn btn-sm btn-warning" 
                                                                onclick="resendNotification({{ $notification['id'] }})">
                                                            <i class="ti ti-refresh"></i>
                                                        </button>
                                                    @endif
                                                    <button type="button" class="btn btn-sm btn-danger" 
                                                            onclick="deleteNotification({{ $notification['id'] }})">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-bell" style="font-size: 4rem; color: #ccc;"></i>
                            <h5 class="mt-3">لا توجد إشعارات</h5>
                            <p class="text-muted">ابدأ بإرسال أول إشعار للمستخدمين</p>
                            <a href="{{ route('revo-woocommerce.notifications.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-1"></i>
                                إرسال إشعار جديد
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    function deleteNotification(id) {
        if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
            $.ajax({
                url: '/admin/revo-woocommerce/notifications/' + id,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        location.reload();
                    }
                },
                error: function() {
                    Botble.showError('حدث خطأ أثناء حذف الإشعار');
                }
            });
        }
    }

    function resendNotification(id) {
        if (confirm('هل تريد إعادة إرسال هذا الإشعار؟')) {
            $.ajax({
                url: '/admin/revo-woocommerce/notifications/' + id + '/resend',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        location.reload();
                    }
                },
                error: function() {
                    Botble.showError('حدث خطأ أثناء إعادة إرسال الإشعار');
                }
            });
        }
    }
</script>
@endpush
