<?php

use Bo<PERSON>ble\Ecommerce\Http\Controllers\API\AddressController;
use Botble\Ecommerce\Http\Controllers\API\BrandController;
use Botble\Ecommerce\Http\Controllers\API\CartController;
use Botble\Ecommerce\Http\Controllers\API\CheckoutController;
use Botble\Ecommerce\Http\Controllers\API\CompareController;
use Botble\Ecommerce\Http\Controllers\API\CouponController;
use Botble\Ecommerce\Http\Controllers\API\CountryController;
use Botble\Ecommerce\Http\Controllers\API\CurrencyController;
use Botble\Ecommerce\Http\Controllers\API\DownloadController;
use Botble\Ecommerce\Http\Controllers\API\FilterController;
use Botble\Ecommerce\Http\Controllers\API\FlashSaleController;
use Botble\Ecommerce\Http\Controllers\API\OrderController;
use Botble\Ecommerce\Http\Controllers\API\OrderReturnController;
use Botble\Ecommerce\Http\Controllers\API\OrderTrackingController;
use Botble\Ecommerce\Http\Controllers\API\ProductCategoryController;
use Botble\Ecommerce\Http\Controllers\API\ProductController;
use Botble\Ecommerce\Http\Controllers\API\ReviewController;
use Botble\Ecommerce\Http\Controllers\API\TaxController;
use Botble\Ecommerce\Http\Controllers\API\WholesaleController;
use Botble\Ecommerce\Http\Controllers\API\WishlistController;
use Botble\Ecommerce\Http\Middleware\ApiCurrencyMiddleware;
use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => ['api', ApiCurrencyMiddleware::class, 'api.language.ecommerce'],
    'prefix' => 'api/v1/ecommerce/',
    'namespace' => 'Botble\Ecommerce\Http\Controllers\API',
], function (): void {
    // Public routes that use token-based security
    Route::get('download/{token}/{filename}', [DownloadController::class, 'downloadFile'])->name('api.ecommerce.download.download-file');
    Route::get('orders/download-proof/{token}/{filename}', [OrderController::class, 'downloadProofFile'])->name('api.ecommerce.orders.download-proof-file');
    Route::get('products', [ProductController::class, 'index']);
    Route::get('products/{slug}', [ProductController::class, 'show']);
    Route::get('products/{slug}/related', [ProductController::class, 'relatedProducts']);
    Route::get('products/{slug}/cross-sale', [ProductController::class, 'getCrossSaleProducts']);
    Route::get('products/{slug}/reviews', [ProductController::class, 'reviews']);
    Route::get('product-variation/{id}', [ProductController::class, 'getProductVariation'])->wherePrimaryKey();

    Route::get('product-categories', [ProductCategoryController::class, 'index']);
    Route::get('product-categories/{slug}', [ProductCategoryController::class, 'show']);
    Route::get('product-categories/{id}/products', [ProductCategoryController::class, 'products']);

    Route::get('brands', [BrandController::class, 'index']);
    Route::get('brands/{slug}', [BrandController::class, 'show']);
    Route::get('brands/{id}/products', [BrandController::class, 'products']);

    Route::get('filters', [FilterController::class, 'getFilters']);

    Route::get('flash-sales', [FlashSaleController::class, 'index']);

    Route::group(['middleware' => ['auth:sanctum']], function (): void {
        Route::get('orders', [OrderController::class, 'index']);
        Route::get('orders/{id}', [OrderController::class, 'show']);
        Route::post('orders/{id}/cancel', [OrderController::class, 'cancel']);
        Route::get('orders/{id}/invoice', [OrderController::class, 'getInvoice']);
        Route::post('orders/{id}/upload-proof', [OrderController::class, 'uploadProof']);
        Route::get('orders/{id}/download-proof', [OrderController::class, 'downloadProof']);
        Route::post('orders/{id}/confirm-delivery', [OrderController::class, 'confirmDelivery']);
        Route::get('addresses', [AddressController::class, 'index']);
        Route::post('addresses', [AddressController::class, 'store']);
        Route::put('addresses/{id}', [AddressController::class, 'update']);
        Route::delete('addresses/{id}', [AddressController::class, 'destroy']);
        Route::get('reviews', [ReviewController::class, 'index']);
        Route::post('reviews', [ReviewController::class, 'store']);
        Route::delete('reviews/{id}', [ReviewController::class, 'destroy'])->wherePrimaryKey();
        Route::get('order-returns', [OrderReturnController::class, 'index']);
        Route::get('order-returns/{id}', [OrderReturnController::class, 'show'])->wherePrimaryKey();
        Route::post('order-returns', [OrderReturnController::class, 'store']);
        Route::get('orders/{order_id}/returns', [OrderReturnController::class, 'getReturnOrder'])->wherePrimaryKey('order_id');

        Route::get('downloads', [DownloadController::class, 'index']);
        Route::get('downloads/{id}', [DownloadController::class, 'download'])->wherePrimaryKey();

        // Wholesale APIs
        Route::group(['prefix' => 'wholesale'], function (): void {
            Route::post('apply', [WholesaleController::class, 'applyForWholesale']);
            Route::get('application-status', [WholesaleController::class, 'getApplicationStatus']);
            Route::get('prices', [WholesaleController::class, 'getWholesalePrices']);
            Route::get('stats', [WholesaleController::class, 'getWholesaleStats']);
            Route::get('products', [WholesaleController::class, 'getWholesaleProducts']);
        });
    });

    Route::post('cart', [CartController::class, 'store']);
    Route::post('cart/{id}', [CartController::class, 'store']);
    Route::put('cart/{id}', [CartController::class, 'update']);
    Route::delete('cart/{id}', [CartController::class, 'destroy']);
    Route::get('cart/{id}', [CartController::class, 'index']);

    Route::post('cart/refresh', [CartController::class, 'refresh']);

    Route::post('coupon/apply', [CouponController::class, 'apply']);
    Route::post('coupon/remove', [CouponController::class, 'remove']);
    Route::get('countries', [CountryController::class, 'index']);
    Route::get('currencies', [CurrencyController::class, 'index']);
    Route::get('currencies/current', [CurrencyController::class, 'getCurrentCurrency']);

    Route::post('checkout/taxes/calculate', TaxController::class);

    Route::group(['middleware' => ['web', 'core']], function (): void {
        Route::get('checkout/cart/{id}', [CheckoutController::class, 'process']);
    });

    Route::post('orders/tracking', OrderTrackingController::class);

    Route::post('wishlist', [WishlistController::class, 'store']);
    Route::post('wishlist/{id}', [WishlistController::class, 'store']);
    Route::delete('wishlist/{id}', [WishlistController::class, 'destroy']);
    Route::get('wishlist/{id}', [WishlistController::class, 'index']);

    Route::post('compare', [CompareController::class, 'store']);
    Route::post('compare/{id}', [CompareController::class, 'store']);
    Route::delete('compare/{id}', [CompareController::class, 'destroy']);
    Route::get('compare/{id}', [CompareController::class, 'index']);

    // Car API Routes
    Route::get('car-models', 'CarController@getModels')->name('api.car-models');
    Route::get('car-years', 'CarController@getYears')->name('api.car-years');
});
