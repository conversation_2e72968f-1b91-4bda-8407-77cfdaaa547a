// Enhanced PWA functionality for Dalilak Auto Theme 2
class DalilakautoEnhancedPWA {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isOnline = navigator.onLine;
        this.init();
    }

    init() {
        this.registerServiceWorker();
        this.setupInstallPrompt();
        this.setupNetworkStatus();
        this.setupUpdateHandler();
        this.createUI();
    }

    // Register Service Worker
    registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/service-worker-copy.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                        this.checkForUpdates(registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    }

    // Setup Install Prompt
    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallBanner();
        });

        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            this.isInstalled = true;
            this.hideInstallBanner();
        });
    }

    // Setup Network Status
    setupNetworkStatus() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.updateNetworkStatus();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.updateNetworkStatus();
        });

        this.updateNetworkStatus();
    }

    // Check for Service Worker Updates
    checkForUpdates(registration) {
        registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    this.showUpdateBanner();
                }
            });
        });
    }

    // Setup Update Handler
    setupUpdateHandler() {
        navigator.serviceWorker.addEventListener('controllerchange', () => {
            window.location.reload();
        });
    }

    // Create UI Elements
    createUI() {
        this.createInstallBanner();
        this.createNetworkStatus();
        this.createUpdateBanner();
        this.createLoadingSpinner();
    }

    // Create Install Banner
    createInstallBanner() {
        const banner = document.createElement('div');
        banner.className = 'pwa-install-banner';
        banner.id = 'pwa-install-banner';
        banner.innerHTML = `
            <div class="banner-content">
                <div class="banner-text">
                    <div class="banner-title">🚗 تثبيت دليلك أوتو المطور</div>
                    <div class="banner-description">احصل على تجربة أفضل مع التطبيق المثبت</div>
                </div>
                <div class="banner-actions">
                    <button class="btn btn-install" onclick="pwaApp.installApp()">تثبيت</button>
                    <button class="btn btn-dismiss" onclick="pwaApp.hideInstallBanner()">إلغاء</button>
                </div>
            </div>
        `;
        document.body.appendChild(banner);
    }

    // Create Network Status
    createNetworkStatus() {
        const status = document.createElement('div');
        status.className = 'pwa-status';
        status.id = 'pwa-status';
        document.body.appendChild(status);
    }

    // Create Update Banner
    createUpdateBanner() {
        const banner = document.createElement('div');
        banner.className = 'pwa-update-banner';
        banner.id = 'pwa-update-banner';
        banner.innerHTML = `
            <div class="update-text">
                🔄 تحديث جديد متاح للتطبيق
                <button class="btn-update" onclick="pwaApp.updateApp()">تحديث الآن</button>
            </div>
        `;
        document.body.appendChild(banner);
    }

    // Create Loading Spinner
    createLoadingSpinner() {
        const loading = document.createElement('div');
        loading.className = 'pwa-loading';
        loading.id = 'pwa-loading';
        loading.innerHTML = '<div class="pwa-spinner"></div>';
        document.body.appendChild(loading);
    }

    // Show Install Banner
    showInstallBanner() {
        const banner = document.getElementById('pwa-install-banner');
        if (banner && !this.isInstalled) {
            banner.style.display = 'block';
        }
    }

    // Hide Install Banner
    hideInstallBanner() {
        const banner = document.getElementById('pwa-install-banner');
        if (banner) {
            banner.style.display = 'none';
        }
    }

    // Install App
    async installApp() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            console.log(`User response to the install prompt: ${outcome}`);
            this.deferredPrompt = null;
            this.hideInstallBanner();
        }
    }

    // Update Network Status
    updateNetworkStatus() {
        const status = document.getElementById('pwa-status');
        if (status) {
            status.className = `pwa-status ${this.isOnline ? 'online' : 'offline'}`;
            status.textContent = this.isOnline ? '🟢 متصل' : '🔴 غير متصل';
            status.style.display = 'block';
            
            // Hide after 3 seconds if online
            if (this.isOnline) {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
            }
        }
    }

    // Show Update Banner
    showUpdateBanner() {
        const banner = document.getElementById('pwa-update-banner');
        if (banner) {
            banner.style.display = 'block';
        }
    }

    // Update App
    updateApp() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistration().then(registration => {
                if (registration && registration.waiting) {
                    registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                }
            });
        }
    }

    // Show Loading
    showLoading() {
        const loading = document.getElementById('pwa-loading');
        if (loading) {
            loading.style.display = 'block';
        }
    }

    // Hide Loading
    hideLoading() {
        const loading = document.getElementById('pwa-loading');
        if (loading) {
            loading.style.display = 'none';
        }
    }
}

// Initialize PWA when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Load PWA styles
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '/pwa-copy/pwa-styles.css';
    document.head.appendChild(link);

    // Initialize PWA
    window.pwaApp = new DalilakautoEnhancedPWA();
});

// Handle service worker messages
navigator.serviceWorker.addEventListener('message', event => {
    if (event.data && event.data.type === 'RELOAD_PAGE') {
        window.location.reload();
    }
});
