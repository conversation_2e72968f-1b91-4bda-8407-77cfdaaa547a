<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Requests;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Rules\EmailRule;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Botble\Ecommerce\Models\Customer;
use Bo<PERSON>ble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class RegisterRequest extends Request
{
    public function rules(): array
    {
        $rules = [
            'name' => ['required', 'max:120', 'min:2'],
            'email' => [
                'nullable',
                Rule::requiredIf(! EcommerceHelper::isLoginUsingPhone()),
                new EmailRule(),
                Rule::unique((new Customer())->getTable()),
            ],
            'phone' => [
                'nullable',
                Rule::requiredIf(EcommerceHelper::isLoginUsingPhone() || get_ecommerce_setting('make_customer_phone_number_required', false)),
                ...explode('|', BaseHelper::getPhoneValidationRule()),
                Rule::unique((new Customer())->getTable(), 'phone'),
            ],
            'password' => ['required', 'min:6', 'confirmed'],
            'agree_terms_and_policy' => ['sometimes', 'accepted:1'],
            'request_wholesale' => ['sometimes', 'boolean'],
            // حقول العنوان لعملاء الجملة
            'address' => [
                'nullable',
                Rule::requiredIf(function () {
                    return $this->boolean('request_wholesale');
                }),
                'string',
                'max:255'
            ],
            'city' => [
                'nullable',
                Rule::requiredIf(function () {
                    return $this->boolean('request_wholesale');
                }),
                'string',
                'max:120'
            ],
            'state' => [
                'nullable',
                Rule::requiredIf(function () {
                    return $this->boolean('request_wholesale');
                }),
                'string',
                'max:120'
            ],
            'zip_code' => [
                'nullable',
                'string',
                'max:20'
            ],
        ];

        return apply_filters('ecommerce_customer_registration_form_validation_rules', $rules);
    }

    public function attributes(): array
    {
        return apply_filters('ecommerce_customer_registration_form_validation_attributes', [
            'name' => __('Name'),
            'email' => __('Email'),
            'password' => __('Password'),
            'phone' => __('Phone'),
            'agree_terms_and_policy' => __('Term and Policy'),
            'request_wholesale' => 'طلب التسجيل كعميل جملة',
            'address' => 'العنوان التفصيلي',
            'city' => 'المدينة',
            'state' => 'المنطقة/المحافظة',
            'zip_code' => 'الرمز البريدي',
        ]);
    }

    public function messages(): array
    {
        return apply_filters('ecommerce_customer_registration_form_validation_messages', []);
    }
}
