<?php

return [
    'name' => 'RevoWOO - Flutter App Manager',
    'description' => 'Mobile App Management for Flutter WooCommerce applications',
    
    // General
    'settings' => 'Settings',
    'settings_description' => 'Configure app settings, logo, contact information and more',
    'manage_settings' => 'Manage Settings',
    'settings_saved' => 'Settings saved successfully!',
    
    // Intro Pages
    'intro_pages' => 'Intro Pages',
    'intro_pages_description' => 'Manage app introduction pages shown to new users',
    'manage_intro_pages' => 'Manage Intro Pages',
    
    // Home Slider
    'home_slider' => 'Home Slider',
    'home_slider_description' => 'Manage main slider images on home screen',
    'manage_slider' => 'Manage Slider',
    
    // Categories
    'categories' => 'Categories',
    'categories_description' => 'Manage custom categories display order',
    'manage_categories' => 'Manage Categories',
    
    // Mini Banner
    'mini_banner' => 'Mini Banner',
    'mini_banner_description' => 'Manage small promotional banners',
    'manage_mini_banner' => 'Manage Mini Banner',
    
    // Flash Sale
    'flash_sale' => 'Flash Sale',
    'flash_sale_description' => 'Configure flash sale products and timing',
    'manage_flash_sale' => 'Manage Flash Sale',
    
    // Notifications
    'notifications' => 'Push Notifications',
    'notifications_description' => 'Send push notifications to app users',
    'manage_notifications' => 'Manage Notifications',
    
    // API
    'api_settings' => 'API Settings',
    'api_enabled' => 'API Enabled',
    'api_version' => 'API Version',
    
    // Firebase
    'firebase_settings' => 'Firebase Settings',
    'firebase_server_key' => 'Firebase Server Key',
    'firebase_enabled' => 'Firebase Enabled',
    
    // App Settings
    'app_settings' => 'App Settings',
    'app_title' => 'App Title',
    'app_logo' => 'App Logo',
    'contact_info' => 'Contact Information',
    'about_url' => 'About URL',
    'customer_support_url' => 'Customer Support URL',
    'splash_screen' => 'Splash Screen',
    
    // Messages
    'plugin_activated' => 'RevoWOO plugin activated successfully!',
    'plugin_deactivated' => 'RevoWOO plugin deactivated successfully!',
    'requires_ecommerce' => 'This plugin requires the Ecommerce plugin to be activated.',
];
