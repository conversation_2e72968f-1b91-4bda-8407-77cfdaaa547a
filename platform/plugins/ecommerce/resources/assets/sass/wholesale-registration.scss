// تنسيقات خاصة بتسجيل عملاء الجملة
#wholesale-address-fields {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
    transition: all 0.3s ease;

    h5 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #dee2e6;
        
        &:before {
            content: "📍";
            margin-left: 8px;
        }
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-control {
        border: 1px solid #ced4da;
        border-radius: 6px;
        padding: 12px 15px;
        font-size: 14px;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

        &:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        &.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
    }

    .auth-input-icon {
        color: #6c757d;
    }

    // تنسيق خاص للحقول المطلوبة
    .form-control[required] {
        border-left: 3px solid #28a745;
    }

    // تنسيق للحقول الاختيارية
    .form-control:not([required]) {
        border-left: 3px solid #6c757d;
    }
}

// تحسين مظهر checkbox عميل الجملة
#request_wholesale {
    transform: scale(1.2);
    margin-left: 8px;
}

// تنسيق للرسائل التوضيحية
.form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

// تحسين الانتقالات
.auth-card {
    .form-group {
        transition: all 0.3s ease;
    }
}

// تنسيق خاص للشاشات الصغيرة
@media (max-width: 768px) {
    #wholesale-address-fields {
        padding: 15px;
        margin-top: 10px;

        h5 {
            font-size: 16px;
            margin-bottom: 15px;
        }

        .form-control {
            padding: 10px 12px;
            font-size: 13px;
        }
    }
}

// تحسين مظهر الأيقونات
.auth-input-icon {
    i, svg {
        width: 18px;
        height: 18px;
    }
}

// تنسيق خاص لحالة التحميل
.loading {
    opacity: 0.7;
    pointer-events: none;
}

// تحسين مظهر رسائل الخطأ
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
    font-weight: 500;
}

// تنسيق خاص للحقول النشطة
.form-control:focus + .auth-input-icon {
    color: #007bff;
}

// تحسين التباعد
.form-group:last-child {
    margin-bottom: 0;
}
