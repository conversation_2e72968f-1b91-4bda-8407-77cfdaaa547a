import 'package:flutter/material.dart';
import '../models/product.dart';

class SearchFiltersWidget extends StatefulWidget {
  final List<Category> categories;
  final List<Brand> brands;
  final List<int> selectedCategoryIds;
  final List<int> selectedBrandIds;
  final double? minPrice;
  final double? maxPrice;
  final String sortBy;
  final Function(List<int>) onCategoriesChanged;
  final Function(List<int>) onBrandsChanged;
  final Function(double?, double?) onPriceRangeChanged;
  final Function(String) onSortChanged;
  final VoidCallback onClearFilters;

  const SearchFiltersWidget({
    super.key,
    required this.categories,
    required this.brands,
    required this.selectedCategoryIds,
    required this.selectedBrandIds,
    required this.minPrice,
    required this.maxPrice,
    required this.sortBy,
    required this.onCategoriesChanged,
    required this.onBrandsChanged,
    required this.onPriceRangeChanged,
    required this.onSortChanged,
    required this.onClearFilters,
  });

  @override
  State<SearchFiltersWidget> createState() => _SearchFiltersWidgetState();
}

class _SearchFiltersWidgetState extends State<SearchFiltersWidget> {
  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    _minPriceController.text = widget.minPrice?.toString() ?? '';
    _maxPriceController.text = widget.maxPrice?.toString() ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الفلاتر
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'فلاتر البحث',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: widget.onClearFilters,
                    child: const Text('مسح الكل'),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // فلتر الفئات
              _buildCategoriesFilter(),
              
              const SizedBox(height: 20),
              
              // فلتر العلامات التجارية
              _buildBrandsFilter(),
              
              const SizedBox(height: 20),
              
              // فلتر نطاق السعر
              _buildPriceRangeFilter(),
              
              const SizedBox(height: 20),
              
              // فلتر الترتيب
              _buildSortFilter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الفئات',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: widget.categories.map((category) {
            final isSelected = widget.selectedCategoryIds.contains(category.id);
            return FilterChip(
              label: Text(category.name),
              selected: isSelected,
              onSelected: (selected) {
                final newSelectedIds = List<int>.from(widget.selectedCategoryIds);
                if (selected) {
                  newSelectedIds.add(category.id);
                } else {
                  newSelectedIds.remove(category.id);
                }
                widget.onCategoriesChanged(newSelectedIds);
              },
              selectedColor: const Color(0xFF1976D2).withOpacity(0.2),
              checkmarkColor: const Color(0xFF1976D2),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildBrandsFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'العلامات التجارية',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          constraints: const BoxConstraints(maxHeight: 120),
          child: SingleChildScrollView(
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: widget.brands.map((brand) {
                final isSelected = widget.selectedBrandIds.contains(brand.id);
                return FilterChip(
                  label: Text(brand.name),
                  selected: isSelected,
                  onSelected: (selected) {
                    final newSelectedIds = List<int>.from(widget.selectedBrandIds);
                    if (selected) {
                      newSelectedIds.add(brand.id);
                    } else {
                      newSelectedIds.remove(brand.id);
                    }
                    widget.onBrandsChanged(newSelectedIds);
                  },
                  selectedColor: const Color(0xFF1976D2).withOpacity(0.2),
                  checkmarkColor: const Color(0xFF1976D2),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPriceRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نطاق السعر (د.ع)',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _minPriceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'من',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) {
                  _updatePriceRange();
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextField(
                controller: _maxPriceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'إلى',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) {
                  _updatePriceRange();
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // أزرار نطاقات سعر سريعة
        Wrap(
          spacing: 8,
          children: [
            _buildQuickPriceButton('أقل من 50,000', null, 50000),
            _buildQuickPriceButton('50,000 - 100,000', 50000, 100000),
            _buildQuickPriceButton('100,000 - 200,000', 100000, 200000),
            _buildQuickPriceButton('أكثر من 200,000', 200000, null),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickPriceButton(String label, double? min, double? max) {
    final isSelected = widget.minPrice == min && widget.maxPrice == max;
    
    return OutlinedButton(
      onPressed: () {
        setState(() {
          _minPriceController.text = min?.toString() ?? '';
          _maxPriceController.text = max?.toString() ?? '';
        });
        widget.onPriceRangeChanged(min, max);
      },
      style: OutlinedButton.styleFrom(
        backgroundColor: isSelected ? const Color(0xFF1976D2).withOpacity(0.1) : null,
        side: BorderSide(
          color: isSelected ? const Color(0xFF1976D2) : Colors.grey[300]!,
        ),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          color: isSelected ? const Color(0xFF1976D2) : null,
        ),
      ),
    );
  }

  Widget _buildSortFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ترتيب النتائج',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildSortOption('relevance', 'الأكثر صلة'),
            _buildSortOption('price_low_high', 'السعر: الأقل أولاً'),
            _buildSortOption('price_high_low', 'السعر: الأعلى أولاً'),
            _buildSortOption('name_a_z', 'الاسم: أ-ي'),
            _buildSortOption('rating', 'الأعلى تقييماً'),
            _buildSortOption('newest', 'الأحدث'),
          ],
        ),
      ],
    );
  }

  Widget _buildSortOption(String value, String label) {
    final isSelected = widget.sortBy == value;
    
    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          widget.onSortChanged(value);
        }
      },
      selectedColor: const Color(0xFF1976D2).withOpacity(0.2),
      checkmarkColor: const Color(0xFF1976D2),
    );
  }

  void _updatePriceRange() {
    final minPrice = double.tryParse(_minPriceController.text);
    final maxPrice = double.tryParse(_maxPriceController.text);
    widget.onPriceRangeChanged(minPrice, maxPrice);
  }

  @override
  void dispose() {
    _minPriceController.dispose();
    _maxPriceController.dispose();
    super.dispose();
  }
}

// ويدجت فلاتر مبسط
class SimpleFiltersWidget extends StatelessWidget {
  final List<String> filterOptions;
  final List<String> selectedFilters;
  final Function(List<String>) onFiltersChanged;

  const SimpleFiltersWidget({
    super.key,
    required this.filterOptions,
    required this.selectedFilters,
    required this.onFiltersChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: filterOptions.length,
        itemBuilder: (context, index) {
          final option = filterOptions[index];
          final isSelected = selectedFilters.contains(option);
          
          return Padding(
            padding: const EdgeInsets.only(left: 8),
            child: FilterChip(
              label: Text(option),
              selected: isSelected,
              onSelected: (selected) {
                final newFilters = List<String>.from(selectedFilters);
                if (selected) {
                  newFilters.add(option);
                } else {
                  newFilters.remove(option);
                }
                onFiltersChanged(newFilters);
              },
            ),
          );
        },
      ),
    );
  }
}

// ويدجت فلتر نطاق السعر مع شريط تمرير
class PriceRangeSliderWidget extends StatefulWidget {
  final double minPrice;
  final double maxPrice;
  final double currentMin;
  final double currentMax;
  final Function(double, double) onChanged;

  const PriceRangeSliderWidget({
    super.key,
    required this.minPrice,
    required this.maxPrice,
    required this.currentMin,
    required this.currentMax,
    required this.onChanged,
  });

  @override
  State<PriceRangeSliderWidget> createState() => _PriceRangeSliderWidgetState();
}

class _PriceRangeSliderWidgetState extends State<PriceRangeSliderWidget> {
  late RangeValues _currentRangeValues;

  @override
  void initState() {
    super.initState();
    _currentRangeValues = RangeValues(widget.currentMin, widget.currentMax);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نطاق السعر: ${_currentRangeValues.start.toInt()} - ${_currentRangeValues.end.toInt()} د.ع',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        RangeSlider(
          values: _currentRangeValues,
          min: widget.minPrice,
          max: widget.maxPrice,
          divisions: 20,
          labels: RangeLabels(
            '${_currentRangeValues.start.toInt()}',
            '${_currentRangeValues.end.toInt()}',
          ),
          onChanged: (RangeValues values) {
            setState(() {
              _currentRangeValues = values;
            });
          },
          onChangeEnd: (RangeValues values) {
            widget.onChanged(values.start, values.end);
          },
        ),
      ],
    );
  }
}
