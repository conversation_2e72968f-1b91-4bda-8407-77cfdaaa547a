import 'package:flutter/material.dart';
import '../features/home/<USER>/product_model.dart';
import '../services/cart_service.dart';

class CartItem {
  final ProductModel product;
  int quantity;
  final DateTime addedAt;

  CartItem({
    required this.product,
    required this.quantity,
    required this.addedAt,
  });

  double get totalPrice => (product.price ?? 0) * quantity;

  Map<String, dynamic> toJson() => {
    'product': product.toJson(),
    'quantity': quantity,
    'addedAt': addedAt.toIso8601String(),
  };

  factory CartItem.fromJson(Map<String, dynamic> json) => CartItem(
    product: ProductModel.fromJson(json['product']),
    quantity: json['quantity'],
    addedAt: DateTime.parse(json['addedAt']),
  );
}

class CartProvider extends ChangeNotifier {
  final List<CartItem> _items = [];
  final CartService _cartService = CartService();

  List<CartItem> get items => List.unmodifiable(_items);
  
  int get itemCount => _items.fold(0, (sum, item) => sum + item.quantity);
  
  double get totalPrice => _items.fold(0, (sum, item) => sum + item.totalPrice);
  
  bool get isEmpty => _items.isEmpty;
  
  bool get isNotEmpty => _items.isNotEmpty;

  // إضافة منتج للسلة
  void addToCart(ProductModel product, [int quantity = 1]) {
    final existingIndex = _items.indexWhere(
      (item) => item.product.id == product.id,
    );

    if (existingIndex >= 0) {
      _items[existingIndex].quantity += quantity;
    } else {
      _items.add(CartItem(
        product: product,
        quantity: quantity,
        addedAt: DateTime.now(),
      ));
    }

    _saveCart();
    notifyListeners();
  }

  // إزالة منتج من السلة
  void removeFromCart(int productId) {
    _items.removeWhere((item) => item.product.id == productId);
    _saveCart();
    notifyListeners();
  }

  // تحديث كمية منتج
  void updateQuantity(int productId, int newQuantity) {
    if (newQuantity <= 0) {
      removeFromCart(productId);
      return;
    }

    final index = _items.indexWhere(
      (item) => item.product.id == productId,
    );

    if (index >= 0) {
      _items[index].quantity = newQuantity;
      _saveCart();
      notifyListeners();
    }
  }

  // زيادة الكمية
  void incrementQuantity(int productId) {
    final index = _items.indexWhere(
      (item) => item.product.id == productId,
    );

    if (index >= 0) {
      _items[index].quantity++;
      _saveCart();
      notifyListeners();
    }
  }

  // تقليل الكمية
  void decrementQuantity(int productId) {
    final index = _items.indexWhere(
      (item) => item.product.id == productId,
    );

    if (index >= 0) {
      if (_items[index].quantity > 1) {
        _items[index].quantity--;
      } else {
        _items.removeAt(index);
      }
      _saveCart();
      notifyListeners();
    }
  }

  // مسح السلة
  void clearCart() {
    _items.clear();
    _saveCart();
    notifyListeners();
  }

  // التحقق من وجود منتج في السلة
  bool isInCart(int productId) {
    return _items.any((item) => item.product.id == productId);
  }

  // الحصول على كمية منتج معين
  int getQuantity(int productId) {
    final item = _items.firstWhere(
      (item) => item.product.id == productId,
      orElse: () => CartItem(
        product: ProductModel(
          id: -1,
          name: '',
          price: 0,
          description: '',
          sku: '',
          imageUrl: ''
        ),
        quantity: 0,
        addedAt: DateTime.now(),
      ),
    );
    return item.quantity;
  }

  // حفظ السلة محلياً
  Future<void> _saveCart() async {
    try {
      await _cartService.saveCart(_items);
    } catch (e) {
      debugPrint('Error saving cart: $e');
    }
  }

  // تحميل السلة المحفوظة
  Future<void> loadCart() async {
    try {
      final savedItems = await _cartService.loadCart();
      _items.clear();
      _items.addAll(savedItems);
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading cart: $e');
    }
  }

  // إرسال السلة للخادم (للمستخدمين المسجلين)
  Future<bool> syncCartWithServer() async {
    try {
      final success = await _cartService.syncWithServer(_items);
      if (success) {
        notifyListeners();
      }
      return success;
    } catch (e) {
      debugPrint('Error syncing cart: $e');
      return false;
    }
  }

  // تحميل السلة من الخادم
  Future<void> loadCartFromServer() async {
    try {
      final serverItems = await _cartService.loadFromServer();
      if (serverItems.isNotEmpty) {
        _items.clear();
        _items.addAll(serverItems);
        _saveCart();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading cart from server: $e');
    }
  }

  // دمج السلة المحلية مع سلة الخادم
  Future<void> mergeWithServerCart() async {
    try {
      final serverItems = await _cartService.loadFromServer();
      
      for (final serverItem in serverItems) {
        final localIndex = _items.indexWhere(
          (item) => item.product.id == serverItem.product.id,
        );
        
        if (localIndex >= 0) {
          // دمج الكميات
          _items[localIndex].quantity += serverItem.quantity.toInt();
        } else {
          // إضافة منتج جديد
          _items.add(serverItem);
        }
      }
      
      _saveCart();
      await syncCartWithServer();
      notifyListeners();
    } catch (e) {
      debugPrint('Error merging carts: $e');
    }
  }

  // حساب الخصم
  double calculateDiscount() {
    // يمكن تطبيق منطق الخصم هنا
    if (totalPrice > 1000) {
      return totalPrice * 0.1; // خصم 10% للطلبات أكثر من 1000 ريال
    }
    return 0;
  }

  // السعر النهائي بعد الخصم
  double get finalPrice => totalPrice - calculateDiscount();

  // رسوم الشحن
  double get shippingFee {
    if (totalPrice > 500) {
      return 0; // شحن مجاني للطلبات أكثر من 500 ريال
    }
    return 25; // رسوم شحن ثابتة
  }

  // المجموع الكلي مع الشحن
  double get grandTotal => finalPrice + shippingFee;
}
