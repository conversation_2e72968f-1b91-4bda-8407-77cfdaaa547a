@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-edit me-2"></i>
                        تعديل السلايدر #{{ $slider['id'] ?? 'غير محدد' }}
                    </h4>
                    <small class="text-muted d-block">البيانات الحالية: {{ json_encode($slider) }}</small>
                    <div class="card-actions">
                        <a href="{{ route('revo-woocommerce.home-slider.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <div class="alert alert-info">
                        <strong>معلومات التصحيح:</strong><br>
                        ID: {{ $slider['id'] ?? 'غير محدد' }}<br>
                        Action: {{ route('revo-woocommerce.home-slider.update', $slider['id'] ?? 0) }}<br>
                        Method: PUT
                    </div>

                    <form action="{{ route('revo-woocommerce.home-slider.update', $slider['id']) }}" method="POST" class="needs-validation" novalidate>
                        @csrf
                        @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label for="title" class="form-label">
                                    <i class="ti ti-heading me-1"></i>
                                    عنوان السلايدر
                                </label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="{{ old('title', $slider['title']) }}" 
                                       placeholder="مثال: عرض خاص">
                                <div class="form-text">العنوان الذي سيظهر على السلايدر (اختياري)</div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="order" class="form-label">
                                    <i class="ti ti-sort-ascending me-1"></i>
                                    الترتيب
                                </label>
                                <input type="number" class="form-control" id="order" name="order" 
                                       value="{{ old('order', $slider['order']) }}" 
                                       placeholder="1" min="0">
                                <div class="form-text">ترتيب ظهور السلايدر</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="image" class="form-label">
                            <i class="ti ti-photo me-1"></i>
                            صورة السلايدر
                            <span class="text-danger">*</span>
                        </label>
                        {!! Form::mediaImage('image', old('image', $slider['image'])) !!}
                        <div class="form-text">اختر صورة السلايدر (يفضل 800x400 بكسل)</div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="link" class="form-label">
                            <i class="ti ti-link me-1"></i>
                            رابط الانتقال
                        </label>
                        <input type="url" class="form-control" id="link" name="link" 
                               value="{{ old('link', $slider['link']) }}" 
                               placeholder="https://example.com/offer">
                        <div class="form-text">الرابط الذي سينتقل إليه المستخدم عند النقر على السلايدر (اختياري)</div>
                    </div>
                    

                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <button type="submit" class="btn btn-success btn-lg me-3">
                                        <i class="ti ti-device-floppy me-2"></i>
                                        حفظ التعديلات
                                    </button>
                                    <a href="{{ route('revo-woocommerce.home-slider.index') }}" class="btn btn-secondary btn-lg">
                                        <i class="ti ti-x me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    $(document).ready(function() {
        // Form validation
        $('.needs-validation').on('submit', function(e) {
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            $(this).addClass('was-validated');
        });
        

        
        // Success message
        @if(session('success'))
            Botble.showSuccess('{{ session('success') }}');
        @endif
        
        // Error messages
        @if($errors->any())
            @foreach($errors->all() as $error)
                Botble.showError('{{ $error }}');
            @endforeach
        @endif
    });
</script>
@endpush
