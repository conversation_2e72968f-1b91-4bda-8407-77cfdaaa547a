import 'package:flutter/material.dart';

class FeaturedProductsWidget extends StatelessWidget {
  const FeaturedProductsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final products = _getFeaturedProducts();
    
    return Container(
      height: 280,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: products.length,
        itemBuilder: (context, index) {
          return _buildProductCard(context, products[index]);
        },
      ),
    );
  }

  Widget _buildProductCard(BuildContext context, ProductData product) {
    return GestureDetector(
      onTap: () => _onProductTap(context, product),
      child: Container(
        width: 200,
        margin: const EdgeInsets.only(left: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المنتج
            Container(
              height: 140,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.car_repair,
                      size: 60,
                      color: Colors.grey[400],
                    ),
                  ),
                  if (product.discount > 0)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${product.discount}%',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  Positioned(
                    top: 8,
                    left: 8,
                    child: GestureDetector(
                      onTap: () => _toggleWishlist(context, product),
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                        child: Icon(
                          product.isFavorite 
                              ? Icons.favorite 
                              : Icons.favorite_border,
                          color: product.isFavorite 
                              ? Colors.red 
                              : Colors.grey,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // معلومات المنتج
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      product.brand,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const Spacer(),
                    Row(
                      children: [
                        ...List.generate(5, (index) {
                          return Icon(
                            index < product.rating 
                                ? Icons.star 
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 14,
                          );
                        }),
                        const SizedBox(width: 4),
                        Text(
                          '(${product.reviewCount})',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        if (product.originalPrice > product.price)
                          Text(
                            '${product.originalPrice.toStringAsFixed(0)} د.ع',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),
                        const SizedBox(width: 4),
                        Text(
                          '${product.price.toStringAsFixed(0)} د.ع',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF1976D2),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<ProductData> _getFeaturedProducts() {
    return [
      ProductData(
        name: 'فلتر هواء أصلي تويوتا',
        brand: 'تويوتا',
        price: 45000,
        originalPrice: 50000,
        discount: 10,
        rating: 5,
        reviewCount: 24,
        isFavorite: false,
      ),
      ProductData(
        name: 'زيت محرك موبيل 1',
        brand: 'موبيل',
        price: 85000,
        originalPrice: 95000,
        discount: 11,
        rating: 4,
        reviewCount: 18,
        isFavorite: true,
      ),
      ProductData(
        name: 'بطارية ACDelco',
        brand: 'ACDelco',
        price: 120000,
        originalPrice: 0,
        discount: 0,
        rating: 5,
        reviewCount: 32,
        isFavorite: false,
      ),
      ProductData(
        name: 'فرامل بريمبو',
        brand: 'بريمبو',
        price: 75000,
        originalPrice: 85000,
        discount: 12,
        rating: 4,
        reviewCount: 15,
        isFavorite: false,
      ),
    ];
  }

  void _onProductTap(BuildContext context, ProductData product) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض تفاصيل: ${product.name}'),
        backgroundColor: const Color(0xFF1976D2),
      ),
    );
  }

  void _toggleWishlist(BuildContext context, ProductData product) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          product.isFavorite 
              ? 'تم حذف ${product.name} من المفضلة'
              : 'تم إضافة ${product.name} للمفضلة',
        ),
        backgroundColor: product.isFavorite ? Colors.orange : Colors.green,
      ),
    );
  }
}

class ProductData {
  final String name;
  final String brand;
  final double price;
  final double originalPrice;
  final int discount;
  final int rating;
  final int reviewCount;
  bool isFavorite;

  ProductData({
    required this.name,
    required this.brand,
    required this.price,
    required this.originalPrice,
    required this.discount,
    required this.rating,
    required this.reviewCount,
    required this.isFavorite,
  });
}
