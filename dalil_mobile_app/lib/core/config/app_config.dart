class AppConfig {
  static const String appName = 'دليل';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق دليل لقطع غيار السيارات';
  
  // إعدادات API
  static const String baseUrl = 'http://localhost:8080/api/v1';
  static const String imageBaseUrl = 'http://localhost:8080';
  
  // مفاتيح التخزين المحلي
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String languageKey = 'app_language';
  static const String themeKey = 'app_theme';
  static const String savedVehiclesKey = 'saved_vehicles';
  static const String cartKey = 'cart_items';
  static const String favoritesKey = 'favorite_products';
  
  // إعدادات التطبيق
  static const int requestTimeout = 30; // ثانية
  static const int maxRetries = 3;
  static const int itemsPerPage = 20;
  
  // إعدادات الصور
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // إعدادات الإشعارات
  static const String fcmServerKey = 'YOUR_FCM_SERVER_KEY';
  
  // روابط مهمة
  static const String privacyPolicyUrl = 'https://dalil.com/privacy';
  static const String termsOfServiceUrl = 'https://dalil.com/terms';
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+964123456789';
  
  // إعدادات Vehicle Parts Finder
  static const String vehiclePartsEndpoint = '/vehicle-parts';
  static const String myVehiclesEndpoint = '/vehicle-parts/my-vehicles';
  
  // ألوان العلامة التجارية
  static const int primaryColorValue = 0xFF1976D2;
  static const int secondaryColorValue = 0xFF03DAC6;
  static const int accentColorValue = 0xFFFF6B35;
  
  // إعدادات الخريطة
  static const double defaultLatitude = 33.3152;
  static const double defaultLongitude = 44.3661;
  static const double defaultZoom = 12.0;
  
  // إعدادات البحث
  static const int searchDebounceMs = 500;
  static const int minSearchLength = 2;
  static const int maxSearchHistory = 10;
  
  // إعدادات الكاش
  static const int cacheExpiryHours = 24;
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  
  // أنواع المستخدمين
  static const String customerType = 'customer';
  static const String wholesaleType = 'wholesale';
  
  // حالات الطلبات
  static const String orderPending = 'pending';
  static const String orderConfirmed = 'confirmed';
  static const String orderShipped = 'shipped';
  static const String orderDelivered = 'delivered';
  static const String orderCancelled = 'cancelled';
  
  // أنواع الدفع
  static const String paymentCash = 'cash';
  static const String paymentCard = 'card';
  static const String paymentWallet = 'wallet';
  
  // إعدادات التطوير
  static const bool isDebugMode = true;
  static const bool enableLogging = true;
  static const bool enableCrashlytics = false;
  
  // الحصول على URL كامل
  static String getFullUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }
  
  // الحصول على URL الصورة كامل
  static String getImageUrl(String imagePath) {
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    return '$imageBaseUrl/$imagePath';
  }
  
  // التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  // التحقق من صحة رقم الهاتف العراقي
  static bool isValidIraqiPhone(String phone) {
    return RegExp(r'^(\+964|964|0)?7[0-9]{9}$').hasMatch(phone);
  }
  
  // تنسيق رقم الهاتف
  static String formatPhoneNumber(String phone) {
    phone = phone.replaceAll(RegExp(r'[^\d]'), '');
    if (phone.startsWith('964')) {
      return '+$phone';
    } else if (phone.startsWith('0')) {
      return '+964${phone.substring(1)}';
    } else if (phone.length == 10) {
      return '+964$phone';
    }
    return phone;
  }
}
