<?php

namespace Botble\Gallery\Http\Resources;

use Botble\Media\Facades\RvMedia;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GalleryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'is_featured' => (bool) $this->is_featured,
            'order' => $this->order,
            'status' => $this->status,
            'image' => $this->image ? RvMedia::getImageUrl($this->image) : null,
            'image_thumb' => $this->image ? RvMedia::getImageUrl($this->image, 'thumb') : null,
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                ];
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
