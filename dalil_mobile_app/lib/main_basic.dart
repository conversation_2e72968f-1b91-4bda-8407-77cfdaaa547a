import 'package:flutter/material.dart';

void main() {
  runApp(const Dal<PERSON>App());
}

class DalilApp extends StatelessWidget {
  const DalilApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'دليل - Dalil',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Arial',
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const DemoHomeScreen(),
    );
  }
}

class DemoHomeScreen extends StatefulWidget {
  const DemoHomeScreen({super.key});

  @override
  State<DemoHomeScreen> createState() => _DemoHomeScreenState();
}

class _DemoHomeScreenState extends State<DemoHomeScreen> {
  String currentLanguage = 'العربية';
  
  final Map<String, Map<String, String>> translations = {
    'العربية': {
      'app_name': 'دليل',
      'welcome': 'مرحباً بك في دليل',
      'vehicle_parts_finder': 'باحث قطع الغيار الذكي',
      'vehicle_parts_finder_desc': 'ابحث عن قطع الغيار المناسبة لسيارتك بسهولة',
      'start_search': 'ابدأ البحث',
      'quick_services': 'الخدمات السريعة',
      'my_vehicles': 'سياراتي المحفوظة',
      'my_vehicles_desc': 'إدارة سياراتك',
      'qr_scanner': 'مسح QR Code',
      'qr_scanner_desc': 'مسح رمز المنتج',
      'special_offers': 'العروض الخاصة',
      'special_offers_desc': 'اكتشف العروض',
      'customer_service': 'خدمة العملاء',
      'customer_service_desc': 'تواصل معنا',
    },
    'English': {
      'app_name': 'Dalil',
      'welcome': 'Welcome to Dalil',
      'vehicle_parts_finder': 'Smart Vehicle Parts Finder',
      'vehicle_parts_finder_desc': 'Easily find the right parts for your vehicle',
      'start_search': 'Start Search',
      'quick_services': 'Quick Services',
      'my_vehicles': 'My Saved Vehicles',
      'my_vehicles_desc': 'Manage your vehicles',
      'qr_scanner': 'QR Scanner',
      'qr_scanner_desc': 'Scan product code',
      'special_offers': 'Special Offers',
      'special_offers_desc': 'Discover offers',
      'customer_service': 'Customer Service',
      'customer_service_desc': 'Contact us',
    },
    'کوردی': {
      'app_name': 'دەلیل',
      'welcome': 'بەخێربێیت بۆ دەلیل',
      'vehicle_parts_finder': 'گەڕەری زیرەکی پارچەکانی ئۆتۆمبێل',
      'vehicle_parts_finder_desc': 'بە ئاسانی پارچەی گونجاو بۆ ئۆتۆمبێلەکەت بدۆزەرەوە',
      'start_search': 'دەستپێکردنی گەڕان',
      'quick_services': 'خزمەتگوزاریە خێراکان',
      'my_vehicles': 'ئۆتۆمبێلە پاشەکەوتکراوەکانم',
      'my_vehicles_desc': 'بەڕێوەبردنی ئۆتۆمبێلەکانت',
      'qr_scanner': 'سکانەری QR',
      'qr_scanner_desc': 'سکانکردنی کۆدی بەرهەم',
      'special_offers': 'پێشکەشە تایبەتەکان',
      'special_offers_desc': 'پێشکەشەکان بدۆزەرەوە',
      'customer_service': 'خزمەتگوزاری کڕیار',
      'customer_service_desc': 'پەیوەندیمان پێوە بکە',
    },
  };

  String tr(String key) {
    return translations[currentLanguage]?[key] ?? key;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('app_name')),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.language),
            onSelected: (language) {
              setState(() {
                currentLanguage = language;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'العربية',
                child: Text('🇪🇬 العربية'),
              ),
              const PopupMenuItem(
                value: 'English',
                child: Text('🇺🇸 English'),
              ),
              const PopupMenuItem(
                value: 'کوردی',
                child: Text('🇮🇶 کوردی'),
              ),
            ],
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              tr('welcome'),
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // بطاقة باحث قطع الغيار
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.car_repair,
                            size: 32,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                tr('vehicle_parts_finder'),
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                tr('vehicle_parts_finder_desc'),
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    ElevatedButton.icon(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('${tr('start_search')} - تم النقر!'),
                            backgroundColor: Colors.green,
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                      icon: const Icon(Icons.search),
                      label: Text(tr('start_search')),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        minimumSize: const Size(double.infinity, 48),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            Text(
              tr('quick_services'),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildServiceCard(
                    Icons.garage,
                    tr('my_vehicles'),
                    tr('my_vehicles_desc'),
                    Colors.orange,
                  ),
                  _buildServiceCard(
                    Icons.qr_code_scanner,
                    tr('qr_scanner'),
                    tr('qr_scanner_desc'),
                    Colors.green,
                  ),
                  _buildServiceCard(
                    Icons.local_offer,
                    tr('special_offers'),
                    tr('special_offers_desc'),
                    Colors.red,
                  ),
                  _buildServiceCard(
                    Icons.support_agent,
                    tr('customer_service'),
                    tr('customer_service_desc'),
                    Colors.purple,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceCard(
    IconData icon,
    String title,
    String subtitle,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم النقر على: $title'),
              backgroundColor: color,
              duration: const Duration(seconds: 2),
            ),
          );
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
