<?php

namespace Bo<PERSON>ble\Ecommerce\Models;

use Botble\Base\Casts\SafeContent;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Botble\Base\Models\Concerns\HasSlug;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CarModel extends BaseModel
{
    use HasSlug;

    protected $table = 'car_models';

    protected $fillable = [
        'make_id',
        'name',
        'slug',
        'description',
        'image',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
    ];

    public function make(): BelongsTo
    {
        return $this->belongsTo(CarMake::class, 'make_id');
    }

    public function years(): HasMany
    {
        return $this->hasMany(CarYear::class, 'model_id');
    }
} 