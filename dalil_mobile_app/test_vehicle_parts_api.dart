import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  print('🚀 بدء اختبار Vehicle Parts APIs...\n');
  
  const String baseUrl = 'http://localhost:8000/api/v1';
  
  // اختبار 1: الحصول على الفئات الجذرية
  await testRootCategories(baseUrl);
  
  // اختبار 2: البحث عن قطع الغيار
  await testSearchParts(baseUrl);
  
  // اختبار 3: البحث بكلمة مفتاحية
  await testSearchWithKeyword(baseUrl);
  
  // اختبار 4: الحصول على قطع الغيار الشائعة
  await testPopularParts(baseUrl);
  
  print('\n✅ انتهى اختبار جميع APIs بنجاح!');
}

Future<void> testRootCategories(String baseUrl) async {
  print('📂 اختبار الحصول على الفئات الجذرية...');
  
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/vehicle-parts/root-categories'),
      headers: {'Accept': 'application/json'},
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      print('✅ نجح الحصول على ${data['data'].length} فئة جذرية');
      
      // طباعة أول فئة كمثال
      if (data['data'].isNotEmpty) {
        final firstCategory = data['data'][0];
        print('   مثال: ${firstCategory['name']} (ID: ${firstCategory['id']})');
      }
    } else {
      print('❌ فشل: ${response.statusCode} - ${response.body}');
    }
  } catch (e) {
    print('❌ خطأ: $e');
  }
  print('');
}

Future<void> testSearchParts(String baseUrl) async {
  print('🔍 اختبار البحث العام عن قطع الغيار...');
  
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/vehicle-parts/search'),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: json.encode({'per_page': 5}),
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final products = data['data']['products'];
      print('✅ نجح البحث ووُجد ${products.length} منتج');
      
      // طباعة أول منتج كمثال
      if (products.isNotEmpty) {
        final firstProduct = products[0];
        print('   مثال: ${firstProduct['name']} - ${firstProduct['price']} ريال');
      }
    } else {
      print('❌ فشل: ${response.statusCode} - ${response.body}');
    }
  } catch (e) {
    print('❌ خطأ: $e');
  }
  print('');
}

Future<void> testSearchWithKeyword(String baseUrl) async {
  print('🔍 اختبار البحث بكلمة مفتاحية...');
  
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/vehicle-parts/search'),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'keyword': 'بروجكتر',
        'per_page': 3,
      }),
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final products = data['data']['products'];
      print('✅ نجح البحث بكلمة "بروجكتر" ووُجد ${products.length} منتج');
      
      // طباعة النتائج
      for (var product in products) {
        print('   - ${product['name']} - ${product['price']} ريال');
      }
    } else {
      print('❌ فشل: ${response.statusCode} - ${response.body}');
    }
  } catch (e) {
    print('❌ خطأ: $e');
  }
  print('');
}

Future<void> testPopularParts(String baseUrl) async {
  print('⭐ اختبار الحصول على قطع الغيار الشائعة...');
  
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/vehicle-parts/categories/2/popular-parts?limit=3'),
      headers: {'Accept': 'application/json'},
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final popularParts = data['data']['popular_parts'];
      print('✅ نجح الحصول على ${popularParts.length} قطعة شائعة للفئة 2');
      
      // طباعة القطع الشائعة
      for (var part in popularParts) {
        print('   - ${part['name']} - ${part['price']} ريال');
      }
    } else {
      print('❌ فشل: ${response.statusCode} - ${response.body}');
    }
  } catch (e) {
    print('❌ خطأ: $e');
  }
  print('');
}
