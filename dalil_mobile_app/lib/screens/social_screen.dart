import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/social_provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/custom_button.dart';
import '../widgets/loading_widget.dart';

class SocialScreen extends StatefulWidget {
  const SocialScreen({super.key});

  @override
  State<SocialScreen> createState() => _SocialScreenState();
}

class _SocialScreenState extends State<SocialScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SocialProvider>().loadSocialData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<SocialProvider, ThemeProvider>(
      builder: (context, socialProvider, themeProvider, child) {
        final isDark = themeProvider.isDarkMode;
        
        return Scaffold(
          backgroundColor: isDark ? Colors.grey[900] : Colors.grey[50],
          appBar: AppBar(
            title: const Text('المشاركة والإحالة'),
            backgroundColor: isDark ? Colors.grey[850] : Colors.white,
            foregroundColor: isDark ? Colors.white : Colors.black,
            bottom: TabBar(
              controller: _tabController,
              labelColor: isDark ? Colors.white : Colors.black,
              unselectedLabelColor: isDark ? Colors.grey[400] : Colors.grey[600],
              indicatorColor: Theme.of(context).primaryColor,
              tabs: const [
                Tab(text: 'المشاركة', icon: Icon(Icons.share)),
                Tab(text: 'الإحالة', icon: Icon(Icons.card_giftcard)),
                Tab(text: 'التاريخ', icon: Icon(Icons.history)),
              ],
            ),
            actions: [
              PopupMenuButton<String>(
                onSelected: (value) => _handleMenuAction(value, socialProvider),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'sample',
                    child: Row(
                      children: [
                        Icon(Icons.bug_report),
                        SizedBox(width: 8),
                        Text('بيانات تجريبية'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'clear',
                    child: Row(
                      children: [
                        Icon(Icons.clear_all, color: Colors.red),
                        SizedBox(width: 8),
                        Text('مسح التاريخ'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          body: socialProvider.isLoading
              ? const Center(child: LoadingWidget())
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildShareTab(socialProvider, isDark),
                    _buildReferralTab(socialProvider, isDark),
                    _buildHistoryTab(socialProvider, isDark),
                  ],
                ),
        );
      },
    );
  }

  Widget _buildShareTab(SocialProvider provider, bool isDark) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // مشاركة التطبيق
          _buildShareAppSection(provider, isDark),
          
          const SizedBox(height: 16),
          
          // مشاركة سريعة
          _buildQuickShareSection(provider, isDark),
          
          const SizedBox(height: 16),
          
          // إحصائيات المشاركة
          _buildShareStatsSection(provider, isDark),
        ],
      ),
    );
  }

  Widget _buildShareAppSection(SocialProvider provider, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.share, color: Colors.white, size: 32),
              SizedBox(width: 12),
              Text(
                'شارك التطبيق',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            'شارك تطبيق دليل مع أصدقائك واحصل على مكافآت رائعة!',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'مشاركة التطبيق',
                  onPressed: () => _showShareDialog(provider),
                  backgroundColor: Colors.white,
                  textColor: Theme.of(context).primaryColor,
                  icon: Icons.share,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickShareSection(SocialProvider provider, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.flash_on, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                'مشاركة سريعة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickShareButton(
                'واتساب',
                Icons.message,
                Colors.green,
                () => provider.shareApp('whatsapp'),
              ),
              _buildQuickShareButton(
                'تيليجرام',
                Icons.send,
                Colors.blue,
                () => provider.shareApp('telegram'),
              ),
              _buildQuickShareButton(
                'تويتر',
                Icons.alternate_email,
                Colors.lightBlue,
                () => provider.shareApp('twitter'),
              ),
              _buildQuickShareButton(
                'نسخ',
                Icons.copy,
                Colors.grey,
                () => provider.shareApp('copy'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickShareButton(String label, IconData icon, Color color, VoidCallback onPressed) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: IconButton(
            icon: Icon(icon, color: color),
            onPressed: () async {
              await onPressed();
              _showSuccessMessage('تم المشاركة على $label');
            },
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildShareStatsSection(SocialProvider provider, bool isDark) {
    final stats = provider.getShareStats();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                'إحصائيات المشاركة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                'إجمالي المشاركات',
                '${stats['totalShares']}',
                Icons.share,
                isDark,
              ),
              _buildStatItem(
                'استخدام الإحالة',
                '${stats['referralUsage']}',
                Icons.people,
                isDark,
              ),
              _buildStatItem(
                'الأرباح',
                '${stats['referralEarnings'].toStringAsFixed(0)} ر.س',
                Icons.monetization_on,
                isDark,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, bool isDark) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isDark ? Colors.grey[400] : Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildReferralTab(SocialProvider provider, bool isDark) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // كود الإحالة
          _buildReferralCodeSection(provider, isDark),
          
          const SizedBox(height: 16),
          
          // كيفية عمل الإحالة
          _buildHowItWorksSection(isDark),
          
          const SizedBox(height: 16),
          
          // إحصائيات الإحالة
          if (provider.hasReferralCode)
            _buildReferralStatsSection(provider, isDark),
        ],
      ),
    );
  }

  Widget _buildReferralCodeSection(SocialProvider provider, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.card_giftcard, color: Colors.amber, size: 32),
              const SizedBox(width: 12),
              Text(
                'كود الإحالة الخاص بك',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          if (provider.hasReferralCode) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.amber),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      provider.referralData!.code,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.amber,
                        letterSpacing: 2,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.copy, color: Colors.amber),
                    onPressed: () => _copyReferralCode(provider.referralData!.code),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'مشاركة الكود',
                    onPressed: () => _shareReferralCode(provider),
                    backgroundColor: Colors.amber,
                    textColor: Colors.white,
                    icon: Icons.share,
                  ),
                ),
              ],
            ),
          ] else ...[
            Text(
              'احصل على كود إحالة خاص بك وابدأ في كسب المكافآت!',
              style: TextStyle(
                fontSize: 16,
                color: isDark ? Colors.grey[300] : Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'إنشاء كود الإحالة',
                    onPressed: () => _generateReferralCode(provider),
                    backgroundColor: Colors.amber,
                    textColor: Colors.white,
                    icon: Icons.add,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHowItWorksSection(bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.help_outline, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                'كيف يعمل نظام الإحالة؟',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildHowItWorksStep('1', 'شارك كودك', 'شارك كود الإحالة الخاص بك مع الأصدقاء', Icons.share, isDark),
          const SizedBox(height: 12),
          _buildHowItWorksStep('2', 'يسجلون باستخدام كودك', 'عندما يستخدم أصدقاؤك كودك عند التسجيل', Icons.person_add, isDark),
          const SizedBox(height: 12),
          _buildHowItWorksStep('3', 'تحصل على مكافآت', 'احصل على 10 ريال لكل صديق يستخدم كودك', Icons.monetization_on, isDark),
        ],
      ),
    );
  }

  Widget _buildHowItWorksStep(String number, String title, String description, IconData icon, bool isDark) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(number, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
          ),
        ),
        const SizedBox(width: 12),
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: isDark ? Colors.white : Colors.black)),
              Text(description, style: TextStyle(fontSize: 14, color: isDark ? Colors.grey[400] : Colors.grey[600])),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReferralStatsSection(SocialProvider provider, bool isDark) {
    final referralData = provider.referralData!;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 4, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.trending_up, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text('إحصائيات الإحالة', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: isDark ? Colors.white : Colors.black)),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildReferralStatCard('الاستخدامات', '${referralData.usageCount}', Icons.people, Colors.blue, isDark)),
              const SizedBox(width: 12),
              Expanded(child: _buildReferralStatCard('الأرباح', '${referralData.totalEarnings.toStringAsFixed(0)} ر.س', Icons.monetization_on, Colors.green, isDark)),
            ],
          ),
          const SizedBox(height: 16),
          Text('تم إنشاء الكود في: ${_formatDate(referralData.createdAt)}', style: TextStyle(fontSize: 12, color: isDark ? Colors.grey[400] : Colors.grey[600])),
        ],
      ),
    );
  }

  Widget _buildReferralStatCard(String label, String value, IconData icon, Color color, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(color: color.withOpacity(0.1), borderRadius: BorderRadius.circular(8), border: Border.all(color: color.withOpacity(0.3))),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(value, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: isDark ? Colors.white : Colors.black)),
          Text(label, style: TextStyle(fontSize: 12, color: isDark ? Colors.grey[400] : Colors.grey[600])),
        ],
      ),
    );
  }

  Widget _buildHistoryTab(SocialProvider provider, bool isDark) {
    final shareHistory = provider.shareHistory;
    if (shareHistory.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 120, color: isDark ? Colors.grey[600] : Colors.grey[400]),
            const SizedBox(height: 24),
            Text('لا يوجد تاريخ مشاركة', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: isDark ? Colors.grey[300] : Colors.grey[600])),
            const SizedBox(height: 12),
            Text('ابدأ بمشاركة التطبيق لرؤية التاريخ هنا', style: TextStyle(fontSize: 16, color: isDark ? Colors.grey[400] : Colors.grey[500])),
          ],
        ),
      );
    }
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: shareHistory.length,
      itemBuilder: (context, index) => _buildHistoryItem(shareHistory[index], provider, isDark),
    );
  }

  Widget _buildHistoryItem(ShareData shareData, SocialProvider provider, bool isDark) {
    IconData icon = shareData.type == 'product' ? Icons.shopping_bag : shareData.type == 'app' ? Icons.mobile_friendly : Icons.local_offer;
    Color color = shareData.type == 'product' ? Colors.blue : shareData.type == 'app' ? Colors.green : Colors.orange;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(color: isDark ? Colors.grey[850] : Colors.white, borderRadius: BorderRadius.circular(12), boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 4, offset: const Offset(0, 2))]),
      child: Row(
        children: [
          Container(padding: const EdgeInsets.all(8), decoration: BoxDecoration(color: color.withOpacity(0.1), shape: BoxShape.circle), child: Icon(icon, color: color, size: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(shareData.title, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: isDark ? Colors.white : Colors.black)),
                const SizedBox(height: 4),
                Text(shareData.description, style: TextStyle(fontSize: 14, color: isDark ? Colors.grey[300] : Colors.grey[700]), maxLines: 2, overflow: TextOverflow.ellipsis),
                const SizedBox(height: 4),
                Text(_formatDateTime(shareData.sharedAt), style: TextStyle(fontSize: 12, color: isDark ? Colors.grey[400] : Colors.grey[500])),
              ],
            ),
          ),
          IconButton(icon: const Icon(Icons.delete, color: Colors.red), onPressed: () => _deleteShare(shareData.id, provider)),
        ],
      ),
    );
  }

  void _handleMenuAction(String action, SocialProvider provider) {
    switch (action) {
      case 'sample': provider.generateSampleData(); break;
      case 'clear': _showClearHistoryDialog(provider); break;
    }
  }

  void _showShareDialog(SocialProvider provider) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('اختر منصة المشاركة', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildShareOption('واتساب', Icons.message, Colors.green, () { provider.shareApp('whatsapp'); Navigator.pop(context); _showSuccessMessage('تم المشاركة على واتساب'); }),
                _buildShareOption('تيليجرام', Icons.send, Colors.blue, () { provider.shareApp('telegram'); Navigator.pop(context); _showSuccessMessage('تم المشاركة على تيليجرام'); }),
                _buildShareOption('تويتر', Icons.alternate_email, Colors.lightBlue, () { provider.shareApp('twitter'); Navigator.pop(context); _showSuccessMessage('تم المشاركة على تويتر'); }),
                _buildShareOption('نسخ', Icons.copy, Colors.grey, () { provider.shareApp('copy'); Navigator.pop(context); _showSuccessMessage('تم نسخ الرابط'); }),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareOption(String label, IconData icon, Color color, VoidCallback onPressed) {
    return Column(
      children: [
        Container(width: 60, height: 60, decoration: BoxDecoration(color: color.withOpacity(0.1), shape: BoxShape.circle, border: Border.all(color: color.withOpacity(0.3))), child: IconButton(icon: Icon(icon, color: color), onPressed: onPressed)),
        const SizedBox(height: 8),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  void _copyReferralCode(String code) { Clipboard.setData(ClipboardData(text: code)); _showSuccessMessage('تم نسخ كود الإحالة'); }
  void _shareReferralCode(SocialProvider provider) { final code = provider.referralData!.code; final text = 'استخدم كود الإحالة الخاص بي: $code للحصول على خصم خاص في تطبيق دليل لقطع غيار السيارات!'; Clipboard.setData(ClipboardData(text: text)); _showSuccessMessage('تم نسخ رسالة الإحالة'); }
  void _generateReferralCode(SocialProvider provider) async { final code = await provider.generateReferralCode(); _showSuccessMessage('تم إنشاء كود الإحالة: $code'); }

  void _deleteShare(String shareId, SocialProvider provider) {
    showDialog(context: context, builder: (context) => AlertDialog(title: const Text('حذف المشاركة'), content: const Text('هل تريد حذف هذه المشاركة من التاريخ؟'), actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('إلغاء')), TextButton(onPressed: () { provider.deleteShare(shareId); Navigator.pop(context); }, child: const Text('حذف', style: TextStyle(color: Colors.red)))]));
  }

  void _showClearHistoryDialog(SocialProvider provider) {
    showDialog(context: context, builder: (context) => AlertDialog(title: const Text('مسح تاريخ المشاركة'), content: const Text('هل تريد مسح جميع تاريخ المشاركة؟ لا يمكن التراجع عن هذا الإجراء.'), actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('إلغاء')), TextButton(onPressed: () { provider.clearShareHistory(); Navigator.pop(context); _showSuccessMessage('تم مسح تاريخ المشاركة'); }, child: const Text('مسح', style: TextStyle(color: Colors.red)))]));
  }

  void _showSuccessMessage(String message) { ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: Colors.green, duration: const Duration(seconds: 2))); }
  String _formatDate(DateTime date) => '${date.day}/${date.month}/${date.year}';
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    if (difference.inMinutes < 1) return 'الآن';
    else if (difference.inHours < 1) return 'منذ ${difference.inMinutes} دقيقة';
    else if (difference.inDays < 1) return 'منذ ${difference.inHours} ساعة';
    else return '${dateTime.day}/${dateTime.month} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
