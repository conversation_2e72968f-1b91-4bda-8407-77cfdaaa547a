import 'package:http/http.dart' as http;
import 'dart:convert';
import '../config/app_config.dart';

class ThemeService {
  static Map<String, dynamic>? _cachedTheme;

  // جلب إعدادات الثيم من API
  static Future<Map<String, dynamic>> getThemeSettings() async {
    if (_cachedTheme != null) {
      return _cachedTheme!;
    }

    try {
      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}/theme-api.php'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          _cachedTheme = data['data'];
          return _cachedTheme!;
        }
      }

      return _getDefaultTheme();
    } catch (e) {
      print('خطأ في جلب إعدادات الثيم: $e');
      return _getDefaultTheme();
    }
  }

  // إعدادات الثيم الافتراضية
  static Map<String, dynamic> _getDefaultTheme() {
    return {
      'colors': {
        'primary': '#0C55AA',
        'secondary': '#6c7a91',
        'text': '#182433',
        'link': '#206bc4',
        'success': '#28a745',
        'warning': '#ffc107',
        'danger': '#dc3545',
        'info': '#17a2b8',
        'light': '#f8f9fa',
        'dark': '#343a40',
      },
      'fonts': {
        'primary': 'Roboto',
        'arabic': 'Cairo',
      },
      'branding': {
        'site_name': 'دليل قطع الغيار',
        'site_title': 'دليلك لقطع غيار السيارات',
        'logo': '',
        'logo_light': '',
        'favicon': '',
      },
      'mobile_specific': {
        'primary_color_hex': 4278998442,
        'secondary_color_hex': 4285299345,
        'text_color_hex': 4279772211,
        'gradient_colors': ['#0C55AA', '#3c77bb'],
        'color_palette': {
          'primary': '#0C55AA',
          'primary_light': '#3c77bb',
          'primary_dark': '#094488',
          'grey_100': '#f8f9fa',
          'grey_200': '#e9ecef',
          'grey_300': '#dee2e6',
          'grey_400': '#ced4da',
          'grey_500': '#adb5bd',
          'grey_600': '#6c757d',
          'grey_700': '#495057',
          'grey_800': '#343a40',
          'grey_900': '#212529',
        }
      },
      'typography': {
        'font_sizes': {
          'xs': 12,
          'sm': 14,
          'base': 16,
          'lg': 18,
          'xl': 20,
          '2xl': 24,
          '3xl': 30,
          '4xl': 36,
          '5xl': 48,
        },
        'font_weights': {
          'light': 300,
          'normal': 400,
          'medium': 500,
          'semibold': 600,
          'bold': 700,
          'extrabold': 800,
        }
      }
    };
  }

  // تحويل hex string إلى Color
  static int hexToColor(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex'; // إضافة alpha channel
    }
    return int.parse(hex, radix: 16);
  }

  // الحصول على لون من الثيم
  static int getColor(Map<String, dynamic> theme, String colorKey) {
    final colorHex = theme['colors']?[colorKey] ?? '#0C55AA';
    return hexToColor(colorHex);
  }

  // الحصول على لون من palette
  static int getPaletteColor(Map<String, dynamic> theme, String colorKey) {
    final colorHex = theme['mobile_specific']?['color_palette']?[colorKey] ?? '#0C55AA';
    return hexToColor(colorHex);
  }

  // الحصول على حجم الخط
  static double getFontSize(Map<String, dynamic> theme, String sizeKey) {
    return (theme['typography']?['font_sizes']?[sizeKey] ?? 16).toDouble();
  }

  // الحصول على وزن الخط
  static int getFontWeight(Map<String, dynamic> theme, String weightKey) {
    return theme['typography']?['font_weights']?[weightKey] ?? 400;
  }

  // الحصول على اسم الخط
  static String getFontFamily(Map<String, dynamic> theme, {bool isArabic = true}) {
    if (isArabic) {
      return theme['fonts']?['arabic'] ?? 'Cairo';
    } else {
      return theme['fonts']?['primary'] ?? 'Roboto';
    }
  }

  // الحصول على معلومات العلامة التجارية
  static String getBrandingInfo(Map<String, dynamic> theme, String key) {
    return theme['branding']?[key] ?? '';
  }

  // مسح الكاش
  static void clearCache() {
    _cachedTheme = null;
  }

  // إعادة تحميل الثيم
  static Future<Map<String, dynamic>> reloadTheme() async {
    clearCache();
    return await getThemeSettings();
  }
}
