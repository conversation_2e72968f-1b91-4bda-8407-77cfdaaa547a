<?php

namespace Botble\RevoWoocommerce\Http\Controllers\Api;

use Botble\Base\Http\Controllers\BaseController;
use Illuminate\Http\JsonResponse;
use Botble\Setting\Facades\Setting;

class SettingsController extends BaseController
{
    public function index(): JsonResponse
    {
        $settings = [
            'app_title' => Setting::get('revo_woocommerce_app_title', 'RevoWOO App'),
            'app_logo' => Setting::get('revo_woocommerce_app_logo', ''),
            'contact_info' => Setting::get('revo_woocommerce_contact_info', ''),
            'about_url' => Setting::get('revo_woocommerce_about_url', ''),
            'customer_support_url' => Setting::get('revo_woocommerce_customer_support_url', ''),
            'version' => '7.0.3',
            'api_version' => 'v1',
        ];

        return response()->json([
            'success' => true,
            'data' => $settings,
        ]);
    }

    public function introPages(): JsonResponse
    {
        // Get intro pages from database or settings
        $introPages = $this->getIntroPages();

        return response()->json([
            'success' => true,
            'data' => $introPages,
        ]);
    }

    public function splashScreen(): JsonResponse
    {
        $splashScreen = [
            'image' => Setting::get('revo_woocommerce_splash_image', ''),
            'duration' => Setting::get('revo_woocommerce_splash_duration', 3),
            'enabled' => Setting::get('revo_woocommerce_splash_enabled', true),
        ];

        return response()->json([
            'success' => true,
            'data' => $splashScreen,
        ]);
    }

    protected function getIntroPages(): array
    {
        // Get intro pages from settings
        $pagesJson = Setting::get('revo_intro_pages', '[]');
        $pages = json_decode($pagesJson, true) ?: [];

        // Filter active pages and sort by order
        $activePages = array_filter($pages, function($page) {
            return $page['status'] ?? true;
        });

        usort($activePages, function($a, $b) {
            return ($a['order'] ?? 0) - ($b['order'] ?? 0);
        });

        return array_values($activePages);
    }
}
