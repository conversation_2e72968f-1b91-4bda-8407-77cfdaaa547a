<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Commands;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Ecommerce\Models\Product;
use Bo<PERSON>ble\Ecommerce\Models\ProductCategory;
use Bo<PERSON>ble\Ecommerce\Models\Brand;
use Bo<PERSON>ble\Ecommerce\Models\ProductTag;
use Botble\Slug\Models\Slug;
use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Exception;

class ImportProductsFromCsvCommand extends Command
{
    protected $signature = 'ecommerce:import-csv-products {--file=} {--create-categories} {--create-brands} {--update-existing}';

    protected $description = 'استيراد المنتجات من ملفات CSV الموجودة في المشروع';

    private array $categoryMapping = [];
    private array $brandMapping = [];
    private array $createdCategories = [];
    private array $createdBrands = [];
    private int $successCount = 0;
    private int $errorCount = 0;
    private array $errors = [];

    public function handle(): int
    {
        $this->info('=== بدء استيراد المنتجات من ملفات CSV ===');
        $this->newLine();

        $this->setupMappings();

        // إنشاء الفئات والعلامات التجارية إذا طُلب ذلك
        if ($this->option('create-categories')) {
            $this->createCategories();
        }

        if ($this->option('create-brands')) {
            $this->createBrands();
        }

        // تحديد ملفات CSV للاستيراد
        $csvFiles = $this->getCsvFiles();

        if (empty($csvFiles)) {
            $this->error('لا توجد ملفات CSV للاستيراد.');
            return self::FAILURE;
        }

        // استيراد المنتجات من كل ملف
        foreach ($csvFiles as $csvFile) {
            $this->info("معالجة ملف: {$csvFile}");
            $this->importFromCsv($csvFile);
            $this->newLine();
        }

        // عرض الإحصائيات النهائية
        $this->showStatistics();

        return self::SUCCESS;
    }

    private function setupMappings(): void
    {
        $this->categoryMapping = [
            'مشط استيرن' => 'مشط استيرن',
            'قاعدة فيت بم' => 'قاعدة فيت بم',
            'لاستيك رباط' => 'لاستيك رباط',
            'لاستيك درايم شفت' => 'لاستيك درايم شفت',
            'فيت بم' => 'فيت بم',
            'اذان محرك' => 'اذان محرك',
            'بوش' => 'بوش',
            'جوين راديتر' => 'جوين راديتر',
            'دبل هايدروليك' => 'دبل هايدروليك',
        ];

        $this->brandMapping = [
            'HF' => 'HF',
            'THC' => 'THC',
            'كوري' => 'كوري',
            'MOBIS 2' => 'MOBIS',
            'تجاري' => 'تجاري',
            'DEPO' => 'DEPO',
            'FPI' => 'FPI',
        ];
    }

    private function getCsvFiles(): array
    {
        $specifiedFile = $this->option('file');
        
        if ($specifiedFile) {
            if (file_exists($specifiedFile)) {
                return [$specifiedFile];
            } else {
                $this->error("الملف المحدد غير موجود: {$specifiedFile}");
                return [];
            }
        }

        // البحث عن ملفات CSV في المجلد الجذر
        $csvFiles = [
            'wc-product-export-14-6-2025-17498487497901.csv',
            'wc-product-export-14-6-2025-1749848893424.csv',
            'wc-product-export-14-6-2025-1749849086328.csv'
        ];

        $existingFiles = [];
        foreach ($csvFiles as $file) {
            if (file_exists(base_path($file))) {
                $existingFiles[] = base_path($file);
            }
        }

        return $existingFiles;
    }

    private function createCategories(): void
    {
        $this->info('إنشاء الفئات المطلوبة...');

        // إنشاء الفئة الرئيسية
        $mainCategory = $this->createCategory([
            'name' => 'قطع غيار السيارات',
            'description' => 'جميع قطع غيار السيارات',
            'is_featured' => true,
        ]);

        // إنشاء الفئات الفرعية
        foreach ($this->categoryMapping as $categoryName) {
            $this->createCategory([
                'name' => $categoryName,
                'description' => "فئة {$categoryName}",
                'parent_id' => $mainCategory->id,
            ]);
        }

        $this->info('تم إنشاء ' . count($this->createdCategories) . ' فئة.');
    }

    private function createCategory(array $data): ProductCategory
    {
        $category = ProductCategory::where('name', $data['name'])->first();

        if (!$category) {
            $category = new ProductCategory();
            $category->name = $data['name'];
            $category->description = $data['description'] ?? '';
            $category->status = BaseStatusEnum::PUBLISHED;
            $category->is_featured = $data['is_featured'] ?? false;

            // تعيين parent_id - استخدام 0 كقيمة افتراضية للفئات الرئيسية
            $category->parent_id = $data['parent_id'] ?? 0;

            $category->save();

            // إنشاء slug
            $slug = new Slug();
            $slug->key = Str::slug($category->name);
            $slug->reference_type = ProductCategory::class;
            $slug->reference_id = $category->id;
            $slug->prefix = 'product-categories';
            $slug->save();

            $this->createdCategories[] = $category->name;
            $this->line("تم إنشاء الفئة: {$category->name}");
        }

        return $category;
    }

    private function createBrands(): void
    {
        $this->info('إنشاء العلامات التجارية...');

        foreach ($this->brandMapping as $brandName) {
            if (empty($brandName)) continue;

            $brand = Brand::where('name', $brandName)->first();

            if (!$brand) {
                $brand = new Brand();
                $brand->name = $brandName;
                $brand->status = BaseStatusEnum::PUBLISHED;
                $brand->save();

                // إنشاء slug
                $slug = new Slug();
                $slug->key = Str::slug($brand->name);
                $slug->reference_type = Brand::class;
                $slug->reference_id = $brand->id;
                $slug->prefix = 'brands';
                $slug->save();

                $this->createdBrands[] = $brandName;
                $this->line("تم إنشاء العلامة التجارية: {$brandName}");
            }
        }

        $this->info('تم إنشاء ' . count($this->createdBrands) . ' علامة تجارية.');
    }

    private function importFromCsv(string $csvFile): void
    {
        $handle = fopen($csvFile, 'r');
        if (!$handle) {
            $this->error("لا يمكن فتح ملف CSV: {$csvFile}");
            return;
        }

        // قراءة العناوين
        $headers = fgetcsv($handle);
        if (!$headers) {
            $this->error("لا يمكن قراءة عناوين الأعمدة من ملف CSV");
            fclose($handle);
            return;
        }

        $rowNumber = 1;
        $fileSuccessCount = 0;
        $fileErrorCount = 0;

        $progressBar = $this->output->createProgressBar();
        $progressBar->start();

        while (($row = fgetcsv($handle)) !== false) {
            $rowNumber++;
            $progressBar->advance();

            try {
                $data = array_combine($headers, $row);
                $productData = $this->parseProductData($data);

                if ($productData && $this->importProduct($productData)) {
                    $fileSuccessCount++;
                    $this->successCount++;
                } else {
                    $fileErrorCount++;
                    $this->errorCount++;
                }
            } catch (Exception $e) {
                $fileErrorCount++;
                $this->errorCount++;
                $this->errors[] = "خطأ في السطر {$rowNumber} من ملف {$csvFile}: " . $e->getMessage();
            }
        }

        $progressBar->finish();
        $this->newLine();

        fclose($handle);

        $this->info("تم استيراد {$fileSuccessCount} منتج بنجاح من الملف.");
        if ($fileErrorCount > 0) {
            $this->warn("فشل استيراد {$fileErrorCount} منتج من الملف.");
        }
    }

    private function parseProductData(array $data): ?array
    {
        $name = trim($data['الاسم'] ?? '');
        if (empty($name)) {
            return null;
        }

        $price = $this->parsePrice($data['السعر الافتراضي'] ?? '0');
        $wholesalePrice = $this->parsePrice($data['بيانات ميتا: _wholesale_price'] ?? '0');

        return [
            'name' => $name,
            'description' => trim($data['وصف قصير'] ?? ''),
            'content' => trim($data['وصف قصير'] ?? ''),
            'sku' => $data['رمز المنتج (SKU)'] ?? $this->generateSku($name),
            'price' => $price,
            'sale_price' => $wholesalePrice > 0 && $wholesalePrice < $price ? $wholesalePrice : null,
            'status' => BaseStatusEnum::PUBLISHED,
            'is_featured' => ($data['بيانات ميتا: recommend_product'] ?? '0') == '1',
            'category' => $this->determineCategory($data),
            'brand' => $this->mapBrand($data['العلامات التجارية'] ?? ''),
            'tags' => $this->parseTags($data['الوسوم'] ?? ''),
            'images' => $this->parseImages($data['الصور'] ?? ''),
            'with_storehouse_management' => true,
            'quantity' => 100,
            'allow_checkout_when_out_of_stock' => true,
            'stock_status' => 'in_stock',
        ];
    }

    private function parsePrice(string $price): float
    {
        $price = str_replace([',', ' '], '', $price);
        return (float) $price;
    }

    private function generateSku(string $name): string
    {
        $sku = Str::slug($name, '-');
        $sku = substr($sku, 0, 20);

        $counter = 1;
        $originalSku = $sku;

        while (Product::where('sku', $sku)->exists()) {
            $sku = $originalSku . '-' . $counter;
            $counter++;
        }

        return $sku;
    }

    private function determineCategory(array $data): ?string
    {
        $name = $data['الاسم'] ?? '';
        $tags = $data['الوسوم'] ?? '';

        // تحديد الفئة بناءً على الوسوم أولاً، ثم اسم المنتج
        foreach ($this->categoryMapping as $keyword => $category) {
            if (str_contains($tags, $keyword) || str_contains($name, $keyword)) {
                return $category;
            }
        }

        // إذا لم نجد فئة محددة، نحاول تحديدها من اسم المنتج
        if (str_contains($name, 'مشط') || str_contains($name, 'استيرن')) {
            return 'مشط استيرن';
        }

        if (str_contains($name, 'قاعدة') && str_contains($name, 'فيت')) {
            return 'قاعدة فيت بم';
        }

        if (str_contains($name, 'لاستيك') && str_contains($name, 'رباط')) {
            return 'لاستيك رباط';
        }

        if (str_contains($name, 'لاستيك') && str_contains($name, 'درايم')) {
            return 'لاستيك درايم شفت';
        }

        if (str_contains($name, 'فيت') && str_contains($name, 'بم')) {
            return 'فيت بم';
        }

        if (str_contains($name, 'اذان') && str_contains($name, 'محرك')) {
            return 'اذان محرك';
        }

        if (str_contains($name, 'بوش')) {
            return 'بوش';
        }

        if (str_contains($name, 'جوين') && str_contains($name, 'راديتر')) {
            return 'جوين راديتر';
        }

        if (str_contains($name, 'دبل') && str_contains($name, 'هايدروليك')) {
            return 'دبل هايدروليك';
        }

        return 'قطع غيار السيارات';
    }

    private function mapBrand(string $brand): string
    {
        $brand = trim($brand);
        return $this->brandMapping[$brand] ?? $brand;
    }

    private function parseTags(string $tags): array
    {
        if (empty($tags)) {
            return [];
        }

        return array_map('trim', explode(',', $tags));
    }

    private function parseImages(string $images): array
    {
        if (empty($images)) {
            return [];
        }

        $imageUrls = array_map('trim', explode(',', $images));
        $validUrls = array_filter($imageUrls, function($url) {
            return filter_var($url, FILTER_VALIDATE_URL);
        });

        // تحميل الصور وحفظها محلياً
        $downloadedImages = [];
        foreach ($validUrls as $url) {
            $downloadedImage = $this->downloadImage($url);
            if ($downloadedImage) {
                $downloadedImages[] = $downloadedImage;
            }
        }

        return $downloadedImages;
    }

    /**
     * تحميل صورة من رابط وحفظها محلياً
     */
    private function downloadImage(string $url): ?string
    {
        try {
            // إنشاء اسم ملف فريد
            $extension = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION);
            if (empty($extension)) {
                $extension = 'jpg'; // افتراضي
            }

            $filename = 'product_' . time() . '_' . uniqid() . '.' . $extension;
            $uploadPath = 'storage/products/';
            $fullPath = public_path($uploadPath);

            // إنشاء المجلد إذا لم يكن موجوداً
            if (!file_exists($fullPath)) {
                mkdir($fullPath, 0755, true);
            }

            $filePath = $fullPath . $filename;

            // تحميل الصورة
            $imageContent = @file_get_contents($url);
            if ($imageContent === false) {
                $this->line("فشل تحميل الصورة: {$url}");
                return null;
            }

            // حفظ الصورة
            if (file_put_contents($filePath, $imageContent)) {
                $this->line("تم تحميل الصورة: {$filename}");
                return $uploadPath . $filename;
            }

        } catch (Exception $e) {
            $this->line("خطأ في تحميل الصورة {$url}: " . $e->getMessage());
        }

        return null;
    }

    private function importProduct(array $data): bool
    {
        try {
            DB::beginTransaction();

            // التحقق من وجود المنتج
            $existingProduct = Product::where('name', $data['name'])->first();
            if ($existingProduct && !$this->option('update-existing')) {
                DB::rollBack();
                return false;
            }

            // إنشاء أو تحديث المنتج
            $product = $existingProduct ?: new Product();

            // ربط العلامة التجارية أولاً
            if ($data['brand']) {
                $brand = Brand::where('name', $data['brand'])->first();
                if ($brand) {
                    $data['brand_id'] = $brand->id;
                }
            }

            // إزالة البيانات التي لا تنتمي للجدول الرئيسي
            $productData = $data;
            unset($productData['category'], $productData['brand'], $productData['tags'], $productData['images']);

            $product->fill($productData);
            $product->save();

            // ربط الفئة بعد حفظ المنتج
            if ($data['category']) {
                $category = ProductCategory::where('name', $data['category'])->first();
                if ($category) {
                    $product->categories()->sync([$category->id]);
                }
            }

            // ربط الوسوم
            if (!empty($data['tags'])) {
                $tagIds = [];
                foreach ($data['tags'] as $tagName) {
                    if (!empty($tagName)) {
                        $tag = ProductTag::firstOrCreate(['name' => $tagName], ['status' => BaseStatusEnum::PUBLISHED]);
                        $tagIds[] = $tag->id;
                    }
                }
                if (!empty($tagIds)) {
                    $product->tags()->sync($tagIds);
                }
            }

            // معالجة الصور
            if (!empty($data['images'])) {
                // تحويل مسارات الصور إلى تنسيق JSON المطلوب
                $imageArray = [];

                // التأكد من أن البيانات مصفوفة
                $images = is_array($data['images']) ? $data['images'] : [$data['images']];

                foreach ($images as $imagePath) {
                    if (!empty($imagePath) && is_string($imagePath)) {
                        $imageArray[] = $imagePath;
                    }
                }

                if (!empty($imageArray)) {
                    // التأكد من أن البيانات JSON صحيحة
                    $jsonImages = json_encode($imageArray);
                    if ($jsonImages !== false) {
                        $product->images = $jsonImages;
                        $product->save();
                        $this->line("تم ربط " . count($imageArray) . " صورة بالمنتج: {$product->name}");
                    } else {
                        $this->line("خطأ في تحويل الصور إلى JSON للمنتج: {$product->name}");
                    }
                }
            }

            // إنشاء slug
            if (!$existingProduct) {
                $slug = new Slug();
                $slug->key = Str::slug($product->name);
                $slug->reference_type = Product::class;
                $slug->reference_id = $product->id;
                $slug->prefix = 'products';
                $slug->save();
            }

            DB::commit();
            return true;

        } catch (Exception $e) {
            DB::rollBack();
            $this->errors[] = "خطأ في استيراد المنتج {$data['name']}: " . $e->getMessage();
            return false;
        }
    }

    private function showStatistics(): void
    {
        $this->newLine();
        $this->info('=== إحصائيات الاستيراد ===');
        $this->info("المنتجات المستوردة بنجاح: {$this->successCount}");
        $this->info("الأخطاء: {$this->errorCount}");
        $this->info("الفئات المنشأة: " . count($this->createdCategories));
        $this->info("العلامات التجارية المنشأة: " . count($this->createdBrands));

        if (!empty($this->errors)) {
            $this->newLine();
            $this->warn('تفاصيل الأخطاء:');
            foreach (array_slice($this->errors, 0, 10) as $error) {
                $this->line("- {$error}");
            }

            if (count($this->errors) > 10) {
                $this->line("... و " . (count($this->errors) - 10) . " أخطاء أخرى");
            }
        }
    }
}
