import 'package:flutter/material.dart';
import '../widgets/app_bar_widget.dart';
import '../widgets/banner_slider_widget.dart';
import '../widgets/vehicle_parts_finder_widget.dart';
import '../widgets/categories_grid_widget.dart';
import '../widgets/featured_products_widget.dart';
import '../widgets/latest_products_widget.dart';
import '../widgets/best_sellers_widget.dart';
import '../widgets/special_offers_widget.dart';
import '../widgets/bottom_navigation_widget.dart';
import '../services/product_service_new.dart';
import '../services/vehicle_parts_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  final ProductServiceNew _productService = ProductServiceNew();
  final VehiclePartsService _vehiclePartsService = VehiclePartsService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(),
      body: _buildBody(),
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
      ),
    );
  }

  Widget _buildBody() {
    switch (_currentIndex) {
      case 0:
        return _buildHomeContent();
      case 1:
        return _buildCategoriesContent();
      case 2:
        return _buildCartContent();
      case 3:
        return _buildProfileContent();
      default:
        return _buildHomeContent();
    }
  }

  Widget _buildHomeContent() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بانر السلايدر
            const BannerSliderWidget(),
            
            const SizedBox(height: 16),
            
            // باحث قطع الغيار - الميزة الأهم
            const VehiclePartsFinderWidget(),
            
            const SizedBox(height: 24),
            
            // الفئات الرئيسية
            _buildSectionTitle('الفئات الرئيسية'),
            const CategoriesGridWidget(),
            
            const SizedBox(height: 24),
            
            // المنتجات المميزة
            _buildSectionTitle('المنتجات المميزة'),
            const FeaturedProductsWidget(),
            
            const SizedBox(height: 24),
            
            // أحدث المنتجات
            _buildSectionTitle('أحدث المنتجات'),
            const LatestProductsWidget(),
            
            const SizedBox(height: 24),
            
            // الأكثر مبيعاً
            _buildSectionTitle('الأكثر مبيعاً'),
            const BestSellersWidget(),
            
            const SizedBox(height: 24),
            
            // العروض الخاصة
            _buildSectionTitle('العروض الخاصة'),
            const SpecialOffersWidget(),
            
            const SizedBox(height: 24),
            
            // معلومات إضافية
            _buildInfoSection(),
            
            const SizedBox(height: 100), // مساحة للـ bottom navigation
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.category, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'صفحة الفئات',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'قريباً سيتم إضافة جميع فئات المنتجات',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildCartContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.shopping_cart, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'سلة التسوق',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'سلة التسوق فارغة حالياً',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.person, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'الملف الشخصي',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'قم بتسجيل الدخول لعرض ملفك الشخصي',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(0xFF1976D2),
            ),
          ),
          TextButton(
            onPressed: () {
              // التنقل لصفحة عرض المزيد
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('عرض المزيد من $title'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            child: const Text('عرض المزيد'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF1976D2).withOpacity(0.1),
            const Color(0xFF4CAF50).withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF1976D2).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: const Color(0xFF1976D2),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'عن دليل',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF1976D2),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'دليل هو متجرك الإلكتروني المتخصص في قطع غيار السيارات. نوفر لك أفضل المنتجات بأسعار تنافسية مع خدمة عملاء متميزة.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildInfoItem(Icons.verified, 'منتجات أصلية'),
              const SizedBox(width: 20),
              _buildInfoItem(Icons.local_shipping, 'توصيل سريع'),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildInfoItem(Icons.support_agent, 'دعم 24/7'),
              const SizedBox(width: 20),
              _buildInfoItem(Icons.payment, 'دفع آمن'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: const Color(0xFF4CAF50),
        ),
        const SizedBox(width: 6),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Future<void> _refreshData() async {
    // محاكاة تحديث البيانات
    await Future.delayed(const Duration(seconds: 1));
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديث البيانات بنجاح'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
}
