<?php

namespace Botble\Ecommerce\Services\Products;

use Botble\Ecommerce\Models\Product;
use Illuminate\Support\Facades\Pipeline;

class ProductPriceService
{
    protected array $priceHandlers = [
        ProductSalePriceService::class,
        ProductFlashSalePriceService::class,
        ProductDiscountPriceService::class,
        ProductCrossSalePriceService::class,
    ];

    public function __construct(
        protected float $finalPrice = 0,
        protected ?Product $product = null
    ) {
    }

    public function getPrice(Product $product): float
    {
        $this->product = $product;

        // التحقق من وجود سعر للمنتج، إذا لم يكن موجوداً نرجع 0
        $productPrice = (float) ($product->price ?? 0);
        $wholesalePrice = (float) ($product->wholesale_price ?? 0);

        if (auth('customer')->check() && auth('customer')->user()->is_wholesale == 1 && $wholesalePrice > 0) {
            $this->product->setFinalPrice($wholesalePrice);
        } else {
            $this->product->setFinalPrice($productPrice);
        }

        $this->applyPriceHandlers();

        return $this->product->getFinalPrice() ?? 0.0;
    }

    public function getOriginalPrice(Product $product): float
    {
        $this->product = $product;

        // التحقق من وجود سعر للمنتج، إذا لم يكن موجوداً نرجع 0
        $productPrice = (float) ($product->price ?? 0);
        $wholesalePrice = (float) ($product->wholesale_price ?? 0);

        if (auth('customer')->check() && auth('customer')->user()->is_wholesale == 1 && $wholesalePrice > 0) {
            $this->product->setOriginalPrice($wholesalePrice);
            $this->applyPriceHandlers();
            return $this->product->getOriginalPrice() ?? $wholesalePrice;
        } else {
            // للعملاء العاديين: نحتاج للحصول على السعر الأصلي قبل تطبيق أي خصومات
            $originalPriceBeforeHandlers = $productPrice;

            $this->product->setOriginalPrice($productPrice);
            $this->product->setFinalPrice($productPrice);

            $this->applyPriceHandlers();

            // للعملاء العاديين، السعر الأصلي يجب أن يكون دائماً السعر الأساسي للمنتج
            // بغض النظر عن الخصومات المطبقة
            return $originalPriceBeforeHandlers;
        }
    }

    protected function applyPriceHandlers(): Product
    {
        return Pipeline::send($this->product)
            ->through($this->priceHandlers)
            ->thenReturn();
    }
}
