@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-ad me-2"></i>
                        إدارة البانر المصغر
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('revo-woocommerce.mini-banner.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة بانر جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" role="alert">
                        <i class="ti ti-info-circle me-2"></i>
                        <strong>معلومة:</strong> البانرات المصغرة تظهر في مواقع مختلفة داخل التطبيق.
                    </div>

                    @if(count($banners) > 0)
                        <div class="row">
                            @foreach($banners as $banner)
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card border-0 shadow-sm">
                                        <img src="{{ $banner['image'] }}" class="card-img-top" alt="{{ $banner['title'] }}" 
                                             style="height: 150px; object-fit: cover;">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ $banner['title'] ?: 'بدون عنوان' }}</h6>
                                            
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="badge bg-secondary">{{ ucfirst($banner['position']) }}</span>
                                                @if($banner['status'])
                                                    <span class="badge bg-success">مفعل</span>
                                                @else
                                                    <span class="badge bg-danger">معطل</span>
                                                @endif
                                            </div>
                                            
                                            @if($banner['link'])
                                                <p class="card-text">
                                                    <small class="text-muted">
                                                        <i class="ti ti-external-link me-1"></i>
                                                        {{ Str::limit($banner['link'], 30) }}
                                                    </small>
                                                </p>
                                            @endif
                                            
                                            <div class="btn-group w-100" role="group">
                                                <a href="{{ route('revo-woocommerce.mini-banner.edit', $banner['id']) }}" 
                                                   class="btn btn-sm btn-warning">
                                                    <i class="ti ti-edit"></i>
                                                    تعديل
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteBanner({{ $banner['id'] }})">
                                                    <i class="ti ti-trash"></i>
                                                    حذف
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-ad" style="font-size: 4rem; color: #ccc;"></i>
                            <h5 class="mt-3">لا توجد بانرات</h5>
                            <p class="text-muted">ابدأ بإضافة أول بانر مصغر</p>
                            <a href="{{ route('revo-woocommerce.mini-banner.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-1"></i>
                                إضافة بانر جديد
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    function deleteBanner(id) {
        if (confirm('هل أنت متأكد من حذف هذا البانر؟')) {
            $.ajax({
                url: '/admin/revo-woocommerce/mini-banner/' + id,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        location.reload();
                    }
                },
                error: function() {
                    Botble.showError('حدث خطأ أثناء حذف البانر');
                }
            });
        }
    }
</script>
@endpush
