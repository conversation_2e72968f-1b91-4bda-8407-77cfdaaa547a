import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/splash/screens/splash_screen.dart';
import '../../features/onboarding/screens/onboarding_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../features/vehicle_parts/screens/vehicle_parts_finder_screen.dart';
import '../../features/vehicle_parts/screens/vehicle_selection_screen.dart';
import '../../features/vehicle_parts/screens/parts_results_screen.dart';
import '../../features/vehicle_parts/screens/my_vehicles_screen.dart';
import '../../features/products/screens/products_screen.dart';
import '../../features/products/screens/product_details_screen.dart';
import '../../features/cart/screens/cart_screen.dart';
import '../../features/cart/screens/checkout_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/profile/screens/edit_profile_screen.dart';
import '../../features/profile/screens/settings_screen.dart';
import '../../features/orders/screens/orders_screen.dart';
import '../../features/orders/screens/order_details_screen.dart';
import '../../features/search/screens/search_screen.dart';
import '../../features/favorites/screens/favorites_screen.dart';
import '../services/storage_service.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/splash',
    redirect: (context, state) async {
      // التحقق من حالة تسجيل الدخول
      final hasToken = await StorageService.hasToken();
      final isOnboardingCompleted = await StorageService.getSetting<bool>(
        'onboarding_completed', 
        defaultValue: false,
      );

      // إذا كان في صفحة البداية
      if (state.location == '/splash') {
        return null; // السماح بالوصول لصفحة البداية
      }

      // إذا لم يكمل التعريف بالتطبيق
      if (!isOnboardingCompleted! && state.location != '/onboarding') {
        return '/onboarding';
      }

      // إذا لم يسجل دخول ويحاول الوصول لصفحة محمية
      if (!hasToken && _isProtectedRoute(state.location)) {
        return '/login';
      }

      // إذا سجل دخول ويحاول الوصول لصفحة المصادقة
      if (hasToken && _isAuthRoute(state.location)) {
        return '/home';
      }

      return null; // لا توجد إعادة توجيه
    },
    routes: [
      // صفحة البداية
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // صفحة التعريف بالتطبيق
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),

      // صفحات المصادقة
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),

      // الصفحة الرئيسية
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),

      // Vehicle Parts Finder
      GoRoute(
        path: '/vehicle-parts-finder',
        name: 'vehicle-parts-finder',
        builder: (context, state) => const VehiclePartsFinderScreen(),
        routes: [
          GoRoute(
            path: '/selection',
            name: 'vehicle-selection',
            builder: (context, state) => const VehicleSelectionScreen(),
          ),
          GoRoute(
            path: '/results',
            name: 'parts-results',
            builder: (context, state) {
              final extra = state.extra as Map<String, dynamic>?;
              return PartsResultsScreen(
                vehicleData: extra?['vehicleData'],
                searchQuery: extra?['searchQuery'],
              );
            },
          ),
          GoRoute(
            path: '/my-vehicles',
            name: 'my-vehicles',
            builder: (context, state) => const MyVehiclesScreen(),
          ),
        ],
      ),

      // المنتجات
      GoRoute(
        path: '/products',
        name: 'products',
        builder: (context, state) {
          final categoryId = state.queryParameters['category'];
          return ProductsScreen(categoryId: categoryId);
        },
      ),
      GoRoute(
        path: '/product/:id',
        name: 'product-details',
        builder: (context, state) {
          final productId = int.parse(state.pathParameters['id']!);
          return ProductDetailsScreen(productId: productId);
        },
      ),

      // السلة والطلبات
      GoRoute(
        path: '/cart',
        name: 'cart',
        builder: (context, state) => const CartScreen(),
      ),
      GoRoute(
        path: '/checkout',
        name: 'checkout',
        builder: (context, state) => const CheckoutScreen(),
      ),
      GoRoute(
        path: '/orders',
        name: 'orders',
        builder: (context, state) => const OrdersScreen(),
      ),
      GoRoute(
        path: '/order/:id',
        name: 'order-details',
        builder: (context, state) {
          final orderId = int.parse(state.pathParameters['id']!);
          return OrderDetailsScreen(orderId: orderId);
        },
      ),

      // البحث والمفضلة
      GoRoute(
        path: '/search',
        name: 'search',
        builder: (context, state) {
          final query = state.queryParameters['q'];
          return SearchScreen(initialQuery: query);
        },
      ),
      GoRoute(
        path: '/favorites',
        name: 'favorites',
        builder: (context, state) => const FavoritesScreen(),
      ),

      // الملف الشخصي
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
        routes: [
          GoRoute(
            path: '/edit',
            name: 'edit-profile',
            builder: (context, state) => const EditProfileScreen(),
          ),
          GoRoute(
            path: '/settings',
            name: 'settings',
            builder: (context, state) => const SettingsScreen(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('خطأ')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'الصفحة غير موجودة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'المسار: ${state.location}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );

  // التحقق من المسارات المحمية
  static bool _isProtectedRoute(String location) {
    final protectedRoutes = [
      '/profile',
      '/cart',
      '/checkout',
      '/orders',
      '/favorites',
      '/vehicle-parts-finder/my-vehicles',
    ];
    
    return protectedRoutes.any((route) => location.startsWith(route));
  }

  // التحقق من مسارات المصادقة
  static bool _isAuthRoute(String location) {
    final authRoutes = [
      '/login',
      '/register',
      '/forgot-password',
      '/onboarding',
    ];
    
    return authRoutes.contains(location);
  }
}
