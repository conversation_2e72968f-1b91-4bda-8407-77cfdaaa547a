import '../models/product.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import '../utils/storage_helper.dart';

class ProductServiceNew {
  static final ProductServiceNew _instance = ProductServiceNew._internal();
  factory ProductServiceNew() => _instance;
  ProductServiceNew._internal();

  final ApiService _apiService = ApiService();

  // === المنتجات ===

  // الحصول على قائمة المنتجات مع فلترة متقدمة
  Future<ApiResponse<PaginatedResponse<Product>>> getProducts({
    int page = 1,
    int perPage = 12,
    String? search,
    List<int>? categoryIds,
    List<int>? brandIds,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
    bool? onSale,
    bool? inStock,
    bool? featured,
    bool? isNew,
  }) async {
    try {
      final response = await _apiService.getProducts(
        page: page,
        perPage: perPage,
        search: search,
        categoryIds: categoryIds,
        brandIds: brandIds,
        minPrice: minPrice,
        maxPrice: maxPrice,
        sortBy: sortBy,
      );

      if (response.isSuccess && response.data != null) {
        final paginatedResponse = PaginatedResponse.fromJson(
          response.data!,
          (json) => Product.fromJson(json),
        );
        return ApiResponse.success(paginatedResponse);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل المنتجات');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل المنتجات: ${e.toString()}');
    }
  }

  // الحصول على تفاصيل منتج واحد
  Future<ApiResponse<Product>> getProduct(String slug) async {
    try {
      final response = await _apiService.getProduct(slug);

      if (response.isSuccess && response.data != null) {
        final product = Product.fromJson(response.data!);
        return ApiResponse.success(product);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل تفاصيل المنتج');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل المنتج: ${e.toString()}');
    }
  }

  // الحصول على المنتجات ذات الصلة
  Future<ApiResponse<List<Product>>> getRelatedProducts(String slug) async {
    try {
      final response = await _apiService.getRelatedProducts(slug);

      if (response.isSuccess && response.data != null) {
        final products = (response.data!['data'] as List?)
            ?.map((json) => Product.fromJson(json))
            .toList() ?? [];
        return ApiResponse.success(products);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل المنتجات ذات الصلة');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل المنتجات ذات الصلة: ${e.toString()}');
    }
  }

  // الحصول على المنتجات المميزة
  Future<ApiResponse<List<Product>>> getFeaturedProducts({int limit = 10}) async {
    try {
      final response = await getProducts(
        perPage: limit,
        featured: true,
        sortBy: 'featured',
      );

      if (response.isSuccess && response.data != null) {
        return ApiResponse.success(response.data!.data);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل المنتجات المميزة');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل المنتجات المميزة: ${e.toString()}');
    }
  }

  // الحصول على أحدث المنتجات
  Future<ApiResponse<List<Product>>> getLatestProducts({int limit = 10}) async {
    try {
      final response = await getProducts(
        perPage: limit,
        sortBy: 'latest',
      );

      if (response.isSuccess && response.data != null) {
        return ApiResponse.success(response.data!.data);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل أحدث المنتجات');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل أحدث المنتجات: ${e.toString()}');
    }
  }

  // الحصول على المنتجات الأكثر مبيعاً
  Future<ApiResponse<List<Product>>> getBestSellingProducts({int limit = 10}) async {
    try {
      final response = await getProducts(
        perPage: limit,
        sortBy: 'best_selling',
      );

      if (response.isSuccess && response.data != null) {
        return ApiResponse.success(response.data!.data);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل المنتجات الأكثر مبيعاً');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل المنتجات الأكثر مبيعاً: ${e.toString()}');
    }
  }

  // الحصول على المنتجات المخفضة
  Future<ApiResponse<List<Product>>> getSaleProducts({int limit = 10}) async {
    try {
      final response = await getProducts(
        perPage: limit,
        onSale: true,
        sortBy: 'discount',
      );

      if (response.isSuccess && response.data != null) {
        return ApiResponse.success(response.data!.data);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل المنتجات المخفضة');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل المنتجات المخفضة: ${e.toString()}');
    }
  }

  // === الفئات ===

  // الحصول على قائمة الفئات
  Future<ApiResponse<List<Category>>> getCategories({
    int? parentId,
    bool includeChildren = false,
  }) async {
    try {
      final response = await _apiService.getCategories(
        parentId: parentId,
        includeChildren: includeChildren,
      );

      if (response.isSuccess && response.data != null) {
        final categories = (response.data!['data'] as List?)
            ?.map((json) => Category.fromJson(json))
            .toList() ?? [];
        return ApiResponse.success(categories);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل الفئات');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل الفئات: ${e.toString()}');
    }
  }

  // الحصول على الفئات الرئيسية
  Future<ApiResponse<List<Category>>> getMainCategories() async {
    return getCategories(parentId: null);
  }

  // الحصول على تفاصيل فئة واحدة
  Future<ApiResponse<Category>> getCategory(String slug) async {
    try {
      final response = await _apiService.getCategory(slug);

      if (response.isSuccess && response.data != null) {
        final category = Category.fromJson(response.data!);
        return ApiResponse.success(category);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل تفاصيل الفئة');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل الفئة: ${e.toString()}');
    }
  }

  // === العلامات التجارية ===

  // الحصول على قائمة العلامات التجارية
  Future<ApiResponse<List<Brand>>> getBrands() async {
    try {
      final response = await _apiService.getBrands();

      if (response.isSuccess && response.data != null) {
        final brands = (response.data!['data'] as List?)
            ?.map((json) => Brand.fromJson(json))
            .toList() ?? [];
        return ApiResponse.success(brands);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل العلامات التجارية');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل العلامات التجارية: ${e.toString()}');
    }
  }

  // === البحث المتقدم ===

  // البحث في المنتجات
  Future<ApiResponse<SearchResponse<Product>>> searchProducts({
    required String query,
    int page = 1,
    int perPage = 12,
    List<int>? categoryIds,
    List<int>? brandIds,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
  }) async {
    try {
      final response = await getProducts(
        page: page,
        perPage: perPage,
        search: query,
        categoryIds: categoryIds,
        brandIds: brandIds,
        minPrice: minPrice,
        maxPrice: maxPrice,
        sortBy: sortBy,
      );

      if (response.isSuccess && response.data != null) {
        final searchResponse = SearchResponse<Product>(
          results: response.data!.data,
          total: response.data!.total,
          query: query,
        );
        return ApiResponse.success(searchResponse);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في البحث');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في البحث: ${e.toString()}');
    }
  }

  // === المفضلة المحلية ===

  // إضافة منتج للمفضلة محلياً
  Future<bool> addToLocalWishlist(int productId) async {
    try {
      return await StorageHelper.addToWishlist(productId);
    } catch (e) {
      return false;
    }
  }

  // حذف منتج من المفضلة محلياً
  Future<bool> removeFromLocalWishlist(int productId) async {
    try {
      return await StorageHelper.removeFromWishlist(productId);
    } catch (e) {
      return false;
    }
  }

  // التحقق من وجود منتج في المفضلة محلياً
  Future<bool> isInLocalWishlist(int productId) async {
    try {
      return await StorageHelper.isInWishlist(productId);
    } catch (e) {
      return false;
    }
  }

  // الحصول على المفضلة المحلية
  Future<List<int>> getLocalWishlist() async {
    try {
      return await StorageHelper.getWishlist();
    } catch (e) {
      return [];
    }
  }

  // === دوال مساعدة ===

  // تنسيق السعر
  String formatPrice(double price) {
    return '${price.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} د.ع';
  }

  // حساب نسبة الخصم
  double calculateDiscountPercentage(double originalPrice, double salePrice) {
    if (originalPrice <= 0 || salePrice >= originalPrice) return 0;
    return ((originalPrice - salePrice) / originalPrice) * 100;
  }

  // التحقق من توفر المنتج
  bool isProductAvailable(Product product) {
    return product.inStock && 
           (product.stockQuantity == null || product.stockQuantity! > 0);
  }

  // الحصول على حالة المخزون
  String getStockStatus(Product product) {
    if (!product.inStock) return 'غير متوفر';
    if (product.stockQuantity == null) return 'متوفر';
    if (product.stockQuantity! <= 0) return 'غير متوفر';
    if (product.stockQuantity! <= 5) return 'كمية محدودة';
    return 'متوفر';
  }
}
