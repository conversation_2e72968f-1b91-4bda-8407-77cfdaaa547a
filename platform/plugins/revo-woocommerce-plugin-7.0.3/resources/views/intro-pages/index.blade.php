@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-presentation me-2"></i>
                        إدارة صفحات التعريف
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('revo-woocommerce.intro-pages.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة صفحة جديدة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" role="alert">
                        <i class="ti ti-info-circle me-2"></i>
                        <strong>معلومة:</strong> صفحات التعريف تظهر للمستخدمين الجدد عند فتح التطبيق لأول مرة.
                    </div>

                    @if(count($introPages) > 0)
                        <div class="row">
                            @foreach($introPages as $page)
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card border-0 shadow-sm">
                                        <img src="{{ $page['image'] }}" class="card-img-top" alt="{{ $page['title'] }}" 
                                             style="height: 200px; object-fit: cover;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <span class="badge bg-secondary">{{ $page['order'] }}</span>
                                                @if($page['status'])
                                                    <span class="badge bg-success">مفعل</span>
                                                @else
                                                    <span class="badge bg-danger">معطل</span>
                                                @endif
                                            </div>
                                            
                                            <h6 class="card-title">{{ $page['title'] }}</h6>
                                            <p class="card-text text-muted small">
                                                {{ Str::limit($page['description'], 100) }}
                                            </p>
                                            
                                            <div class="btn-group w-100" role="group">
                                                <a href="{{ route('revo-woocommerce.intro-pages.edit', $page['id']) }}" 
                                                   class="btn btn-sm btn-warning">
                                                    <i class="ti ti-edit"></i>
                                                    تعديل
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteIntroPage({{ $page['id'] }})">
                                                    <i class="ti ti-trash"></i>
                                                    حذف
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-presentation" style="font-size: 4rem; color: #ccc;"></i>
                            <h5 class="mt-3">لا توجد صفحات تعريف</h5>
                            <p class="text-muted">ابدأ بإضافة أول صفحة تعريف للمستخدمين الجدد</p>
                            <a href="{{ route('revo-woocommerce.intro-pages.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-1"></i>
                                إضافة صفحة جديدة
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    function deleteIntroPage(id) {
        if (confirm('هل أنت متأكد من حذف هذه الصفحة؟')) {
            $.ajax({
                url: '/admin/revo-woocommerce/intro-pages/' + id,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        location.reload();
                    }
                },
                error: function() {
                    Botble.showError('حدث خطأ أثناء حذف الصفحة');
                }
            });
        }
    }
</script>
@endpush
