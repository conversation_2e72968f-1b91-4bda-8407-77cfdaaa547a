<?php

namespace Botble\Base\Forms\FieldOptions;

use Botble\Base\Facades\BaseHelper;
use Botble\Base\Forms\FormFieldOptions;

class SlugFieldOption extends FormFieldOptions
{
    public function from(string $from = 'name'): static
    {
        $this->addAttribute('data-slug-from', $from);

        return $this;
    }

    public function prefixLabel(string $label = '', bool $translation = true): static
    {
        if ($translation) {
            $label = trans($label);
        }

        $this->addAttribute('data-prefix-label', $label);

        return $this;
    }

    public function getHtmlAttributes(): array
    {
        $this->addAttribute('class', 'form-control is-slug');
        $this->addAttribute('data-counter', 150);
        
        return parent::getHtmlAttributes();
    }
} 