name: dalil_mobile_app
description: "تطبيق دليل لقطع غيار السيارات - Dalil Auto Parts Mobile App"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  flutter_staggered_grid_view: ^0.7.0
  carousel_slider: ^4.2.1
  smooth_page_indicator: ^1.1.0

  # State Management
  provider: ^6.1.1
  get: ^4.6.6

  # HTTP & API
  http: ^1.1.2
  dio: ^5.4.0
  pretty_dio_logger: ^1.3.1

  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Authentication & Security
  local_auth: ^2.1.7
  crypto: ^3.0.3

  # Navigation
  go_router: ^12.1.3

  # Utils & Localization
  intl: ^0.20.2
  easy_localization: ^3.0.3
  url_launcher: ^6.2.2
  image_picker: ^1.0.4
  permission_handler: ^11.1.0
  connectivity_plus: ^5.0.2
  package_info_plus: ^4.2.0


  # Maps & Location
  geolocator: ^10.1.0
  geocoding: ^2.1.1

  # Notifications
  flutter_local_notifications: ^16.3.0

  # QR Code
  qr_code_scanner: ^1.0.1
  qr_flutter: ^4.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.1
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/logos/
    - assets/translations/

  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
  #       - asset: assets/fonts/Cairo-Bold.ttf
  #         weight: 700
