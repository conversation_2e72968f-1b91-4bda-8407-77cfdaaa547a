import 'package:flutter/material.dart';
import 'featured_products_widget.dart';

class LatestProductsWidget extends StatelessWidget {
  const LatestProductsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final products = _getLatestProducts();
    
    return Container(
      height: 280,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: products.length,
        itemBuilder: (context, index) {
          return _buildProductCard(context, products[index]);
        },
      ),
    );
  }

  Widget _buildProductCard(BuildContext context, ProductData product) {
    return GestureDetector(
      onTap: () => _onProductTap(context, product),
      child: Container(
        width: 200,
        margin: const EdgeInsets.only(left: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المنتج
            Container(
              height: 140,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.new_releases,
                      size: 60,
                      color: Colors.grey[400],
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'جديد',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // معلومات المنتج
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      product.brand,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${product.price.toStringAsFixed(0)} د.ع',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1976D2),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<ProductData> _getLatestProducts() {
    return [
      ProductData(
        name: 'مصابيح LED الجديدة',
        brand: 'فيليبس',
        price: 35000,
        originalPrice: 0,
        discount: 0,
        rating: 5,
        reviewCount: 8,
        isFavorite: false,
      ),
      ProductData(
        name: 'كاميرا خلفية ذكية',
        brand: 'بوش',
        price: 150000,
        originalPrice: 0,
        discount: 0,
        rating: 4,
        reviewCount: 3,
        isFavorite: false,
      ),
      ProductData(
        name: 'نظام ملاحة GPS',
        brand: 'جارمن',
        price: 200000,
        originalPrice: 0,
        discount: 0,
        rating: 5,
        reviewCount: 12,
        isFavorite: true,
      ),
    ];
  }

  void _onProductTap(BuildContext context, ProductData product) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض تفاصيل: ${product.name}'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
