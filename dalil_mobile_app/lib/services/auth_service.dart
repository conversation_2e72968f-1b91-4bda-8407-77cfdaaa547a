import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';
  static const String _userTypeKey = 'user_type';

  // تسجيل الدخول
  static Future<AuthResult> login({
    required String phone,
    required String password,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/login'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: {
          'phone': phone,
          'password': password,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 302) {
        // Laravel يعيد redirect عند نجاح تسجيل الدخول
        // نحتاج لحفظ بيانات المستخدم محلياً
        final userData = {
          'phone': phone,
          'name': 'المستخدم', // سيتم تحديثه لاحقاً من API
          'user_type': 'individual', // افتراضي
        };
        
        await _saveUserData(userData);
        await _saveToken('logged_in'); // token مؤقت
        
        return AuthResult(success: true, user: userData);
      } else {
        return AuthResult(
          success: false,
          errorMessage: 'خطأ في رقم الهاتف أو كلمة المرور',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        errorMessage: 'خطأ في الاتصال بالخادم',
      );
    }
  }

  // تسجيل حساب جديد
  static Future<AuthResult> register({
    required String name,
    required String phone,
    required String password,
    required String passwordConfirmation,
    String? address,
    String userType = 'individual', // individual أو wholesale
  }) async {
    try {
      final body = {
        'name': name,
        'phone': phone,
        'password': password,
        'password_confirmation': passwordConfirmation,
      };

      // إضافة بيانات إضافية لعملاء الجملة
      if (userType == 'wholesale') {
        body['is_wholesale'] = '1';
        if (address != null && address.isNotEmpty) {
          body['address'] = address;
        }
      }

      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/register'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: body,
      );

      if (response.statusCode == 200 || response.statusCode == 302) {
        // Laravel يعيد redirect عند نجاح التسجيل
        final userData = {
          'name': name,
          'phone': phone,
          'user_type': userType,
          'address': address ?? '',
        };
        
        await _saveUserData(userData);
        await _saveToken('logged_in');
        
        return AuthResult(success: true, user: userData);
      } else {
        // محاولة استخراج رسالة الخطأ من الاستجابة
        String errorMessage = 'خطأ في التسجيل';
        try {
          final responseData = json.decode(response.body);
          if (responseData['message'] != null) {
            errorMessage = responseData['message'];
          } else if (responseData['errors'] != null) {
            final errors = responseData['errors'] as Map<String, dynamic>;
            errorMessage = errors.values.first.first;
          }
        } catch (e) {
          // إذا فشل في تحليل JSON، استخدم رسالة افتراضية
        }
        
        return AuthResult(
          success: false,
          errorMessage: errorMessage,
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        errorMessage: 'خطأ في الاتصال بالخادم',
      );
    }
  }

  // تسجيل الدخول بـ OTP
  static Future<AuthResult> loginWithOtp({
    required String phone,
  }) async {
    try {
      // إرسال OTP
      final sendResponse = await http.post(
        Uri.parse('${AppConfig.baseUrl}/customer/otp/send'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: {
          'phone': phone,
        },
      );

      if (sendResponse.statusCode == 200) {
        return AuthResult(
          success: true,
          message: 'تم إرسال رمز التحقق إلى رقم هاتفك',
          data: {'phone': phone},
        );
      } else {
        return AuthResult(
          success: false,
          errorMessage: 'خطأ في إرسال رمز التحقق',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        errorMessage: 'خطأ في الاتصال بالخادم',
      );
    }
  }

  // تأكيد OTP
  static Future<AuthResult> verifyOtp({
    required String phone,
    required String otp,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/customer/otp/verify'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: {
          'phone': phone,
          'otp': otp,
        },
      );

      if (response.statusCode == 200) {
        final userData = {
          'phone': phone,
          'name': 'المستخدم',
          'user_type': 'individual',
        };
        
        await _saveUserData(userData);
        await _saveToken('logged_in');
        
        return AuthResult(success: true, user: userData);
      } else {
        return AuthResult(
          success: false,
          errorMessage: 'رمز التحقق غير صحيح',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        errorMessage: 'خطأ في الاتصال بالخادم',
      );
    }
  }

  // تسجيل الخروج
  static Future<void> logout() async {
    try {
      await http.get(
        Uri.parse('${AppConfig.baseUrl}/customer/logout'),
        headers: AppConfig.defaultHeaders,
      );
    } catch (e) {
      // تجاهل الأخطاء في تسجيل الخروج من الخادم
    }
    
    // حذف البيانات المحلية
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_userKey);
    await prefs.remove(_userTypeKey);
  }

  // فحص حالة تسجيل الدخول
  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey) != null;
  }

  // الحصول على بيانات المستخدم المحفوظة
  static Future<Map<String, dynamic>?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_userKey);
    if (userJson != null) {
      return json.decode(userJson);
    }
    return null;
  }

  // الحصول على نوع المستخدم
  static Future<String> getUserType() async {
    final user = await getCurrentUser();
    return user?['user_type'] ?? 'individual';
  }

  // فحص إذا كان المستخدم عميل جملة
  static Future<bool> isWholesaleCustomer() async {
    final userType = await getUserType();
    return userType == 'wholesale';
  }

  // حفظ بيانات المستخدم
  static Future<void> _saveUserData(Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, json.encode(userData));
    await prefs.setString(_userTypeKey, userData['user_type'] ?? 'individual');
  }

  // حفظ التوكن
  static Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  // الحصول على التوكن
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }
}

// نتيجة عملية المصادقة
class AuthResult {
  final bool success;
  final String? errorMessage;
  final String? message;
  final Map<String, dynamic>? user;
  final Map<String, dynamic>? data;

  AuthResult({
    required this.success,
    this.errorMessage,
    this.message,
    this.user,
    this.data,
  });
}
