import 'package:flutter/material.dart';
import '../../../core/services/api_service.dart';
import '../../../core/services/storage_service.dart';
import '../models/banner_model.dart';
import '../models/category_model.dart';
import '../models/product_model.dart';

class HomeProvider extends ChangeNotifier {
  // حالة التحميل
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // حالة الخطأ
  bool _hasError = false;
  bool get hasError => _hasError;
  String _errorMessage = '';
  String get errorMessage => _errorMessage;

  // البيانات
  List<BannerModel> _banners = [];
  List<BannerModel> get banners => _banners;

  List<CategoryModel> _categories = [];
  List<CategoryModel> get categories => _categories;

  List<ProductModel> _featuredProducts = [];
  List<ProductModel> get featuredProducts => _featuredProducts;

  List<ProductModel> _recommendedProducts = [];
  List<ProductModel> get recommendedProducts => _recommendedProducts;

  List<String> _recentSearches = [];
  List<String> get recentSearches => _recentSearches;

  // إحصائيات
  Map<String, dynamic> _stats = {};
  Map<String, dynamic> get stats => _stats;

  HomeProvider() {
    _loadCachedData();
  }

  // تحميل البيانات المحفوظة محلياً
  Future<void> _loadCachedData() async {
    try {
      // تحميل البحثات الأخيرة
      _recentSearches = await StorageService.getSearchHistory();
      
      // تحميل البيانات المحفوظة في الكاش
      final cachedBanners = await StorageService.getCache<List>('home_banners');
      if (cachedBanners != null) {
        _banners = cachedBanners.map((json) => BannerModel.fromJson(json)).toList();
      }

      final cachedCategories = await StorageService.getCache<List>('home_categories');
      if (cachedCategories != null) {
        _categories = cachedCategories.map((json) => CategoryModel.fromJson(json)).toList();
      }

      final cachedProducts = await StorageService.getCache<List>('featured_products');
      if (cachedProducts != null) {
        _featuredProducts = cachedProducts.map((json) => ProductModel.fromJson(json)).toList();
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading cached data: $e');
    }
  }

  // تحميل بيانات الصفحة الرئيسية
  Future<void> loadHomeData() async {
    _setLoading(true);
    _setError(false, '');

    try {
      // تحميل البانرات
      await _loadBanners();
      
      // تحميل الفئات
      await _loadCategories();
      
      // تحميل المنتجات المميزة
      await _loadFeaturedProducts();
      
      // تحميل المنتجات المقترحة
      await _loadRecommendedProducts();
      
      // تحميل الإحصائيات
      await _loadStats();

    } catch (e) {
      _setError(true, 'حدث خطأ أثناء تحميل البيانات');
      debugPrint('Error loading home data: $e');
    } finally {
      _setLoading(false);
    }
  }

  // تحميل البانرات الترويجية
  Future<void> _loadBanners() async {
    try {
      final response = await ApiService.instance.get('/banners');
      
      if (response.success && response.data != null) {
        final bannersData = response.data as List;
        _banners = bannersData.map((json) => BannerModel.fromJson(json)).toList();
        
        // حفظ في الكاش
        await StorageService.saveCache(
          'home_banners',
          bannersData,
          expiry: const Duration(hours: 6),
        );
      }
    } catch (e) {
      debugPrint('Error loading banners: $e');
    }
  }

  // تحميل الفئات الرئيسية
  Future<void> _loadCategories() async {
    try {
      final response = await ApiService.instance.get('/categories');
      
      if (response.success && response.data != null) {
        final categoriesData = response.data as List;
        _categories = categoriesData.map((json) => CategoryModel.fromJson(json)).toList();
        
        // حفظ في الكاش
        await StorageService.saveCache(
          'home_categories',
          categoriesData,
          expiry: const Duration(hours: 12),
        );
      }
    } catch (e) {
      debugPrint('Error loading categories: $e');
    }
  }

  // تحميل المنتجات المميزة
  Future<void> _loadFeaturedProducts() async {
    try {
      final response = await ApiService.instance.get('/products/featured');
      
      if (response.success && response.data != null) {
        final productsData = response.data as List;
        _featuredProducts = productsData.map((json) => ProductModel.fromJson(json)).toList();
        
        // حفظ في الكاش
        await StorageService.saveCache(
          'featured_products',
          productsData,
          expiry: const Duration(hours: 2),
        );
      }
    } catch (e) {
      debugPrint('Error loading featured products: $e');
    }
  }

  // تحميل المنتجات المقترحة
  Future<void> _loadRecommendedProducts() async {
    try {
      final response = await ApiService.instance.get('/products/recommended');
      
      if (response.success && response.data != null) {
        final productsData = response.data as List;
        _recommendedProducts = productsData.map((json) => ProductModel.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Error loading recommended products: $e');
    }
  }

  // تحميل الإحصائيات
  Future<void> _loadStats() async {
    try {
      final response = await ApiService.instance.get('/stats/home');
      
      if (response.success && response.data != null) {
        _stats = response.data as Map<String, dynamic>;
      }
    } catch (e) {
      debugPrint('Error loading stats: $e');
    }
  }

  // إضافة مصطلح بحث جديد
  Future<void> addSearchTerm(String term) async {
    if (term.trim().isEmpty) return;
    
    await StorageService.addSearchTerm(term.trim());
    _recentSearches = await StorageService.getSearchHistory();
    notifyListeners();
  }

  // مسح تاريخ البحث
  Future<void> clearSearchHistory() async {
    await StorageService.clearSearchHistory();
    _recentSearches.clear();
    notifyListeners();
  }

  // إزالة مصطلح بحث محدد
  Future<void> removeSearchTerm(String term) async {
    _recentSearches.remove(term);
    await StorageService.saveSearchHistory(_recentSearches);
    notifyListeners();
  }

  // تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // تحديث حالة الخطأ
  void _setError(bool hasError, String message) {
    _hasError = hasError;
    _errorMessage = message;
    notifyListeners();
  }

  // إعادة تحميل البيانات
  Future<void> refresh() async {
    await loadHomeData();
  }

  // البحث في المنتجات المميزة
  List<ProductModel> searchFeaturedProducts(String query) {
    if (query.trim().isEmpty) return _featuredProducts;
    
    final lowerQuery = query.toLowerCase();
    return _featuredProducts.where((product) {
      return product.name.toLowerCase().contains(lowerQuery) ||
             product.description.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  // الحصول على فئة بالمعرف
  CategoryModel? getCategoryById(int id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  // الحصول على منتج بالمعرف
  ProductModel? getProductById(int id) {
    try {
      return _featuredProducts.firstWhere((product) => product.id == id);
    } catch (e) {
      // البحث في المنتجات المقترحة أيضاً
      try {
        return _recommendedProducts.firstWhere((product) => product.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  // الحصول على إحصائية محددة
  T? getStat<T>(String key) {
    return _stats[key] as T?;
  }

  // التحقق من وجود بيانات
  bool get hasData {
    return _banners.isNotEmpty || 
           _categories.isNotEmpty || 
           _featuredProducts.isNotEmpty;
  }
}
