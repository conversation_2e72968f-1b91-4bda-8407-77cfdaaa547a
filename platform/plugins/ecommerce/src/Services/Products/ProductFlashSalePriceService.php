<?php

namespace Botble\Ecommerce\Services\Products;

use Botble\Ecommerce\Facades\FlashSale;
use Botble\Ecommerce\Models\Product;
use Closure;

class ProductFlashSalePriceService extends ProductPriceHandlerService
{
    public function handle(Product $product, Closure $next)
    {
        if (! FlashSale::isEnabled()) {
            return $next($product);
        }

        $finalPrice = (float) ($product->getFinalPrice() ?: $product->price);

        // الحصول على سعر العرض الفعلي
        $flashSale = FlashSale::getFacadeRoot()->flashSaleForProduct($product);
        $flashSalePrice = null;

        if ($flashSale && $flashSale->pivot->quantity > $flashSale->pivot->sold) {
            $flashSalePrice = (float) $flashSale->pivot->price;
        }

        // إذا لم يكن هناك عرض فلاش سيل، لا نغير شيئاً
        if (!$flashSalePrice) {
            $product->setOriginalPrice($finalPrice);
            return $next($product);
        }

        // للعملاء الجملة: مقارنة سعر العرض مع سعر الجملة واختيار الأفضل
        if (auth('customer')->check() && auth('customer')->user()->is_wholesale == 1 && $product->wholesale_price > 0) {
            $wholesalePrice = (float) $product->wholesale_price;

            // اختيار أفضل سعر بين سعر الجملة وسعر العرض
            if ($flashSalePrice < $wholesalePrice) {
                // سعر العرض أفضل من سعر الجملة
                $product->setOriginalPrice($flashSalePrice);
                $product->setFinalPrice($flashSalePrice);
            } else {
                // سعر الجملة أفضل - لا نغير شيئاً
                $product->setOriginalPrice($finalPrice);
            }
        } else {
            // للعملاء العاديين: المنطق الأصلي
            if ($flashSalePrice < $finalPrice) {
                $product->setOriginalPrice($flashSalePrice);
                $product->setFinalPrice($flashSalePrice);
            } else {
                $product->setOriginalPrice($finalPrice);
            }
        }

        return $next($product);
    }
}
