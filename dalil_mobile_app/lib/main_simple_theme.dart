import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() {
  runApp(const DalilApp());
}

class DalilApp extends StatelessWidget {
  const DalilApp({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _loadTheme(),
      builder: (context, snapshot) {
        final theme = snapshot.data ?? _getDefaultTheme();
        
        return MaterialApp(
          title: theme['branding']?['site_title'] ?? 'دليل قطع الغيار',
          debugShowCheckedModeBanner: false,
          locale: const Locale('ar', 'IQ'),
          supportedLocales: const [
            Locale('ar', 'IQ'),
            Locale('en', 'US'),
          ],
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          theme: _buildTheme(theme),
          home: HomeScreen(theme: theme),
        );
      },
    );
  }

  Future<Map<String, dynamic>> _loadTheme() async {
    try {
      final response = await http.get(
        Uri.parse('http://localhost:8080/theme-api.php'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return data['data'];
        }
      }
    } catch (e) {
      print('خطأ في جلب الثيم: $e');
    }
    return _getDefaultTheme();
  }

  Map<String, dynamic> _getDefaultTheme() {
    return {
      'colors': {
        'primary': '#0C55AA',
        'secondary': '#6c7a91',
        'text': '#182433',
      },
      'fonts': {
        'primary': 'Roboto',
        'arabic': 'Cairo',
      },
      'branding': {
        'site_name': 'دليل قطع الغيار',
        'site_title': 'دليلك لقطع غيار السيارات',
      },
    };
  }

  ThemeData _buildTheme(Map<String, dynamic> theme) {
    final primaryColorHex = theme['colors']?['primary'] ?? '#0C55AA';
    final primaryColor = Color(int.parse(primaryColorHex.replaceAll('#', 'FF'), radix: 16));

    return ThemeData(
      primarySwatch: Colors.blue,
      primaryColor: primaryColor,
      fontFamily: theme['fonts']?['arabic'] ?? 'Cairo',
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }
}

class HomeScreen extends StatelessWidget {
  final Map<String, dynamic> theme;

  const HomeScreen({super.key, required this.theme});

  @override
  Widget build(BuildContext context) {
    final primaryColorHex = theme['colors']?['primary'] ?? '#0C55AA';
    final primaryColor = Color(int.parse(primaryColorHex.replaceAll('#', 'FF'), radix: 16));
    
    final gradientColors = theme['mobile_specific']?['gradient_colors'] as List? ?? 
        [primaryColorHex, primaryColorHex];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          theme['branding']?['site_name'] ?? 'دليل قطع الغيار',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة باحث قطع الغيار
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(int.parse(gradientColors[0].replaceAll('#', 'FF'), radix: 16)),
                    Color(int.parse(gradientColors[1].replaceAll('#', 'FF'), radix: 16)),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withOpacity(0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: const Icon(
                      Icons.car_repair,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 20),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'باحث قطع الغيار',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 6),
                        Text(
                          'ابحث عن قطع الغيار حسب نوع السيارة',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // معلومات الثيم
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات الثيم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildInfoRow('اسم الموقع', theme['branding']?['site_name'] ?? 'غير محدد'),
                    _buildInfoRow('عنوان الموقع', theme['branding']?['site_title'] ?? 'غير محدد'),
                    _buildInfoRow('اللون الأساسي', theme['colors']?['primary'] ?? 'غير محدد'),
                    _buildInfoRow('اللون الثانوي', theme['colors']?['secondary'] ?? 'غير محدد'),
                    _buildInfoRow('الخط الأساسي', theme['fonts']?['primary'] ?? 'غير محدد'),
                    _buildInfoRow('الخط العربي', theme['fonts']?['arabic'] ?? 'غير محدد'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 32),

            // ألوان الثيم
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ألوان الثيم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 12,
                      runSpacing: 12,
                      children: [
                        _buildColorChip('أساسي', theme['colors']?['primary']),
                        _buildColorChip('ثانوي', theme['colors']?['secondary']),
                        _buildColorChip('نص', theme['colors']?['text']),
                        _buildColorChip('رابط', theme['colors']?['link']),
                        _buildColorChip('نجاح', theme['colors']?['success']),
                        _buildColorChip('تحذير', theme['colors']?['warning']),
                        _buildColorChip('خطر', theme['colors']?['danger']),
                        _buildColorChip('معلومات', theme['colors']?['info']),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorChip(String label, String? colorHex) {
    if (colorHex == null) return const SizedBox.shrink();
    
    final color = Color(int.parse(colorHex.replaceAll('#', 'FF'), radix: 16));
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        label,
        style: TextStyle(
          color: _getContrastColor(color),
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  Color _getContrastColor(Color color) {
    // حساب التباين لاختيار لون النص المناسب
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}
