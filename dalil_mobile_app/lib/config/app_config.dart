class AppConfig {
  // إعدادات الخادم - استخدام APIs حقيقية فقط
  static const String baseUrl = 'http://localhost:8000';
  static const String apiBaseUrl = '$baseUrl/api/v1';

  // لا توجد بيانات افتراضية - استخدام APIs حقيقية فقط
  static const bool useLocalData = false;
  static const String storageUrl = '$baseUrl/storage';
  static const String mediaUrl = '$baseUrl/storage/app/public';
  
  // إعدادات التطبيق
  static const String appName = 'دليل';
  static const String appVersion = '1.0.0';
  static const String defaultLanguage = 'ar';
  
  // إعدادات API
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  };
  
  // === مسارات APIs التجارة الإلكترونية الحقيقية ===
  static const String categoriesEndpoint = '/ecommerce/product-categories';
  static const String productsEndpoint = '/ecommerce/products';
  static const String brandsEndpoint = '/ecommerce/brands';
  static const String cartEndpoint = '/ecommerce/cart';
  static const String ordersEndpoint = '/ecommerce/orders';
  static const String reviewsEndpoint = '/ecommerce/reviews';
  static const String wishlistEndpoint = '/ecommerce/wishlist';
  static const String compareEndpoint = '/ecommerce/compare';
  static const String couponsEndpoint = '/ecommerce/coupon';
  static const String addressesEndpoint = '/ecommerce/addresses';
  static const String currenciesEndpoint = '/ecommerce/currencies';
  static const String countriesEndpoint = '/ecommerce/countries';
  static const String flashSalesEndpoint = '/ecommerce/flash-sales';
  static const String filtersEndpoint = '/ecommerce/filters';
  static const String checkoutEndpoint = '/ecommerce/checkout';
  static const String downloadsEndpoint = '/ecommerce/downloads';
  static const String orderReturnsEndpoint = '/ecommerce/order-returns';
  static const String orderTrackingEndpoint = '/ecommerce/orders/tracking';

  // === نظام التسعير للجملة ===
  static const String wholesaleEndpoint = '/ecommerce/wholesale';
  static const String wholesalePricesEndpoint = '/ecommerce/wholesale/prices';
  static const String wholesaleApplicationEndpoint = '/ecommerce/wholesale/apply';
  static const String wholesaleStatsEndpoint = '/ecommerce/wholesale/stats';

  // === Vehicle Parts Finder ===
  static const String vehiclePartsEndpoint = '/vehicle-parts';
  static const String vehicleCategoriesEndpoint = '/vehicle-parts/root-categories';
  static const String vehicleMakesEndpoint = '/vehicle-parts/makes';
  static const String vehicleModelsEndpoint = '/vehicle-parts/models';
  static const String vehicleSearchEndpoint = '/vehicle-parts/search';
  static const String vehiclePartsEndpoint2 = '/vehicle-parts/vehicle';
  static const String myVehiclesEndpoint = '/vehicle-parts/my-vehicles';
  static const String popularPartsEndpoint = '/vehicle-parts/categories';
  static const String quickSearchEndpoint = '/vehicle-parts/quick-search';

  // === المصادقة والمستخدمين ===
  static const String authEndpoint = '/auth';
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String logoutEndpoint = '/auth/logout';
  static const String profileEndpoint = '/auth/profile';
  static const String otpEndpoint = '/otp';
  static const String otpSendEndpoint = '/otp/send';
  static const String otpVerifyEndpoint = '/otp/verify';

  // === المحتوى والوسائط ===
  static const String blogEndpoint = '/posts';
  static const String slidersEndpoint = '/simple-sliders';
  static const String adsEndpoint = '/ads';
  static const String galleriesEndpoint = '/galleries';
  static const String testimonialsEndpoint = '/testimonials';

  // === الدعم والتواصل ===
  static const String contactEndpoint = '/contact';
  static const String faqEndpoint = '/faq';
  static const String newsletterEndpoint = '/newsletter';

  // === الموقع والتحليلات ===
  static const String locationEndpoint = '/location';
  static const String analyticsEndpoint = '/analytics';
  static const String marketplaceEndpoint = '/marketplace';
  static const String announcementsEndpoint = '/announcements';
  
  // إعدادات التخزين المؤقت
  static const Duration cacheTimeout = Duration(minutes: 30);
  static const int maxCacheSize = 100; // عدد العناصر
  
  // إعدادات الصور
  static const String defaultProductImage = 'assets/images/default_product.png';
  static const String defaultCategoryImage = 'assets/images/default_category.png';
  static const String logoImage = 'assets/images/logo-4.png';
  
  // إعدادات التطبيق
  static const int productsPerPage = 20;
  static const int categoriesLimit = 10;
  static const int featuredProductsLimit = 10;
  
  // إعدادات الألوان
  static const int primaryColorValue = 0xFF1976D2;
  static const int secondaryColorValue = 0xFF4CAF50;
  static const int accentColorValue = 0xFFFF9800;
  
  // إعدادات الخطوط
  static const String primaryFontFamily = 'Arial';
  static const String arabicFontFamily = 'Tajawal';
  
  // إعدادات الشبكة
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  
  // إعدادات التصحيح
  static const bool enableLogging = true;
  static const bool enableCrashReporting = false;
  static const bool enableDebugMode = true;

  // مفاتيح التخزين المحلي المفقودة
  static const String wishlistKey = 'wishlist_data';
  static const String themeKey = 'app_theme';

  // دوال مساعدة محذوفة لتجنب التكرار
  
  // إعدادات المصادقة
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String languageKey = 'selected_language';
  
  // إعدادات الإشعارات
  static const bool enablePushNotifications = true;
  static const String fcmServerKey = 'your_fcm_server_key_here';
  
  // إعدادات التحليلات
  static const bool enableAnalytics = false;
  static const String analyticsKey = 'your_analytics_key_here';
  
  // إعدادات الدفع
  static const String currency = 'IQD';
  static const String currencySymbol = 'د.ع';
  static const String currencyPosition = 'after'; // before or after
  
  // إعدادات البحث
  static const int searchMinLength = 2;
  static const Duration searchDebounce = Duration(milliseconds: 500);
  
  // إعدادات السلة
  static const int maxCartItems = 50;
  static const String cartKey = 'shopping_cart';
  
  // إعدادات المفضلة
  static const String favoritesKey = 'favorites';
  static const int maxFavorites = 100;
  
  // إعدادات التقييمات
  static const int maxRating = 5;
  static const int minRating = 1;
  
  // إعدادات المشاركة
  static const String shareText = 'تطبيق دليل لقطع غيار السيارات';
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.dalil.auto';
  static const String appStoreUrl = 'https://apps.apple.com/app/dalil-auto/id123456789';
  
  // إعدادات الاتصال
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+964 ************';
  static const String websiteUrl = 'https://dalil.com';
  
  // إعدادات وسائل التواصل الاجتماعي
  static const String facebookUrl = 'https://facebook.com/dalil';
  static const String instagramUrl = 'https://instagram.com/dalil';
  static const String twitterUrl = 'https://twitter.com/dalil';
  static const String youtubeUrl = 'https://youtube.com/dalil';

  // إعدادات الصور الافتراضية
  static const String defaultImageUrl = '/assets/images/placeholder.png';
  
  // دوال مساعدة
  static String getImageUrl(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return defaultProductImage;
    }

    if (imagePath.startsWith('http')) {
      return imagePath;
    }

    if (imagePath.startsWith('assets/')) {
      return imagePath;
    }

    // إذا كان المسار يبدأ بـ / فهو مسار مطلق
    if (imagePath.startsWith('/')) {
      return '$baseUrl$imagePath';
    }

    // إذا كان مسار نسبي، أضف storage URL
    return '$storageUrl/$imagePath';
  }
  
  static String getCategoryImageUrl(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return defaultCategoryImage;
    }
    
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    
    if (imagePath.startsWith('assets/')) {
      return imagePath;
    }
    
    return '$storageUrl/$imagePath';
  }
  
  static String formatPrice(dynamic price) {
    if (price == null) return '0';
    
    final numPrice = price is String ? double.tryParse(price) ?? 0 : price.toDouble();
    final formattedPrice = numPrice.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
    
    return currencyPosition == 'before' 
        ? '$currencySymbol $formattedPrice'
        : '$formattedPrice $currencySymbol';
  }
  
  static bool isProduction() {
    return !baseUrl.contains('localhost') && !baseUrl.contains('127.0.0.1');
  }
  
  static bool isDevelopment() {
    return !isProduction();
  }
}

// إعدادات البيئة
class EnvironmentConfig {
  static const String development = 'development';
  static const String staging = 'staging';
  static const String production = 'production';
  
  static String get currentEnvironment {
    if (AppConfig.isDevelopment()) {
      return development;
    }
    return production;
  }
  
  static bool get isDebugMode {
    return currentEnvironment == development;
  }
}

// إعدادات الأخطاء
class ErrorConfig {
  static const String networkError = 'خطأ في الاتصال بالشبكة';
  static const String serverError = 'خطأ في الخادم';
  static const String unknownError = 'حدث خطأ غير متوقع';
  static const String timeoutError = 'انتهت مهلة الاتصال';
  static const String noDataError = 'لا توجد بيانات متاحة';
  static const String invalidDataError = 'البيانات غير صحيحة';
  
  static String getErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'network_error':
        return networkError;
      case 'server_error':
        return serverError;
      case 'timeout_error':
        return timeoutError;
      case 'no_data':
        return noDataError;
      case 'invalid_data':
        return invalidDataError;
      default:
        return unknownError;
    }
  }
}
