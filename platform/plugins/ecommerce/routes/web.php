// Car Makes
Route::group(['prefix' => 'car-makes', 'as' => 'car-makes.'], function () {
    Route::resource('', 'CarMakeController')->parameters(['' => 'car-make']);
    Route::delete('items/destroy', [
        'as' => 'deletes',
        'uses' => 'CarMakeController@deletes',
        'permission' => 'car-makes.destroy',
    ]);
});

// Include other routes files here
require __DIR__ . '/cart.php';
require __DIR__ . '/customer.php';
require __DIR__ . '/order-returns.php';
require __DIR__ . '/shipments.php';
require __DIR__ . '/review.php';
require __DIR__ . '/invoice.php';
require __DIR__ . '/tax.php';
require __DIR__ . '/bulk-import.php';
require __DIR__ . '/car-makes.php';
require __DIR__ . '/car-models.php';
