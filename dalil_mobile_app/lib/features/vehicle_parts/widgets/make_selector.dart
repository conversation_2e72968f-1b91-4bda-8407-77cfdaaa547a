import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../providers/vehicle_parts_provider.dart';
import '../models/vehicle_category_model.dart';

class MakeSelector extends StatelessWidget {
  const MakeSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<VehiclePartsProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'vehicle_parts_finder.make'.tr(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(
                  color: provider.selectedVehicleType == null 
                      ? Colors.grey[300]! 
                      : Theme.of(context).primaryColor.withOpacity(0.3),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: _buildContent(context, provider),
            ),
          ],
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, VehiclePartsProvider provider) {
    if (provider.selectedVehicleType == null) {
      return _buildDisabledState(context);
    }

    if (provider.isLoading && provider.makes.isEmpty) {
      return _buildLoadingState(context);
    }

    if (provider.makes.isEmpty) {
      return _buildEmptyState(context);
    }

    return _buildDropdown(context, provider);
  }

  Widget _buildDisabledState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          Icon(
            Icons.block,
            color: Colors.grey[400],
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            'اختر نوع المركبة أولاً',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 12),
          Text(
            'تحميل الماركات...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.grey[600],
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            'لا توجد ماركات متاحة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdown(BuildContext context, VehiclePartsProvider provider) {
    return DropdownButtonHideUnderline(
      child: DropdownButton<VehicleCategoryModel>(
        value: provider.selectedMake,
        hint: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Icon(
                Icons.business,
                color: Colors.grey[600],
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'vehicle_parts_finder.select_make'.tr(),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        isExpanded: true,
        icon: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: Icon(
            Icons.keyboard_arrow_down,
            color: Colors.grey[600],
          ),
        ),
        items: provider.makes.map((make) {
          return DropdownMenuItem<VehicleCategoryModel>(
            value: make,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  // صورة الماركة
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: make.imageUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(6),
                            child: CachedNetworkImage(
                              imageUrl: make.imageUrl!,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: Colors.grey[100],
                                child: Icon(
                                  Icons.business,
                                  color: Colors.grey[400],
                                  size: 16,
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: Colors.grey[100],
                                child: Icon(
                                  Icons.business,
                                  color: Colors.grey[400],
                                  size: 16,
                                ),
                              ),
                            ),
                          )
                        : Container(
                            color: Colors.grey[100],
                            child: Icon(
                              Icons.business,
                              color: Colors.grey[400],
                              size: 16,
                            ),
                          ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      make.name,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  if (make.childrenCount > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${make.childrenCount}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        }).toList(),
        onChanged: (make) {
          if (make != null) {
            provider.selectMake(make);
          }
        },
        dropdownColor: Colors.white,
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        menuMaxHeight: 300,
      ),
    );
  }
}
