<?php

namespace Botble\RevoWoocommerce\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;
use Illuminate\Contracts\View\View;

class CategoriesController extends BaseController
{
    public function index(): View
    {
        $this->pageTitle('إدارة الفئات');

        // Get categories from ecommerce plugin
        $categories = $this->getCategories();

        return view('plugins/revo-woocommerce-plugin-7.0.3::categories.index', compact('categories'));
    }

    public function updateOrder(Request $request, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|integer',
            'categories.*.order' => 'required|integer|min:0',
        ], [
            'categories.required' => 'بيانات الفئات مطلوبة',
            'categories.*.id.required' => 'معرف الفئة مطلوب',
            'categories.*.order.required' => 'ترتيب الفئة مطلوب',
            'categories.*.order.integer' => 'الترتيب يجب أن يكون رقم صحيح',
        ]);

        // Update categories order (placeholder implementation)
        $this->updateCategoriesOrder($request->input('categories'));

        return $response
            ->setMessage('تم تحديث ترتيب الفئات بنجاح!');
    }

    public function toggleVisibility($id, BaseHttpResponse $response): BaseHttpResponse
    {
        // Toggle category visibility in app (placeholder implementation)
        $this->toggleCategoryVisibility($id);

        return $response
            ->setMessage('تم تحديث حالة الفئة بنجاح!');
    }

    protected function getCategories(): array
    {
        // Placeholder data - in real implementation, this would fetch from ec_product_categories
        return [
            [
                'id' => 1,
                'name' => 'الإلكترونيات',
                'image' => 'https://via.placeholder.com/150x150/007bff/ffffff?text=Electronics',
                'products_count' => 25,
                'order' => 1,
                'visible_in_app' => true,
                'status' => 'published',
            ],
            [
                'id' => 2,
                'name' => 'الملابس',
                'image' => 'https://via.placeholder.com/150x150/28a745/ffffff?text=Clothes',
                'products_count' => 45,
                'order' => 2,
                'visible_in_app' => true,
                'status' => 'published',
            ],
            [
                'id' => 3,
                'name' => 'المنزل والحديقة',
                'image' => 'https://via.placeholder.com/150x150/ffc107/ffffff?text=Home',
                'products_count' => 18,
                'order' => 3,
                'visible_in_app' => false,
                'status' => 'published',
            ],
            [
                'id' => 4,
                'name' => 'الرياضة',
                'image' => 'https://via.placeholder.com/150x150/dc3545/ffffff?text=Sports',
                'products_count' => 32,
                'order' => 4,
                'visible_in_app' => true,
                'status' => 'published',
            ],
        ];
    }

    protected function updateCategoriesOrder(array $categories): void
    {
        // Placeholder implementation - would update in database
        // In real implementation: update revo_category_order in settings or separate table
        foreach ($categories as $category) {
            setting()->set('revo_category_order_' . $category['id'], $category['order']);
        }
        setting()->save();
    }

    protected function toggleCategoryVisibility($id): void
    {
        // Placeholder implementation - would toggle in database
        // In real implementation: update revo_category_visible_in_app setting
        $currentVisibility = setting('revo_category_visible_' . $id, true);
        setting()->set('revo_category_visible_' . $id, !$currentVisibility);
        setting()->save();
    }
}
