import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:easy_localization/easy_localization.dart';
import '../providers/vehicle_parts_provider.dart';
import '../models/vehicle_category_model.dart';

class QuickSearchBar extends StatefulWidget {
  const QuickSearchBar({super.key});

  @override
  State<QuickSearchBar> createState() => _QuickSearchBarState();
}

class _QuickSearchBarState extends State<QuickSearchBar> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _showResults = false;

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<VehiclePartsProvider>(
      builder: (context, provider, child) {
        return Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                decoration: InputDecoration(
                  hintText: 'vehicle_parts_finder.quick_search'.tr(),
                  prefixIcon: Icon(
                    Icons.search,
                    color: Theme.of(context).primaryColor,
                  ),
                  suffixIcon: _controller.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _controller.clear();
                            provider.quickSearch('');
                            setState(() {
                              _showResults = false;
                            });
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey[50],
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                onChanged: (value) {
                  provider.quickSearch(value);
                  setState(() {
                    _showResults = value.isNotEmpty;
                  });
                },
                onTap: () {
                  setState(() {
                    _showResults = _controller.text.isNotEmpty;
                  });
                },
              ),
            ),
            
            // نتائج البحث السريع
            if (_showResults && provider.quickSearchResults.isNotEmpty)
              Container(
                margin: const EdgeInsets.only(top: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: provider.quickSearchResults.length,
                  separatorBuilder: (context, index) => Divider(
                    height: 1,
                    color: Colors.grey[200],
                  ),
                  itemBuilder: (context, index) {
                    final result = provider.quickSearchResults[index];
                    return _buildSearchResultItem(context, result, provider);
                  },
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildSearchResultItem(
    BuildContext context,
    VehicleCategoryModel result,
    VehiclePartsProvider provider,
  ) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          _getIconForType(result),
          color: Theme.of(context).primaryColor,
          size: 20,
        ),
      ),
      title: Text(
        result.name,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        _getSubtitleForResult(result),
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Colors.grey[600],
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey[400],
      ),
      onTap: () {
        _selectSearchResult(result, provider);
      },
    );
  }

  IconData _getIconForType(VehicleCategoryModel result) {
    // تحديد الأيقونة بناءً على نوع النتيجة
    if (result.parentId == null) {
      // نوع المركبة
      return Icons.directions_car;
    } else {
      // ماركة أو موديل
      return Icons.business;
    }
  }

  String _getSubtitleForResult(VehicleCategoryModel result) {
    if (result.parentId == null) {
      return 'نوع مركبة';
    } else {
      return 'ماركة سيارة';
    }
  }

  void _selectSearchResult(
    VehicleCategoryModel result,
    VehiclePartsProvider provider,
  ) {
    // إخفاء النتائج
    setState(() {
      _showResults = false;
    });
    
    // إزالة التركيز
    _focusNode.unfocus();
    
    // مسح النص
    _controller.clear();
    
    // تحديد النتيجة بناءً على نوعها
    if (result.parentId == null) {
      // نوع مركبة
      provider.selectVehicleType(result);
    } else {
      // ماركة - نحتاج لتحديد نوع المركبة أولاً
      // يمكن تحسين هذا بإضافة معلومات أكثر في API
      _showSelectionDialog(context, result, provider);
    }
  }

  void _showSelectionDialog(
    BuildContext context,
    VehicleCategoryModel result,
    VehiclePartsProvider provider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تحديد ${result.name}'),
        content: Text('هل تريد تحديد ${result.name} كماركة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('common.cancel'.tr()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // يمكن إضافة منطق أكثر تعقيداً هنا
              // لتحديد الماركة مباشرة إذا كانت البيانات متوفرة
            },
            child: Text('common.select'.tr()),
          ),
        ],
      ),
    );
  }
}
