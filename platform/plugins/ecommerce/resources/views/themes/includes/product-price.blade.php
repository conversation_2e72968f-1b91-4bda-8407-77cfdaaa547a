@if (! EcommerceHelper::hideProductPrice() || EcommerceHelper::isCartEnabled())
    @php
        $isDisplayPriceOriginal ??= true;
        $priceWrapperClassName ??= null;
        $priceClassName ??= null;
        $priceOriginalClassName ??= null;
        $priceOriginalWrapperClassName ??= null;
        $isAdmin = auth()->guard('web')->check() && auth()->guard('web')->user()->super_user;
        $isWholesaleCustomer = auth()->guard('customer')->check() && auth()->guard('customer')->user()->is_wholesale;
    @endphp

    <div class="{{ $priceWrapperClassName === null ? 'bb-product-price mb-3' : $priceWrapperClassName }}">
        @if ($isAdmin)
            {{-- عرض كلا السعرين للمشرفين --}}
            <div class="d-flex flex-column">
                <span class="{{ $priceClassName === null ? 'bb-product-price-text fw-bold' : $priceClassName }}" data-bb-value="product-price">
                    <span class="price-label">{{ __('plugins/ecommerce::ecommerce.Price') }}:</span> {{ $priceFormatted ?? $product->price()->displayAsText() }}
                </span>
                <span class="{{ $priceClassName === null ? 'bb-product-price-text fw-bold text-success' : $priceClassName . ' text-success' }}" data-bb-value="product-wholesale-price">
                    <span class="price-label">{{ __('plugins/ecommerce::ecommerce.Wholesale') }}:</span> {{ format_price($product->wholesale_price) }}
                </span>
            </div>
        @elseif ($isWholesaleCustomer)
            {{-- عرض سعر الجملة فقط لعملاء الجملة --}}
            <span class="{{ $priceClassName === null ? 'bb-product-price-text fw-bold' : $priceClassName }}" data-bb-value="product-wholesale-price">
                {{ format_price($product->wholesale_price) }}
            </span>
        @else
            {{-- عرض السعر العادي لباقي العملاء --}}
            <span class="{{ $priceClassName === null ? 'bb-product-price-text fw-bold' : $priceClassName }}" data-bb-value="product-price">
                {{ $priceFormatted ?? $product->price()->displayAsText() }}
            </span>
        @endif

        @if (!$isWholesaleCustomer && !$isAdmin && $isDisplayPriceOriginal && $product->isOnSale())
            @include(EcommerceHelper::viewPath('includes.product-prices.original'), [
                'priceWrapperClassName' => $priceOriginalWrapperClassName,
                'priceClassName' => $priceOriginalClassName,
            ])
        @endif
    </div>
@endif
