<div class="mb-3">
    <h4>{{ trans('plugins/ecommerce::products.car_fitment') }}</h4>
    <p class="text-muted">{{ trans('plugins/ecommerce::products.car_fitment_description') }}</p>

    <div class="car-fitment-container">
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="car-fitment-table">
                <thead>
                    <tr>
                        <th>{{ trans('plugins/ecommerce::car-makes.name') }}</th>
                        <th>{{ trans('plugins/ecommerce::car-models.name') }}</th>
                        <th>{{ trans('plugins/ecommerce::car-years.name') }}</th>
                        <th style="width: 120px"></th>
                    </tr>
                </thead>
                <tbody>
                    @if ($product && $product->carFitment->count() > 0)
                        @foreach($product->carFitment as $item)
                            <tr class="car-fitment-item">
                                <td>
                                    <select name="car_fitment[{{ $loop->index }}][make_id]" class="form-select car-make-select" data-row="{{ $loop->index }}">
                                        <option value="">{{ trans('plugins/ecommerce::products.select_make') }}</option>
                                        @foreach(\Botble\Ecommerce\Models\CarMake::wherePublished()->get() as $make)
                                            <option value="{{ $make->id }}" {{ $item->pivot->make_id == $make->id ? 'selected' : '' }}>{{ $make->name }}</option>
                                        @endforeach
                                    </select>
                                </td>
                                <td>
                                    <select name="car_fitment[{{ $loop->index }}][model_id]" class="form-select car-model-select" data-row="{{ $loop->index }}">
                                        <option value="">{{ trans('plugins/ecommerce::products.select_model') }}</option>
                                        @foreach(\Botble\Ecommerce\Models\CarModel::wherePublished()->where('make_id', $item->pivot->make_id)->get() as $model)
                                            <option value="{{ $model->id }}" {{ $item->pivot->model_id == $model->id ? 'selected' : '' }}>{{ $model->name }}</option>
                                        @endforeach
                                    </select>
                                </td>
                                <td>
                                    <select name="car_fitment[{{ $loop->index }}][year_id]" class="form-select car-year-select">
                                        <option value="">{{ trans('plugins/ecommerce::products.select_year') }}</option>
                                        @foreach(\Botble\Ecommerce\Models\CarYear::where('model_id', $item->pivot->model_id)->get() as $year)
                                            <option value="{{ $year->id }}" {{ $item->pivot->year_id == $year->id ? 'selected' : '' }}>{{ $year->year }}</option>
                                        @endforeach
                                    </select>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-danger remove-car-fitment"><i class="ti ti-trash"></i></button>
                                </td>
                            </tr>
                        @endforeach
                    @endif
                    <tr class="car-fitment-item empty" @if($product && $product->carFitment->count() > 0) style="display: none" @endif>
                        <td colspan="4" class="text-center">{{ trans('plugins/ecommerce::products.no_car_fitment') }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4">
                            <button type="button" id="add-car-fitment" class="btn btn-info"><i class="ti ti-plus"></i> {{ trans('plugins/ecommerce::products.add_car_fitment') }}</button>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // نموذج الصف الجديد
        const fitmentRowTemplate = `
            <tr class="car-fitment-item">
                <td>
                    <select name="car_fitment[__INDEX__][make_id]" class="form-select car-make-select" data-row="__INDEX__">
                        <option value="">{{ trans('plugins/ecommerce::products.select_make') }}</option>
                        @foreach(\Botble\Ecommerce\Models\CarMake::wherePublished()->get() as $make)
                            <option value="{{ $make->id }}">{{ $make->name }}</option>
                        @endforeach
                    </select>
                </td>
                <td>
                    <select name="car_fitment[__INDEX__][model_id]" class="form-select car-model-select" data-row="__INDEX__" disabled>
                        <option value="">{{ trans('plugins/ecommerce::products.select_model') }}</option>
                    </select>
                </td>
                <td>
                    <select name="car_fitment[__INDEX__][year_id]" class="form-select car-year-select" disabled>
                        <option value="">{{ trans('plugins/ecommerce::products.select_year') }}</option>
                    </select>
                </td>
                <td>
                    <button type="button" class="btn btn-danger remove-car-fitment"><i class="ti ti-trash"></i></button>
                </td>
            </tr>
        `;
        
        // زر إضافة توافق جديد
        document.getElementById('add-car-fitment').addEventListener('click', function() {
            const tbody = document.querySelector('#car-fitment-table tbody');
            const emptyRow = tbody.querySelector('.empty');
            if (emptyRow) {
                emptyRow.style.display = 'none';
            }
            
            const rowCount = tbody.querySelectorAll('tr:not(.empty)').length;
            const newRow = fitmentRowTemplate.replace(/__INDEX__/g, rowCount);
            tbody.insertAdjacentHTML('beforeend', newRow);
            
            // تفعيل الأحداث للصف الجديد
            attachEventListeners();
        });
        
        // تفعيل أحداث التغيير والحذف
        function attachEventListeners() {
            // أحداث تغيير الماركة
            document.querySelectorAll('.car-make-select').forEach(select => {
                if (!select.hasAttribute('data-initialized')) {
                    select.addEventListener('change', function() {
                        const row = this.getAttribute('data-row');
                        const modelSelect = document.querySelector(`.car-model-select[data-row="${row}"]`);
                        const yearSelect = modelSelect.closest('tr').querySelector('.car-year-select');
                        
                        modelSelect.innerHTML = '<option value="">{{ trans('plugins/ecommerce::products.select_model') }}</option>';
                        yearSelect.innerHTML = '<option value="">{{ trans('plugins/ecommerce::products.select_year') }}</option>';
                        
                        modelSelect.disabled = !this.value;
                        yearSelect.disabled = true;
                        
                        if (this.value) {
                            // استدعاء API للحصول على الموديلات
                            fetch(`/api/car-models?make_id=${this.value}`)
                                .then(response => response.json())
                                .then(data => {
                                    data.forEach(model => {
                                        const option = document.createElement('option');
                                        option.value = model.id;
                                        option.textContent = model.name;
                                        modelSelect.appendChild(option);
                                    });
                                });
                        }
                    });
                    select.setAttribute('data-initialized', 'true');
                }
            });
            
            // أحداث تغيير الموديل
            document.querySelectorAll('.car-model-select').forEach(select => {
                if (!select.hasAttribute('data-initialized')) {
                    select.addEventListener('change', function() {
                        const yearSelect = this.closest('tr').querySelector('.car-year-select');
                        
                        yearSelect.innerHTML = '<option value="">{{ trans('plugins/ecommerce::products.select_year') }}</option>';
                        yearSelect.disabled = !this.value;
                        
                        if (this.value) {
                            // استدعاء API للحصول على السنوات
                            fetch(`/api/car-years?model_id=${this.value}`)
                                .then(response => response.json())
                                .then(data => {
                                    data.forEach(year => {
                                        const option = document.createElement('option');
                                        option.value = year.id;
                                        option.textContent = year.year;
                                        yearSelect.appendChild(option);
                                    });
                                });
                        }
                    });
                    select.setAttribute('data-initialized', 'true');
                }
            });
            
            // أحداث أزرار الحذف
            document.querySelectorAll('.remove-car-fitment').forEach(button => {
                if (!button.hasAttribute('data-initialized')) {
                    button.addEventListener('click', function() {
                        const row = this.closest('tr');
                        row.remove();
                        
                        const tbody = document.querySelector('#car-fitment-table tbody');
                        const items = tbody.querySelectorAll('tr:not(.empty)');
                        if (items.length === 0) {
                            const emptyRow = tbody.querySelector('.empty');
                            if (emptyRow) {
                                emptyRow.style.display = '';
                            }
                        }
                    });
                    button.setAttribute('data-initialized', 'true');
                }
            });
        }
        
        // تفعيل الأحداث عند تحميل الصفحة
        attachEventListeners();
    });
</script> 