<div class="car-search-form mb-4">
    <h4 class="mb-3">{{ trans('plugins/ecommerce::products.car_search') }}</h4>
    <form action="{{ route('public.products') }}" method="GET" id="carSearchForm">
        <div class="row">
            <div class="col-md-4 mb-3">
                <select name="car_make" id="carMakeSelect" class="form-select">
                    <option value="">{{ trans('plugins/ecommerce::products.select_make') }}</option>
                    @foreach(\Botble\Ecommerce\Models\CarMake::wherePublished()->orderBy('name')->get() as $make)
                        <option value="{{ $make->id }}" {{ request()->input('car_make') == $make->id ? 'selected' : '' }}>{{ $make->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-4 mb-3">
                <select name="car_model" id="carModelSelect" class="form-select" disabled>
                    <option value="">{{ trans('plugins/ecommerce::products.select_model') }}</option>
                </select>
            </div>
            <div class="col-md-4 mb-3">
                <select name="car_year" id="carYearSelect" class="form-select" disabled>
                    <option value="">{{ trans('plugins/ecommerce::products.select_year') }}</option>
                </select>
            </div>
        </div>
        <div class="mb-4 text-end">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> {{ __('Search') }}
            </button>
            @if(auth('customer')->check())
                <button type="button" id="saveCarButton" class="btn btn-outline-primary ms-2" data-bs-toggle="tooltip" title="{{ __('Save this car to your garage') }}">
                    <i class="fas fa-save"></i> {{ __('Save Car') }}
                </button>
            @endif
        </div>
        <div class="mb-3">
            <div class="input-group">
                <input type="text" name="vin" id="vinInput" class="form-control" placeholder="{{ __('Enter VIN/Chassis Number') }}" value="{{ request()->input('vin') }}">
                <button type="button" id="vinSearchButton" class="btn btn-outline-secondary">{{ __('Lookup') }}</button>
            </div>
            <small class="text-muted">{{ __('Enter your vehicle identification number (VIN) to automatically find compatible parts') }}</small>
        </div>
    </form>
</div>

@if(auth('customer')->check())
<div class="my-cars-section mb-4" style="display: none;">
    <h5 class="mb-3">{{ __('My Saved Vehicles') }}</h5>
    <div id="savedCarsContainer" class="row g-3">
        <!-- Saved cars will be displayed here dynamically -->
    </div>
</div>
@endif

<script>
document.addEventListener('DOMContentLoaded', function() {
    const carMakeSelect = document.getElementById('carMakeSelect');
    const carModelSelect = document.getElementById('carModelSelect');
    const carYearSelect = document.getElementById('carYearSelect');
    const vinInput = document.getElementById('vinInput');
    const vinSearchButton = document.getElementById('vinSearchButton');
    const saveCarButton = document.getElementById('saveCarButton');
    
    // Function to load models based on selected make
    function loadCarModels(makeId, selectedModelId = null) {
        if (!makeId) {
            carModelSelect.innerHTML = '<option value="">{{ trans("plugins/ecommerce::products.select_model") }}</option>';
            carModelSelect.disabled = true;
            carYearSelect.innerHTML = '<option value="">{{ trans("plugins/ecommerce::products.select_year") }}</option>';
            carYearSelect.disabled = true;
            return;
        }
        
        carModelSelect.disabled = true;
        carModelSelect.innerHTML = '<option value="">{{ __("Loading...") }}</option>';
        
        fetch(`/api/car-models?make_id=${makeId}`)
            .then(response => response.json())
            .then(data => {
                carModelSelect.innerHTML = '<option value="">{{ trans("plugins/ecommerce::products.select_model") }}</option>';
                data.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name;
                    if (selectedModelId && model.id == selectedModelId) {
                        option.selected = true;
                    }
                    carModelSelect.appendChild(option);
                });
                carModelSelect.disabled = false;
                
                // If a model is selected (either from URL params or from VIN lookup), load years
                if (carModelSelect.value) {
                    loadCarYears(carModelSelect.value);
                }
            })
            .catch(error => {
                console.error('Error loading car models:', error);
                carModelSelect.innerHTML = '<option value="">{{ __("Error loading models") }}</option>';
                carModelSelect.disabled = false;
            });
    }
    
    // Function to load years based on selected model
    function loadCarYears(modelId, selectedYearId = null) {
        if (!modelId) {
            carYearSelect.innerHTML = '<option value="">{{ trans("plugins/ecommerce::products.select_year") }}</option>';
            carYearSelect.disabled = true;
            return;
        }
        
        carYearSelect.disabled = true;
        carYearSelect.innerHTML = '<option value="">{{ __("Loading...") }}</option>';
        
        fetch(`/api/car-years?model_id=${modelId}`)
            .then(response => response.json())
            .then(data => {
                carYearSelect.innerHTML = '<option value="">{{ trans("plugins/ecommerce::products.select_year") }}</option>';
                data.forEach(year => {
                    const option = document.createElement('option');
                    option.value = year.id;
                    option.textContent = year.year;
                    if (selectedYearId && year.id == selectedYearId) {
                        option.selected = true;
                    }
                    carYearSelect.appendChild(option);
                });
                carYearSelect.disabled = false;
            })
            .catch(error => {
                console.error('Error loading car years:', error);
                carYearSelect.innerHTML = '<option value="">{{ __("Error loading years") }}</option>';
                carYearSelect.disabled = false;
            });
    }
    
    // Load models when a make is selected
    carMakeSelect.addEventListener('change', function() {
        loadCarModels(this.value);
    });
    
    // Load years when a model is selected
    carModelSelect.addEventListener('change', function() {
        loadCarYears(this.value);
    });
    
    // VIN Lookup functionality
    vinSearchButton.addEventListener('click', function() {
        const vin = vinInput.value.trim();
        if (!vin) {
            alert('{{ __("Please enter a VIN/Chassis number") }}');
            return;
        }
        
        vinSearchButton.disabled = true;
        vinSearchButton.textContent = '{{ __("Looking up...") }}';
        
        fetch(`/api/vin-lookup?vin=${vin}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Auto-select make, model, year from VIN data
                    if (data.car_make_id) {
                        carMakeSelect.value = data.car_make_id;
                        loadCarModels(data.car_make_id, data.car_model_id);
                        
                        // The year will be loaded by the model change event handler
                        setTimeout(() => {
                            if (data.car_year_id) {
                                carYearSelect.value = data.car_year_id;
                            }
                        }, 1000); // Give time for models to load
                    }
                } else {
                    alert(data.message || '{{ __("Could not find vehicle information for this VIN") }}');
                }
            })
            .catch(error => {
                console.error('Error during VIN lookup:', error);
                alert('{{ __("Error during VIN lookup. Please try again.") }}');
            })
            .finally(() => {
                vinSearchButton.disabled = false;
                vinSearchButton.textContent = '{{ __("Lookup") }}';
            });
    });
    
    // Save Car functionality for logged-in users
    if (saveCarButton) {
        saveCarButton.addEventListener('click', function() {
            const makeId = carMakeSelect.value;
            const modelId = carModelSelect.value;
            const yearId = carYearSelect.value;
            
            if (!makeId || !modelId || !yearId) {
                alert('{{ __("Please select make, model and year to save this car") }}');
                return;
            }
            
            saveCarButton.disabled = true;
            
            fetch('/customer/save-car', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    make_id: makeId,
                    model_id: modelId,
                    year_id: yearId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('{{ __("Vehicle saved successfully!") }}');
                    loadSavedCars(); // Refresh the saved cars list
                } else {
                    alert(data.message || '{{ __("Failed to save vehicle") }}');
                }
            })
            .catch(error => {
                console.error('Error saving car:', error);
                alert('{{ __("Error saving vehicle. Please try again.") }}');
            })
            .finally(() => {
                saveCarButton.disabled = false;
            });
        });
    }
    
    // Function to load saved cars for logged-in users
    function loadSavedCars() {
        const savedCarsSection = document.querySelector('.my-cars-section');
        const savedCarsContainer = document.getElementById('savedCarsContainer');
        
        if (!savedCarsContainer) return;
        
        fetch('/customer/saved-cars')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.cars.length > 0) {
                    savedCarsContainer.innerHTML = '';
                    
                    data.cars.forEach(car => {
                        const carCard = document.createElement('div');
                        carCard.className = 'col-md-4';
                        carCard.innerHTML = `
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">${car.make_name} ${car.model_name} ${car.year}</h6>
                                    <div class="d-flex justify-content-between">
                                        <button class="btn btn-sm btn-primary select-car" 
                                                data-make="${car.make_id}" 
                                                data-model="${car.model_id}" 
                                                data-year="${car.year_id}">
                                            {{ __("Select") }}
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger delete-car" 
                                                data-id="${car.id}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                        savedCarsContainer.appendChild(carCard);
                    });
                    
                    // Show the saved cars section
                    savedCarsSection.style.display = 'block';
                    
                    // Add event listeners for select and delete buttons
                    document.querySelectorAll('.select-car').forEach(button => {
                        button.addEventListener('click', function() {
                            const makeId = this.getAttribute('data-make');
                            const modelId = this.getAttribute('data-model');
                            const yearId = this.getAttribute('data-year');
                            
                            // Set the form values
                            carMakeSelect.value = makeId;
                            loadCarModels(makeId, modelId);
                            
                            // Submit the form
                            setTimeout(() => {
                                carYearSelect.value = yearId;
                                document.getElementById('carSearchForm').submit();
                            }, 1000);
                        });
                    });
                    
                    document.querySelectorAll('.delete-car').forEach(button => {
                        button.addEventListener('click', function() {
                            if (confirm('{{ __("Are you sure you want to remove this vehicle?") }}')) {
                                const carId = this.getAttribute('data-id');
                                
                                fetch(`/customer/delete-car/${carId}`, {
                                    method: 'DELETE',
                                    headers: {
                                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                    }
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        loadSavedCars(); // Refresh the list
                                    } else {
                                        alert(data.message || '{{ __("Failed to delete vehicle") }}');
                                    }
                                })
                                .catch(error => {
                                    console.error('Error deleting car:', error);
                                    alert('{{ __("Error deleting vehicle. Please try again.") }}');
                                });
                            }
                        });
                    });
                } else {
                    // Hide the section if no saved cars
                    savedCarsSection.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error loading saved cars:', error);
                savedCarsSection.style.display = 'none';
            });
    }
    
    // Load saved cars on page load for logged-in users
    if (document.querySelector('.my-cars-section')) {
        loadSavedCars();
    }
    
    // If make is already selected (e.g. from URL parameters), load models
    if (carMakeSelect.value) {
        loadCarModels(carMakeSelect.value, "{{ request()->input('car_model') }}");
    }
});
</script> 