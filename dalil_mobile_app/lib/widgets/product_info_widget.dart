import 'package:flutter/material.dart';
import '../models/product.dart';

class ProductInfoWidget extends StatefulWidget {
  final Product product;
  final bool isWholesaleCustomer;
  final Function(int) onQuantityChanged;
  final Function(String?) onVariationChanged;

  const ProductInfoWidget({
    super.key,
    required this.product,
    required this.isWholesaleCustomer,
    required this.onQuantityChanged,
    required this.onVariationChanged,
  });

  @override
  State<ProductInfoWidget> createState() => _ProductInfoWidgetState();
}

class _ProductInfoWidgetState extends State<ProductInfoWidget> {
  int _quantity = 1;
  String? _selectedVariation;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // اسم المنتج
          Text(
            widget.product.name,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // العلامة التجارية
          if (widget.product.brand != null)
            Row(
              children: [
                Text(
                  'العلامة التجارية: ',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                Text(
                  widget.product.brand!.name,
                  style: const TextStyle(
                    color: Color(0xFF1976D2),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          
          const SizedBox(height: 12),
          
          // التقييم والمراجعات
          _buildRatingSection(),
          
          const SizedBox(height: 16),
          
          // الأسعار
          _buildPriceSection(),
          
          const SizedBox(height: 16),
          
          // حالة المخزون
          _buildStockStatus(),
          
          const SizedBox(height: 20),
          
          // الاختلافات (إن وجدت)
          if (widget.product.hasVariations)
            _buildVariationsSection(),
          
          // اختيار الكمية
          _buildQuantitySelector(),
          
          const SizedBox(height: 16),
          
          // الوصف المختصر
          if (widget.product.shortDescription != null)
            _buildShortDescription(),
          
          const SizedBox(height: 16),
          
          // معلومات إضافية
          _buildAdditionalInfo(),
        ],
      ),
    );
  }

  Widget _buildRatingSection() {
    if (!widget.product.hasRating) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        Row(
          children: List.generate(5, (index) {
            return Icon(
              index < (widget.product.rating ?? 0)
                  ? Icons.star
                  : Icons.star_border,
              color: Colors.amber,
              size: 20,
            );
          }),
        ),
        const SizedBox(width: 8),
        Text(
          '${widget.product.rating?.toStringAsFixed(1)} (${widget.product.reviewCount} مراجعة)',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildPriceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // السعر الأساسي
        Row(
          children: [
            if (widget.product.hasDiscount) ...[
              Text(
                widget.product.formattedOriginalPrice,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  decoration: TextDecoration.lineThrough,
                ),
              ),
              const SizedBox(width: 8),
            ],
            Text(
              widget.product.formattedPrice,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1976D2),
              ),
            ),
          ],
        ),
        
        // سعر الجملة (للعملاء المؤهلين)
        if (widget.isWholesaleCustomer && widget.product.hasWholesalePrice) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.business, color: Colors.green[700], size: 16),
                const SizedBox(width: 6),
                Text(
                  'سعر الجملة: ${widget.product.formattedWholesalePrice}',
                  style: TextStyle(
                    color: Colors.green[700],
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
        
        // مبلغ التوفير
        if (widget.product.hasDiscount) ...[
          const SizedBox(height: 4),
          Text(
            'توفر ${(widget.product.price - widget.product.currentPrice).toStringAsFixed(0)} د.ع',
            style: const TextStyle(
              color: Colors.green,
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStockStatus() {
    final stockStatus = widget.product.stockStatusText;
    Color statusColor;
    IconData statusIcon;

    if (!widget.product.inStock) {
      statusColor = Colors.red;
      statusIcon = Icons.cancel;
    } else if (widget.product.stockQuantity != null && widget.product.stockQuantity! <= 5) {
      statusColor = Colors.orange;
      statusIcon = Icons.warning;
    } else {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    }

    return Row(
      children: [
        Icon(statusIcon, color: statusColor, size: 20),
        const SizedBox(width: 8),
        Text(
          stockStatus,
          style: TextStyle(
            color: statusColor,
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        if (widget.product.stockQuantity != null && widget.product.stockQuantity! > 0)
          Text(
            ' (${widget.product.stockQuantity} متوفر)',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
      ],
    );
  }

  Widget _buildVariationsSection() {
    if (widget.product.variations == null || widget.product.variations!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الخيارات المتاحة:',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: widget.product.variations!.map((variation) {
            final isSelected = _selectedVariation == variation.id.toString();
            return ChoiceChip(
              label: Text('${variation.attributes.values.join(' - ')}'),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedVariation = selected ? variation.id.toString() : null;
                });
                widget.onVariationChanged(_selectedVariation);
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildQuantitySelector() {
    return Row(
      children: [
        Text(
          'الكمية:',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(width: 16),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: _quantity > 1 ? () => _updateQuantity(_quantity - 1) : null,
                icon: const Icon(Icons.remove),
                iconSize: 20,
              ),
              Container(
                width: 50,
                alignment: Alignment.center,
                child: Text(
                  _quantity.toString(),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => _updateQuantity(_quantity + 1),
                icon: const Icon(Icons.add),
                iconSize: 20,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShortDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نبذة مختصرة:',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          widget.product.shortDescription!,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildAdditionalInfo() {
    return Column(
      children: [
        _buildInfoRow(Icons.local_shipping, 'التوصيل', 'توصيل مجاني للطلبات أكثر من 100,000 د.ع'),
        _buildInfoRow(Icons.verified, 'الضمان', 'ضمان الجودة والأصالة'),
        _buildInfoRow(Icons.support_agent, 'الدعم', 'خدمة عملاء 24/7'),
        if (widget.product.sku != null)
          _buildInfoRow(Icons.qr_code, 'رمز المنتج', widget.product.sku!),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _updateQuantity(int newQuantity) {
    setState(() {
      _quantity = newQuantity;
    });
    widget.onQuantityChanged(_quantity);
  }
}
