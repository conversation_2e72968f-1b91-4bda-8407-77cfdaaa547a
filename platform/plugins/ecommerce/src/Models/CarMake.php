<?php

namespace Bo<PERSON>ble\Ecommerce\Models;

use Botble\Base\Casts\SafeContent;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Botble\Base\Models\Concerns\HasSlug;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CarMake extends BaseModel
{
    use HasSlug;

    protected $table = 'car_makes';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'logo',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
    ];

    public function models(): HasMany
    {
        return $this->hasMany(CarModel::class, 'make_id');
    }
} 