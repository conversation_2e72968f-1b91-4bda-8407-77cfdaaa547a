<?php

use <PERSON><PERSON>ble\Faq\Http\Controllers\API\FaqController;
use Botble\Faq\Http\Controllers\API\FaqCategoryController;
use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => ['api', 'throttle:api'],
    'prefix' => 'api/v1/faq',
    'namespace' => 'Botble\Faq\Http\Controllers\API',
], function (): void {
    // الأسئلة الشائعة
    Route::get('/', [FaqController::class, 'index']);
    Route::get('/{id}', [FaqController::class, 'show'])->wherePrimaryKey();
    Route::get('/search', [FaqController::class, 'search']);
    
    // فئات الأسئلة الشائعة
    Route::get('/categories', [FaqCategoryController::class, 'index']);
    Route::get('/categories/{id}', [FaqCategoryController::class, 'show'])->wherePrimaryKey();
    Route::get('/categories/{id}/faqs', [FaqCategoryController::class, 'getFaqs'])->wherePrimaryKey();
});
