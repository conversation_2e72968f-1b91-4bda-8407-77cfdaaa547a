@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        إضافة سلايدر جديد
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('revo-woocommerce.home-slider.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" role="alert">
                        <i class="ti ti-info-circle me-2"></i>
                        <strong>معلومة:</strong> السلايدر الرئيسي يظهر في أعلى الصفحة الرئيسية للتطبيق.
                    </div>

                    {!! Form::open(['route' => 'revo-woocommerce.home-slider.store', 'method' => 'POST', 'class' => 'needs-validation', 'novalidate' => true]) !!}
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label for="title" class="form-label">
                                    <i class="ti ti-heading me-1"></i>
                                    عنوان السلايدر
                                </label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="{{ old('title') }}" 
                                       placeholder="مثال: عرض خاص">
                                <div class="form-text">العنوان الذي سيظهر على السلايدر (اختياري)</div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="order" class="form-label">
                                    <i class="ti ti-sort-ascending me-1"></i>
                                    الترتيب
                                </label>
                                <input type="number" class="form-control" id="order" name="order" 
                                       value="{{ old('order', 1) }}" 
                                       placeholder="1" min="0">
                                <div class="form-text">ترتيب ظهور السلايدر</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="image" class="form-label">
                            <i class="ti ti-photo me-1"></i>
                            صورة السلايدر
                            <span class="text-danger">*</span>
                        </label>
                        {!! Form::mediaImage('image', old('image')) !!}
                        <div class="form-text">اختر صورة السلايدر (يفضل 800x400 بكسل)</div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="link" class="form-label">
                            <i class="ti ti-link me-1"></i>
                            رابط الانتقال
                        </label>
                        <input type="url" class="form-control" id="link" name="link" 
                               value="{{ old('link') }}" 
                               placeholder="https://example.com/offer">
                        <div class="form-text">الرابط الذي سينتقل إليه المستخدم عند النقر على السلايدر (اختياري)</div>
                    </div>
                    

                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <button type="submit" class="btn btn-success btn-lg me-3">
                                        <i class="ti ti-plus me-2"></i>
                                        إضافة السلايدر
                                    </button>
                                    <a href="{{ route('revo-woocommerce.home-slider.index') }}" class="btn btn-secondary btn-lg">
                                        <i class="ti ti-x me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    $(document).ready(function() {
        // Form validation
        $('.needs-validation').on('submit', function(e) {
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            $(this).addClass('was-validated');
        });
        

        
        // Show messages
        @if(session('success'))
            alert('{{ session('success') }}');
        @endif

        @if($errors->any())
            alert('{{ $errors->first() }}');
        @endif
    });
</script>
@endpush
