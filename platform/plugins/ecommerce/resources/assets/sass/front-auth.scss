@import 'wholesale-registration';

.auth-card {
    background-color: rgba(248, 249, 250, 1) !important;
    border: 0 !important;

    .card-header {
        background-color: rgba(248, 249, 250, 1) !important;
    }

    form {
        svg {
            stroke-width: 1.25;
            width: 24px;
            height: 24px;
        }

        .auth-input-icon {
            position: absolute;
            top: 3px;
            inset-inline-start: 1px;
            z-index: 10;
            background: transparent;
            border: 0;
        }

        .form-control {
            background-color: #fff;
        }
    }

    .text-decoration-underline {
        text-decoration: underline !important;
    }

    .gap-3 {
        gap: 1rem !important;
    }

    .pb-0 {
        padding-bottom: 0 !important;
    }

    .ps-5 {
        padding-inline-start: 3rem !important;
    }

    .form-check {
        display: block;
        min-height: 1.5rem;
        padding-inline-start: 1.5em;
        margin-bottom: 0.125rem;

        &.form-check-inline {
            display: inline-block;
            margin-inline-end: 1rem;
        }

        .form-check-input {
            margin-inline-start: -1.5em;

            &:checked {
                background-color: var(--primary-color);
                border-color: var(--primary-color);
            }
        }
    }

    .shop-url-status {
        top: 0;
        inset-inline-end: 0;
    }

    .text-primary {
        color: var(--primary-color) !important;
    }

    .auth-card__header {
        padding: 3rem 3rem 0;
        border: 0;

        @media (max-width: 767.98px) {
            padding: 1rem 1rem 0;
        }
    }

    .auth-card__body {
        padding: 1rem 3rem 3rem;

        @media (max-width: 767.98px) {
            padding: 1rem 1rem 3rem;
        }
    }

    .auth-card__banner {
        margin-bottom: 1rem;
        border-top-left-radius: var(--bs-card-inner-border-radius);
        border-top-right-radius: var(--bs-card-inner-border-radius);
        width: 100%;
    }

    &.auth-card__horizontal {
        .auth-card__banner {
            margin-bottom: 0;
        }

        .auth-card__left {
            padding: 0;
        }

        .auth-card__right {
            background: rgba(var(--primary-color-rgb), 0.05);
        }
    }
}

body[dir="rtl"] {
    .auth-card form .auth-input-icon {
        left: auto;
        right: 1px;
    }

    svg.icon.svg-icon-ti-ti-arrow-narrow-right {
        transform: rotate(180deg);
    }
}
