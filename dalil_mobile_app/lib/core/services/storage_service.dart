import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';
import '../config/app_config.dart';

class StorageService {
  static late SharedPreferences _prefs;
  static late Box _box;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _box = await Hive.openBox('dalil_storage');
  }

  // ===== إدارة التوكن =====
  static Future<void> saveToken(String token) async {
    await _prefs.setString(AppConfig.tokenKey, token);
  }

  static Future<String?> getToken() async {
    return _prefs.getString(AppConfig.tokenKey);
  }

  static Future<void> removeToken() async {
    await _prefs.remove(AppConfig.tokenKey);
  }

  static Future<bool> hasToken() async {
    return _prefs.containsKey(AppConfig.tokenKey);
  }

  // ===== إدارة بيانات المستخدم =====
  static Future<void> saveUser(Map<String, dynamic> userData) async {
    await _prefs.setString(AppConfig.userKey, jsonEncode(userData));
  }

  static Future<Map<String, dynamic>?> getUser() async {
    final userString = _prefs.getString(AppConfig.userKey);
    if (userString != null) {
      return jsonDecode(userString) as Map<String, dynamic>;
    }
    return null;
  }

  static Future<void> removeUser() async {
    await _prefs.remove(AppConfig.userKey);
  }

  // ===== إدارة اللغة =====
  static Future<void> saveLanguage(String languageCode) async {
    await _prefs.setString(AppConfig.languageKey, languageCode);
  }

  static Future<String> getLanguage() async {
    return _prefs.getString(AppConfig.languageKey) ?? 'ar';
  }

  // ===== إدارة الثيم =====
  static Future<void> saveTheme(String theme) async {
    await _prefs.setString(AppConfig.themeKey, theme);
  }

  static Future<String> getTheme() async {
    return _prefs.getString(AppConfig.themeKey) ?? 'system';
  }

  // ===== إدارة السيارات المحفوظة =====
  static Future<void> saveSavedVehicles(List<Map<String, dynamic>> vehicles) async {
    await _box.put(AppConfig.savedVehiclesKey, vehicles);
  }

  static Future<List<Map<String, dynamic>>> getSavedVehicles() async {
    final vehicles = _box.get(AppConfig.savedVehiclesKey);
    if (vehicles != null) {
      return List<Map<String, dynamic>>.from(vehicles);
    }
    return [];
  }

  static Future<void> addSavedVehicle(Map<String, dynamic> vehicle) async {
    final vehicles = await getSavedVehicles();
    vehicles.add(vehicle);
    await saveSavedVehicles(vehicles);
  }

  static Future<void> removeSavedVehicle(int vehicleId) async {
    final vehicles = await getSavedVehicles();
    vehicles.removeWhere((vehicle) => vehicle['id'] == vehicleId);
    await saveSavedVehicles(vehicles);
  }

  // ===== إدارة السلة =====
  static Future<void> saveCartItems(List<Map<String, dynamic>> items) async {
    await _box.put(AppConfig.cartKey, items);
  }

  static Future<List<Map<String, dynamic>>> getCartItems() async {
    final items = _box.get(AppConfig.cartKey);
    if (items != null) {
      return List<Map<String, dynamic>>.from(items);
    }
    return [];
  }

  static Future<void> addCartItem(Map<String, dynamic> item) async {
    final items = await getCartItems();
    
    // البحث عن المنتج في السلة
    final existingIndex = items.indexWhere(
      (cartItem) => cartItem['product_id'] == item['product_id'],
    );

    if (existingIndex != -1) {
      // تحديث الكمية إذا كان المنتج موجود
      items[existingIndex]['quantity'] = 
          (items[existingIndex]['quantity'] ?? 1) + (item['quantity'] ?? 1);
    } else {
      // إضافة منتج جديد
      items.add(item);
    }

    await saveCartItems(items);
  }

  static Future<void> updateCartItemQuantity(int productId, int quantity) async {
    final items = await getCartItems();
    final index = items.indexWhere((item) => item['product_id'] == productId);
    
    if (index != -1) {
      if (quantity <= 0) {
        items.removeAt(index);
      } else {
        items[index]['quantity'] = quantity;
      }
      await saveCartItems(items);
    }
  }

  static Future<void> removeCartItem(int productId) async {
    final items = await getCartItems();
    items.removeWhere((item) => item['product_id'] == productId);
    await saveCartItems(items);
  }

  static Future<void> clearCart() async {
    await _box.delete(AppConfig.cartKey);
  }

  // ===== إدارة المفضلة =====
  static Future<void> saveFavorites(List<int> productIds) async {
    await _box.put(AppConfig.favoritesKey, productIds);
  }

  static Future<List<int>> getFavorites() async {
    final favorites = _box.get(AppConfig.favoritesKey);
    if (favorites != null) {
      return List<int>.from(favorites);
    }
    return [];
  }

  static Future<void> addFavorite(int productId) async {
    final favorites = await getFavorites();
    if (!favorites.contains(productId)) {
      favorites.add(productId);
      await saveFavorites(favorites);
    }
  }

  static Future<void> removeFavorite(int productId) async {
    final favorites = await getFavorites();
    favorites.remove(productId);
    await saveFavorites(favorites);
  }

  static Future<bool> isFavorite(int productId) async {
    final favorites = await getFavorites();
    return favorites.contains(productId);
  }

  // ===== إدارة تاريخ البحث =====
  static Future<void> saveSearchHistory(List<String> searches) async {
    await _box.put('search_history', searches);
  }

  static Future<List<String>> getSearchHistory() async {
    final history = _box.get('search_history');
    if (history != null) {
      return List<String>.from(history);
    }
    return [];
  }

  static Future<void> addSearchTerm(String term) async {
    final history = await getSearchHistory();
    
    // إزالة المصطلح إذا كان موجود
    history.remove(term);
    
    // إضافة المصطلح في المقدمة
    history.insert(0, term);
    
    // الاحتفاظ بعدد محدود من المصطلحات
    if (history.length > AppConfig.maxSearchHistory) {
      history.removeRange(AppConfig.maxSearchHistory, history.length);
    }
    
    await saveSearchHistory(history);
  }

  static Future<void> clearSearchHistory() async {
    await _box.delete('search_history');
  }

  // ===== إدارة الكاش =====
  static Future<void> saveCache(String key, dynamic data, {Duration? expiry}) async {
    final cacheData = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiry': expiry?.inMilliseconds,
    };
    await _box.put('cache_$key', cacheData);
  }

  static Future<T?> getCache<T>(String key) async {
    final cacheData = _box.get('cache_$key');
    if (cacheData != null) {
      final timestamp = cacheData['timestamp'] as int;
      final expiry = cacheData['expiry'] as int?;
      
      if (expiry != null) {
        final now = DateTime.now().millisecondsSinceEpoch;
        if (now - timestamp > expiry) {
          // انتهت صلاحية الكاش
          await _box.delete('cache_$key');
          return null;
        }
      }
      
      return cacheData['data'] as T?;
    }
    return null;
  }

  static Future<void> clearCache() async {
    final keys = _box.keys.where((key) => key.toString().startsWith('cache_'));
    for (final key in keys) {
      await _box.delete(key);
    }
  }

  // ===== إدارة الإعدادات العامة =====
  static Future<void> saveSetting(String key, dynamic value) async {
    if (value is String) {
      await _prefs.setString(key, value);
    } else if (value is int) {
      await _prefs.setInt(key, value);
    } else if (value is double) {
      await _prefs.setDouble(key, value);
    } else if (value is bool) {
      await _prefs.setBool(key, value);
    } else if (value is List<String>) {
      await _prefs.setStringList(key, value);
    } else {
      await _prefs.setString(key, jsonEncode(value));
    }
  }

  static Future<T?> getSetting<T>(String key, {T? defaultValue}) async {
    if (T == String) {
      return _prefs.getString(key) as T? ?? defaultValue;
    } else if (T == int) {
      return _prefs.getInt(key) as T? ?? defaultValue;
    } else if (T == double) {
      return _prefs.getDouble(key) as T? ?? defaultValue;
    } else if (T == bool) {
      return _prefs.getBool(key) as T? ?? defaultValue;
    } else {
      final value = _prefs.getString(key);
      if (value != null) {
        try {
          return jsonDecode(value) as T;
        } catch (e) {
          return defaultValue;
        }
      }
      return defaultValue;
    }
  }

  // ===== مسح جميع البيانات =====
  static Future<void> clearAll() async {
    await _prefs.clear();
    await _box.clear();
  }
}
