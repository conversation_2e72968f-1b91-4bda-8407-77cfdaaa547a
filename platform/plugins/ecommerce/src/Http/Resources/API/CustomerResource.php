<?php

namespace Botble\Ecommerce\Http\Resources\API;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'phone_country_code' => $this->phone_country_code,
            'phone_verified_at' => $this->phone_verified_at,
            'avatar' => $this->avatar,
            'avatar_url' => $this->avatar_url,
            'status' => $this->status,
            'dob' => $this->dob,
            'is_approved' => $this->is_approved,
            'is_wholesale' => (bool) $this->is_wholesale,
            'company' => $this->company,
            'tax_id' => $this->tax_id,
            'business_license' => $this->business_license,
            'business_type' => $this->business_type,
            'years_in_business' => $this->years_in_business,
            'expected_monthly_volume' => $this->expected_monthly_volume,
            'wholesale_info' => $this->when($this->is_wholesale || $this->wholesale_application_status !== 'not_applied', [
                'application_status' => $this->wholesale_application_status,
                'application_date' => $this->wholesale_application_date,
                'approval_date' => $this->wholesale_approval_date,
                'application_notes' => $this->wholesale_application_notes,
            ]),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
