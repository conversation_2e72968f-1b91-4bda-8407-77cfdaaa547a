import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/search_provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/custom_button.dart';
import '../widgets/product_card.dart';
import '../widgets/loading_widget.dart';

class AdvancedSearchScreen extends StatefulWidget {
  const AdvancedSearchScreen({super.key});

  @override
  State<AdvancedSearchScreen> createState() => _AdvancedSearchScreenState();
}

class _AdvancedSearchScreenState extends State<AdvancedSearchScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();
  
  bool _showFilters = false;
  String _selectedCategory = '';
  String _selectedBrand = '';
  bool _inStockOnly = false;
  double _minRating = 0;
  String _sortBy = 'relevance';
  bool _ascending = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SearchProvider>().loadSearchHistory();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _minPriceController.dispose();
    _maxPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<SearchProvider, ThemeProvider>(
      builder: (context, searchProvider, themeProvider, child) {
        final isDark = themeProvider.isDarkMode;
        
        return Scaffold(
          backgroundColor: isDark ? Colors.grey[900] : Colors.grey[50],
          appBar: AppBar(
            title: const Text('البحث المتقدم'),
            backgroundColor: isDark ? Colors.grey[850] : Colors.white,
            foregroundColor: isDark ? Colors.white : Colors.black,
            bottom: TabBar(
              controller: _tabController,
              labelColor: isDark ? Colors.white : Colors.black,
              unselectedLabelColor: isDark ? Colors.grey[400] : Colors.grey[600],
              indicatorColor: Theme.of(context).primaryColor,
              tabs: const [
                Tab(text: 'البحث', icon: Icon(Icons.search)),
                Tab(text: 'النتائج', icon: Icon(Icons.list)),
                Tab(text: 'التاريخ', icon: Icon(Icons.history)),
              ],
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              _buildSearchTab(searchProvider, isDark),
              _buildResultsTab(searchProvider, isDark),
              _buildHistoryTab(searchProvider, isDark),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSearchTab(SearchProvider searchProvider, bool isDark) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // شريط البحث الرئيسي
          _buildMainSearchBar(searchProvider, isDark),
          
          const SizedBox(height: 16),
          
          // زر إظهار/إخفاء الفلاتر
          _buildFilterToggle(isDark),
          
          // قسم الفلاتر
          if (_showFilters) ...[
            const SizedBox(height: 16),
            _buildFiltersSection(isDark),
          ],
          
          const SizedBox(height: 24),
          
          // أزرار الإجراء
          _buildActionButtons(searchProvider),
          
          const SizedBox(height: 24),
          
          // اقتراحات سريعة
          _buildQuickSuggestions(searchProvider, isDark),
        ],
      ),
    );
  }

  Widget _buildMainSearchBar(SearchProvider searchProvider, bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'ابحث عن قطع الغيار...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        searchProvider.clearResults();
                      },
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            onChanged: (value) {
              setState(() {});
              if (value.length > 2) {
                searchProvider.getSearchSuggestions(value);
              }
            },
            onSubmitted: (value) => _performSearch(searchProvider),
          ),
          
          // عرض الاقتراحات
          if (searchProvider.suggestions.isNotEmpty)
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              child: Column(
                children: searchProvider.suggestions.map((suggestion) {
                  return ListTile(
                    dense: true,
                    leading: const Icon(Icons.search, size: 16),
                    title: Text(suggestion),
                    onTap: () {
                      _searchController.text = suggestion;
                      _performSearch(searchProvider);
                    },
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilterToggle(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Icon(
          _showFilters ? Icons.filter_list_off : Icons.filter_list,
          color: Theme.of(context).primaryColor,
        ),
        title: Text(
          _showFilters ? 'إخفاء الفلاتر' : 'إظهار الفلاتر المتقدمة',
          style: TextStyle(
            color: isDark ? Colors.white : Colors.black,
            fontWeight: FontWeight.w600,
          ),
        ),
        trailing: Icon(
          _showFilters ? Icons.expand_less : Icons.expand_more,
          color: isDark ? Colors.grey[400] : Colors.grey[600],
        ),
        onTap: () {
          setState(() {
            _showFilters = !_showFilters;
          });
        },
      ),
    );
  }

  Widget _buildFiltersSection(bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الفلاتر المتقدمة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          
          // فلتر السعر
          _buildPriceFilter(isDark),
          
          const SizedBox(height: 16),
          
          // فلتر التوفر
          _buildStockFilter(isDark),
          
          const SizedBox(height: 16),
          
          // فلتر التقييم
          _buildRatingFilter(isDark),
          
          const SizedBox(height: 16),
          
          // فلتر الترتيب
          _buildSortFilter(isDark),
        ],
      ),
    );
  }

  Widget _buildPriceFilter(bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نطاق السعر (ريال)',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _minPriceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'من',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextField(
                controller: _maxPriceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'إلى',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStockFilter(bool isDark) {
    return Row(
      children: [
        Checkbox(
          value: _inStockOnly,
          onChanged: (value) {
            setState(() {
              _inStockOnly = value ?? false;
            });
          },
          activeColor: Theme.of(context).primaryColor,
        ),
        Text(
          'المنتجات المتوفرة فقط',
          style: TextStyle(
            color: isDark ? Colors.white : Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildRatingFilter(bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التقييم الأدنى: ${_minRating.toStringAsFixed(1)} نجمة',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black,
          ),
        ),
        Slider(
          value: _minRating,
          min: 0,
          max: 5,
          divisions: 10,
          activeColor: Theme.of(context).primaryColor,
          onChanged: (value) {
            setState(() {
              _minRating = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildSortFilter(bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ترتيب النتائج',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _sortBy,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: const [
            DropdownMenuItem(value: 'relevance', child: Text('الأكثر صلة')),
            DropdownMenuItem(value: 'price', child: Text('السعر')),
            DropdownMenuItem(value: 'rating', child: Text('التقييم')),
            DropdownMenuItem(value: 'name', child: Text('الاسم')),
            DropdownMenuItem(value: 'newest', child: Text('الأحدث')),
          ],
          onChanged: (value) {
            setState(() {
              _sortBy = value ?? 'relevance';
            });
          },
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Radio<bool>(
              value: true,
              groupValue: _ascending,
              onChanged: (value) {
                setState(() {
                  _ascending = value ?? true;
                });
              },
              activeColor: Theme.of(context).primaryColor,
            ),
            Text('تصاعدي', style: TextStyle(color: isDark ? Colors.white : Colors.black)),
            const SizedBox(width: 16),
            Radio<bool>(
              value: false,
              groupValue: _ascending,
              onChanged: (value) {
                setState(() {
                  _ascending = value ?? true;
                });
              },
              activeColor: Theme.of(context).primaryColor,
            ),
            Text('تنازلي', style: TextStyle(color: isDark ? Colors.white : Colors.black)),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons(SearchProvider searchProvider) {
    return Row(
      children: [
        Expanded(
          child: PrimaryButton(
            text: 'بحث',
            onPressed: searchProvider.isLoading ? null : () => _performSearch(searchProvider),
            isLoading: searchProvider.isLoading,
            icon: Icons.search,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: SecondaryButton(
            text: 'مسح الفلاتر',
            onPressed: () => _clearFilters(searchProvider),
            icon: Icons.clear_all,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickSuggestions(SearchProvider searchProvider, bool isDark) {
    final quickSearches = [
      'بروجكتر',
      'فرامل',
      'إطارات',
      'زيوت',
      'فلاتر',
      'بطاريات',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'بحث سريع',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: quickSearches.map((term) {
            return ActionChip(
              label: Text(term),
              onPressed: () {
                _searchController.text = term;
                _performSearch(searchProvider);
              },
              backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
              labelStyle: TextStyle(
                color: Theme.of(context).primaryColor,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildResultsTab(SearchProvider searchProvider, bool isDark) {
    if (searchProvider.isLoading) {
      return const Center(child: LoadingWidget());
    }

    if (searchProvider.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              searchProvider.errorMessage,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            SecondaryButton(
              text: 'إعادة المحاولة',
              onPressed: () => _performSearch(searchProvider),
            ),
          ],
        ),
      );
    }

    if (!searchProvider.hasResults) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: isDark ? Colors.grey[600] : Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              searchProvider.currentQuery.isEmpty
                  ? 'ابدأ البحث للحصول على النتائج'
                  : 'لا توجد نتائج للبحث',
              style: TextStyle(
                fontSize: 16,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // إحصائيات النتائج
        _buildResultsStats(searchProvider, isDark),
        
        // قائمة النتائج
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: searchProvider.searchResults.length,
            itemBuilder: (context, index) {
              final product = searchProvider.searchResults[index];
              return ProductCard(
                product: product,
                onTap: () => _navigateToProduct(product),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildResultsStats(SearchProvider searchProvider, bool isDark) {
    final stats = searchProvider.getSearchStats();
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('النتائج', '${stats['totalResults']}', Icons.list, isDark),
          _buildStatItem('متوسط السعر', '${stats['averagePrice'].toStringAsFixed(0)} ر.س', Icons.attach_money, isDark),
          _buildStatItem('متوفر', '${stats['inStockCount']}', Icons.check_circle, isDark),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, bool isDark) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isDark ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildHistoryTab(SearchProvider searchProvider, bool isDark) {
    if (searchProvider.searchHistory.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: isDark ? Colors.grey[600] : Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا يوجد تاريخ بحث',
              style: TextStyle(
                fontSize: 16,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // زر مسح التاريخ
        Container(
          width: double.infinity,
          margin: const EdgeInsets.all(16),
          child: SecondaryButton(
            text: 'مسح تاريخ البحث',
            onPressed: () => searchProvider.clearSearchHistory(),
            icon: Icons.clear_all,
          ),
        ),
        
        // قائمة التاريخ
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: searchProvider.searchHistory.length,
            itemBuilder: (context, index) {
              final query = searchProvider.searchHistory[index];
              return Card(
                child: ListTile(
                  leading: const Icon(Icons.history),
                  title: Text(query),
                  trailing: IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => searchProvider.removeFromHistory(query),
                  ),
                  onTap: () {
                    _searchController.text = query;
                    _tabController.animateTo(0);
                    _performSearch(searchProvider);
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _performSearch(SearchProvider searchProvider) {
    if (_searchController.text.trim().isEmpty) return;

    final filter = SearchFilter(
      minPrice: double.tryParse(_minPriceController.text),
      maxPrice: double.tryParse(_maxPriceController.text),
      inStock: _inStockOnly ? true : null,
      minRating: _minRating > 0 ? _minRating : null,
      sortBy: _sortBy != 'relevance' ? _sortBy : null,
      ascending: _ascending,
    );

    searchProvider.search(_searchController.text, filter: filter);
    _tabController.animateTo(1);
  }

  void _clearFilters(SearchProvider searchProvider) {
    setState(() {
      _minPriceController.clear();
      _maxPriceController.clear();
      _inStockOnly = false;
      _minRating = 0;
      _sortBy = 'relevance';
      _ascending = true;
    });
    
    searchProvider.clearFilters();
  }

  void _navigateToProduct(product) {
    Navigator.pushNamed(
      context,
      '/product-detail',
      arguments: product,
    );
  }
}
