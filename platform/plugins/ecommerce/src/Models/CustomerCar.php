<?php

namespace Botble\Ecommerce\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerCar extends BaseModel
{
    protected $table = 'customer_cars';

    protected $fillable = [
        'customer_id',
        'make_id',
        'model_id',
        'year_id',
        'make_category_id',
        'model_category_id',
        'year_category_id',
        'vin',
        'name',
        'notes',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public function make(): BelongsTo
    {
        return $this->belongsTo(CarMake::class, 'make_id');
    }

    public function model(): BelongsTo
    {
        return $this->belongsTo(CarModel::class, 'model_id');
    }

    public function year(): BelongsTo
    {
        return $this->belongsTo(CarYear::class, 'year_id');
    }

    // علاقات مع فئات المنتجات
    public function makeCategory(): BelongsTo
    {
        return $this->belongsTo(\Botble\Ecommerce\Models\ProductCategory::class, 'make_category_id');
    }

    public function modelCategory(): BelongsTo
    {
        return $this->belongsTo(\Botble\Ecommerce\Models\ProductCategory::class, 'model_category_id');
    }

    public function yearCategory(): BelongsTo
    {
        return $this->belongsTo(\Botble\Ecommerce\Models\ProductCategory::class, 'year_category_id');
    }
} 