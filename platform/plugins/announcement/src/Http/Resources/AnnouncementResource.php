<?php

namespace ArchiElite\Announcement\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AnnouncementResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'content' => $this->content,
            'formatted_content' => $this->formatted_content,
            'has_action' => (bool) $this->has_action,
            'action_label' => $this->action_label,
            'action_url' => $this->action_url,
            'action_open_new_tab' => (bool) $this->action_open_new_tab,
            'dismissible' => (bool) $this->dismissible,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'is_active' => (bool) $this->is_active,
            'is_available' => $this->is_available,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
