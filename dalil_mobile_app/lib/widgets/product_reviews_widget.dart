import 'package:flutter/material.dart';

class ProductReviewsWidget extends StatefulWidget {
  final String productSlug;
  final double averageRating;
  final int reviewCount;

  const ProductReviewsWidget({
    super.key,
    required this.productSlug,
    required this.averageRating,
    required this.reviewCount,
  });

  @override
  State<ProductReviewsWidget> createState() => _ProductReviewsWidgetState();
}

class _ProductReviewsWidgetState extends State<ProductReviewsWidget> {
  List<ReviewData> _reviews = [];
  bool _isLoading = false;
  bool _showAllReviews = false;

  @override
  void initState() {
    super.initState();
    _loadReviews();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'آراء العملاء',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: _showAddReviewDialog,
                child: const Text('إضافة مراجعة'),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // ملخص التقييمات
          _buildRatingSummary(),
          
          const SizedBox(height: 20),
          
          // قائمة المراجعات
          if (_isLoading)
            const Center(child: CircularProgressIndicator())
          else if (_reviews.isEmpty)
            _buildEmptyState()
          else
            _buildReviewsList(),
        ],
      ),
    );
  }

  Widget _buildRatingSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // التقييم العام
          Expanded(
            child: Column(
              children: [
                Text(
                  widget.averageRating.toStringAsFixed(1),
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1976D2),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) {
                    return Icon(
                      index < widget.averageRating
                          ? Icons.star
                          : Icons.star_border,
                      color: Colors.amber,
                      size: 20,
                    );
                  }),
                ),
                const SizedBox(height: 4),
                Text(
                  '${widget.reviewCount} مراجعة',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          // توزيع النجوم
          Expanded(
            flex: 2,
            child: Column(
              children: List.generate(5, (index) {
                final starCount = 5 - index;
                final percentage = _getStarPercentage(starCount);
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    children: [
                      Text(
                        '$starCount',
                        style: const TextStyle(fontSize: 12),
                      ),
                      const SizedBox(width: 4),
                      const Icon(Icons.star, color: Colors.amber, size: 12),
                      const SizedBox(width: 8),
                      Expanded(
                        child: LinearProgressIndicator(
                          value: percentage / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: const AlwaysStoppedAnimation<Color>(Colors.amber),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${percentage.toInt()}%',
                        style: const TextStyle(fontSize: 10),
                      ),
                    ],
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsList() {
    final displayReviews = _showAllReviews ? _reviews : _reviews.take(3).toList();
    
    return Column(
      children: [
        ...displayReviews.map((review) => _buildReviewItem(review)).toList(),
        
        if (_reviews.length > 3 && !_showAllReviews)
          TextButton(
            onPressed: () {
              setState(() {
                _showAllReviews = true;
              });
            },
            child: Text('عرض جميع المراجعات (${_reviews.length})'),
          ),
      ],
    );
  }

  Widget _buildReviewItem(ReviewData review) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المراجع
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: const Color(0xFF1976D2),
                child: Text(
                  review.userName.isNotEmpty ? review.userName[0].toUpperCase() : 'ع',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review.userName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    Row(
                      children: [
                        Row(
                          children: List.generate(5, (index) {
                            return Icon(
                              index < review.rating
                                  ? Icons.star
                                  : Icons.star_border,
                              color: Colors.amber,
                              size: 16,
                            );
                          }),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          review.formattedDate,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // نص المراجعة
          Text(
            review.comment,
            style: const TextStyle(
              fontSize: 14,
              height: 1.5,
            ),
          ),
          
          // أزرار التفاعل
          const SizedBox(height: 12),
          Row(
            children: [
              TextButton.icon(
                onPressed: () => _likeReview(review.id),
                icon: Icon(
                  review.isLiked ? Icons.thumb_up : Icons.thumb_up_outlined,
                  size: 16,
                ),
                label: Text('مفيد (${review.likesCount})'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey[600],
                  textStyle: const TextStyle(fontSize: 12),
                ),
              ),
              TextButton.icon(
                onPressed: () => _reportReview(review.id),
                icon: const Icon(Icons.flag_outlined, size: 16),
                label: const Text('إبلاغ'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey[600],
                  textStyle: const TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.rate_review_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مراجعات بعد',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'كن أول من يكتب مراجعة لهذا المنتج',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _showAddReviewDialog,
            child: const Text('اكتب مراجعة'),
          ),
        ],
      ),
    );
  }

  // === دوال مساعدة ===

  double _getStarPercentage(int starCount) {
    // محاكاة توزيع النجوم
    switch (starCount) {
      case 5: return 60;
      case 4: return 25;
      case 3: return 10;
      case 2: return 3;
      case 1: return 2;
      default: return 0;
    }
  }

  Future<void> _loadReviews() async {
    setState(() => _isLoading = true);
    
    // محاكاة تحميل المراجعات
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _reviews = _getMockReviews();
      _isLoading = false;
    });
  }

  List<ReviewData> _getMockReviews() {
    return [
      ReviewData(
        id: 1,
        userName: 'أحمد محمد',
        rating: 5,
        comment: 'منتج ممتاز وجودة عالية. التوصيل كان سريع والتعامل احترافي. أنصح بالشراء.',
        date: DateTime.now().subtract(const Duration(days: 5)),
        likesCount: 12,
        isLiked: false,
      ),
      ReviewData(
        id: 2,
        userName: 'فاطمة علي',
        rating: 4,
        comment: 'المنتج جيد ومطابق للوصف. السعر مناسب والجودة مقبولة.',
        date: DateTime.now().subtract(const Duration(days: 10)),
        likesCount: 8,
        isLiked: true,
      ),
      ReviewData(
        id: 3,
        userName: 'محمد حسن',
        rating: 5,
        comment: 'تجربة رائعة! المنتج أصلي والخدمة ممتازة. سأشتري مرة أخرى بالتأكيد.',
        date: DateTime.now().subtract(const Duration(days: 15)),
        likesCount: 15,
        isLiked: false,
      ),
    ];
  }

  void _showAddReviewDialog() {
    showDialog(
      context: context,
      builder: (context) => AddReviewDialog(
        productSlug: widget.productSlug,
        onReviewAdded: (review) {
          setState(() {
            _reviews.insert(0, review);
          });
        },
      ),
    );
  }

  void _likeReview(int reviewId) {
    setState(() {
      final reviewIndex = _reviews.indexWhere((r) => r.id == reviewId);
      if (reviewIndex != -1) {
        final review = _reviews[reviewIndex];
        _reviews[reviewIndex] = ReviewData(
          id: review.id,
          userName: review.userName,
          rating: review.rating,
          comment: review.comment,
          date: review.date,
          likesCount: review.isLiked ? review.likesCount - 1 : review.likesCount + 1,
          isLiked: !review.isLiked,
        );
      }
    });
  }

  void _reportReview(int reviewId) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إرسال البلاغ. شكراً لك.'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}

class ReviewData {
  final int id;
  final String userName;
  final int rating;
  final String comment;
  final DateTime date;
  final int likesCount;
  final bool isLiked;

  ReviewData({
    required this.id,
    required this.userName,
    required this.rating,
    required this.comment,
    required this.date,
    required this.likesCount,
    required this.isLiked,
  });

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inMinutes} دقيقة';
    }
  }
}

class AddReviewDialog extends StatefulWidget {
  final String productSlug;
  final Function(ReviewData) onReviewAdded;

  const AddReviewDialog({
    super.key,
    required this.productSlug,
    required this.onReviewAdded,
  });

  @override
  State<AddReviewDialog> createState() => _AddReviewDialogState();
}

class _AddReviewDialogState extends State<AddReviewDialog> {
  final _commentController = TextEditingController();
  int _rating = 5;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة مراجعة'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // اختيار التقييم
          Row(
            children: [
              const Text('التقييم: '),
              ...List.generate(5, (index) {
                return IconButton(
                  onPressed: () {
                    setState(() {
                      _rating = index + 1;
                    });
                  },
                  icon: Icon(
                    index < _rating ? Icons.star : Icons.star_border,
                    color: Colors.amber,
                  ),
                );
              }),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // نص المراجعة
          TextField(
            controller: _commentController,
            maxLines: 4,
            decoration: const InputDecoration(
              hintText: 'اكتب مراجعتك هنا...',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _submitReview,
          child: const Text('إرسال'),
        ),
      ],
    );
  }

  void _submitReview() {
    if (_commentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى كتابة نص المراجعة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final newReview = ReviewData(
      id: DateTime.now().millisecondsSinceEpoch,
      userName: 'أنت',
      rating: _rating,
      comment: _commentController.text.trim(),
      date: DateTime.now(),
      likesCount: 0,
      isLiked: false,
    );

    widget.onReviewAdded(newReview);
    Navigator.pop(context);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إضافة مراجعتك بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
