@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-bolt me-2"></i>
                        إدارة التخفيضات السريعة
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('revo-woocommerce.flash-sale.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة تخفيض جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="ti ti-clock me-2"></i>
                        <strong>تنبيه:</strong> التخفيضات السريعة محدودة الوقت وتظهر بشكل بارز في التطبيق.
                    </div>

                    @if(count($flashSales) > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>اسم التخفيض</th>
                                        <th>نسبة الخصم</th>
                                        <th>عدد المنتجات</th>
                                        <th>تاريخ البداية</th>
                                        <th>تاريخ النهاية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($flashSales as $sale)
                                        <tr>
                                            <td>
                                                <strong>{{ $sale['name'] }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-danger">{{ $sale['discount_percentage'] }}%</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $sale['products_count'] }} منتج</span>
                                            </td>
                                            <td>
                                                <small>{{ date('Y-m-d H:i', strtotime($sale['start_date'])) }}</small>
                                            </td>
                                            <td>
                                                <small>{{ date('Y-m-d H:i', strtotime($sale['end_date'])) }}</small>
                                            </td>
                                            <td>
                                                @php
                                                    $statusColors = [
                                                        'active' => 'success',
                                                        'scheduled' => 'warning',
                                                        'expired' => 'secondary'
                                                    ];
                                                    $statusNames = [
                                                        'active' => 'نشط',
                                                        'scheduled' => 'مجدول',
                                                        'expired' => 'منتهي'
                                                    ];
                                                @endphp
                                                <span class="badge bg-{{ $statusColors[$sale['status']] ?? 'secondary' }}">
                                                    {{ $statusNames[$sale['status']] ?? $sale['status'] }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('revo-woocommerce.flash-sale.edit', $sale['id']) }}" 
                                                       class="btn btn-sm btn-warning">
                                                        <i class="ti ti-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" 
                                                            onclick="deleteFlashSale({{ $sale['id'] }})">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-bolt" style="font-size: 4rem; color: #ccc;"></i>
                            <h5 class="mt-3">لا توجد تخفيضات سريعة</h5>
                            <p class="text-muted">ابدأ بإضافة أول تخفيض سريع</p>
                            <a href="{{ route('revo-woocommerce.flash-sale.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-1"></i>
                                إضافة تخفيض جديد
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    function deleteFlashSale(id) {
        if (confirm('هل أنت متأكد من حذف هذا التخفيض؟')) {
            $.ajax({
                url: '/admin/revo-woocommerce/flash-sale/' + id,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        location.reload();
                    }
                },
                error: function() {
                    Botble.showError('حدث خطأ أثناء حذف التخفيض');
                }
            });
        }
    }
</script>
@endpush
