<?php

namespace Bo<PERSON>ble\RevoWoocommerce\Providers;

use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Base\Facades\DashboardMenu;
use Botble\Base\Supports\DashboardMenuItem;
use Illuminate\Support\ServiceProvider;
use Illuminate\Routing\Events\RouteMatched;
use Illuminate\Support\Facades\View;

class RevoWoocommerceServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        // Register any bindings or singletons here
    }

    public function boot(): void
    {
        // Check if ecommerce plugin is active
        if (! is_plugin_active('ecommerce')) {
            return;
        }

        // Register views manually
        $this->registerViews();

        $this
            ->setNamespace('plugins/revo-woocommerce-plugin-7.0.3')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['general', 'permissions'])
            ->loadAndPublishTranslations()
            ->loadRoutes(['web', 'api', 'test', 'admin'])
            ->loadMigrations()
            ->publishAssets();

        // Load API routes if API is enabled
        if (class_exists('Botble\Api\Facades\ApiHelper')) {
            $this->loadRoutes(['api']);
        }

        // Register hooks and filters
        $this->registerHooks();
    }

    protected function registerHooks(): void
    {
        // Register admin menu using Botble's system
        DashboardMenu::default()->beforeRetrieving(function (): void {
            $this->registerAdminMenu();
        });

        // Register order hooks for notifications if ecommerce is active
        if (is_plugin_active('ecommerce')) {
            add_action('ecommerce.order.created', [$this, 'handleNewOrder']);
            add_action('ecommerce.order.status_changed', [$this, 'handleOrderStatusChanged']);
        }
    }

    public function registerAdminMenu(): void
    {
        DashboardMenu::make()
            ->registerItem(
                DashboardMenuItem::make()
                    ->id('cms-plugins-revo-woocommerce')
                    ->priority(120)
                    ->name('RevoWOO - إدارة تطبيق Flutter')
                    ->icon('ti ti-device-mobile')
                    ->route('revo-woocommerce.index')
                    ->permissions('revo-woocommerce.index')
            )
            ->registerItem(
                DashboardMenuItem::make()
                    ->id('cms-plugins-revo-woocommerce-settings')
                    ->priority(1)
                    ->parentId('cms-plugins-revo-woocommerce')
                    ->name('الإعدادات')
                    ->route('revo-woocommerce.settings')
                    ->permissions('revo-woocommerce.settings')
            )
            ->registerItem(
                DashboardMenuItem::make()
                    ->id('cms-plugins-revo-woocommerce-intro-pages')
                    ->priority(2)
                    ->parentId('cms-plugins-revo-woocommerce')
                    ->name('صفحات التعريف')
                    ->route('revo-woocommerce.intro-pages.index')
                    ->permissions('revo-woocommerce.intro-pages')
            );
    }

    public function handleNewOrder($order): void
    {
        // Handle new order notifications
        // This would integrate with Firebase to send push notifications
    }

    public function handleOrderStatusChanged($order): void
    {
        // Handle order status change notifications
        // This would integrate with Firebase to send push notifications
    }

    protected function registerViews(): void
    {
        $viewPath = plugin_path('revo-woocommerce-plugin-7.0.3/resources/views');

        if (is_dir($viewPath)) {
            View::addNamespace('plugins/revo-woocommerce-plugin-7.0.3', $viewPath);
        }
    }
}
