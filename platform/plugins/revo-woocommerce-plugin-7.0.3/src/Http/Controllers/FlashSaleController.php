<?php

namespace Botble\RevoWoocommerce\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;
use Illuminate\Contracts\View\View;

class FlashSaleController extends BaseController
{
    public function index(): View
    {
        $this->pageTitle('إدارة التخفيضات السريعة');

        $flashSales = $this->getFlashSales();

        return view('plugins/revo-woocommerce-plugin-7.0.3::flash-sale.index', compact('flashSales'));
    }

    public function create(): View
    {
        $this->pageTitle('إضافة تخفيض سريع جديد');

        return view('plugins/revo-woocommerce-plugin-7.0.3::flash-sale.create');
    }

    public function store(Request $request, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'start_date' => 'required|date|after:now',
            'end_date' => 'required|date|after:start_date',
            'discount_percentage' => 'required|numeric|min:1|max:100',
            'products' => 'required|array|min:1',
            'products.*' => 'integer|exists:ec_products,id',
        ], [
            'name.required' => 'اسم التخفيض مطلوب',
            'start_date.required' => 'تاريخ البداية مطلوب',
            'start_date.after' => 'تاريخ البداية يجب أن يكون في المستقبل',
            'end_date.required' => 'تاريخ النهاية مطلوب',
            'end_date.after' => 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية',
            'discount_percentage.required' => 'نسبة الخصم مطلوبة',
            'discount_percentage.min' => 'نسبة الخصم يجب أن تكون على الأقل 1%',
            'discount_percentage.max' => 'نسبة الخصم يجب أن تكون أقل من 100%',
            'products.required' => 'يجب اختيار منتج واحد على الأقل',
            'products.*.exists' => 'المنتج المحدد غير موجود',
        ]);

        $this->storeFlashSale($request->all());

        return $response
            ->setMessage('تم إضافة التخفيض السريع بنجاح!')
            ->setNextUrl(route('revo-woocommerce.flash-sale.index'));
    }

    public function edit($id): View
    {
        $this->pageTitle('تعديل التخفيض السريع');

        $flashSale = $this->getFlashSale($id);

        return view('plugins/revo-woocommerce-plugin-7.0.3::flash-sale.edit', compact('flashSale'));
    }

    public function update(Request $request, $id, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'discount_percentage' => 'required|numeric|min:1|max:100',
            'products' => 'required|array|min:1',
            'products.*' => 'integer|exists:ec_products,id',
        ], [
            'name.required' => 'اسم التخفيض مطلوب',
            'start_date.required' => 'تاريخ البداية مطلوب',
            'end_date.required' => 'تاريخ النهاية مطلوب',
            'end_date.after' => 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية',
            'discount_percentage.required' => 'نسبة الخصم مطلوبة',
            'discount_percentage.min' => 'نسبة الخصم يجب أن تكون على الأقل 1%',
            'discount_percentage.max' => 'نسبة الخصم يجب أن تكون أقل من 100%',
            'products.required' => 'يجب اختيار منتج واحد على الأقل',
            'products.*.exists' => 'المنتج المحدد غير موجود',
        ]);

        $this->updateFlashSale($id, $request->all());

        return $response
            ->setMessage('تم تحديث التخفيض السريع بنجاح!')
            ->setNextUrl(route('revo-woocommerce.flash-sale.index'));
    }

    public function destroy($id, BaseHttpResponse $response): BaseHttpResponse
    {
        $this->deleteFlashSale($id);

        return $response
            ->setMessage('تم حذف التخفيض السريع بنجاح!');
    }

    protected function getFlashSales(): array
    {
        return [
            [
                'id' => 1,
                'name' => 'تخفيضات نهاية الأسبوع',
                'start_date' => '2025-07-26 00:00:00',
                'end_date' => '2025-07-28 23:59:59',
                'discount_percentage' => 25,
                'products_count' => 15,
                'status' => 'active',
                'created_at' => '2025-07-25 10:00:00',
            ],
            [
                'id' => 2,
                'name' => 'عرض منتصف الشهر',
                'start_date' => '2025-08-15 00:00:00',
                'end_date' => '2025-08-17 23:59:59',
                'discount_percentage' => 40,
                'products_count' => 8,
                'status' => 'scheduled',
                'created_at' => '2025-07-25 11:00:00',
            ],
            [
                'id' => 3,
                'name' => 'تصفية المخزون',
                'start_date' => '2025-07-20 00:00:00',
                'end_date' => '2025-07-24 23:59:59',
                'discount_percentage' => 60,
                'products_count' => 22,
                'status' => 'expired',
                'created_at' => '2025-07-19 09:00:00',
            ],
        ];
    }

    protected function getFlashSale($id): array
    {
        $flashSales = $this->getFlashSales();
        return collect($flashSales)->firstWhere('id', $id) ?? [];
    }

    protected function storeFlashSale(array $data): void
    {
        // Placeholder implementation - would store in database
    }

    protected function updateFlashSale($id, array $data): void
    {
        // Placeholder implementation - would update in database
    }

    protected function deleteFlashSale($id): void
    {
        // Placeholder implementation - would delete from database
    }
}
