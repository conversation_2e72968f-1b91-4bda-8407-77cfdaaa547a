import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:easy_localization/easy_localization.dart';
import '../providers/vehicle_parts_provider.dart';
import '../models/vehicle_category_model.dart';

class VehicleTypeSelector extends StatelessWidget {
  const VehicleTypeSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<VehiclePartsProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'vehicle_parts_finder.vehicle_type'.tr(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(12),
              ),
              child: provider.vehicleTypes.isEmpty
                  ? _buildLoadingState(context)
                  : _buildDropdown(context, provider),
            ),
          ],
        );
      },
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 12),
          Text(
            'common.loading'.tr(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdown(BuildContext context, VehiclePartsProvider provider) {
    return DropdownButtonHideUnderline(
      child: DropdownButton<VehicleCategoryModel>(
        value: provider.selectedVehicleType,
        hint: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Icon(
                Icons.directions_car,
                color: Colors.grey[600],
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'vehicle_parts_finder.select_vehicle_type'.tr(),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        isExpanded: true,
        icon: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: Icon(
            Icons.keyboard_arrow_down,
            color: Colors.grey[600],
          ),
        ),
        items: provider.vehicleTypes.map((vehicleType) {
          return DropdownMenuItem<VehicleCategoryModel>(
            value: vehicleType,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  _getVehicleTypeIcon(vehicleType.name),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      vehicleType.name,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  if (vehicleType.childrenCount > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${vehicleType.childrenCount}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        }).toList(),
        onChanged: (vehicleType) {
          if (vehicleType != null) {
            provider.selectVehicleType(vehicleType);
          }
        },
        dropdownColor: Colors.white,
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        menuMaxHeight: 300,
      ),
    );
  }

  Widget _getVehicleTypeIcon(String name) {
    IconData iconData;
    Color color;

    if (name.contains('سيارة') || name.toLowerCase().contains('car')) {
      iconData = Icons.directions_car;
      color = Colors.blue;
    } else if (name.contains('شاحنة') || name.toLowerCase().contains('truck')) {
      iconData = Icons.local_shipping;
      color = Colors.orange;
    } else if (name.contains('دراجة') || name.toLowerCase().contains('motorcycle')) {
      iconData = Icons.motorcycle;
      color = Colors.red;
    } else if (name.contains('حافلة') || name.toLowerCase().contains('bus')) {
      iconData = Icons.directions_bus;
      color = Colors.green;
    } else {
      iconData = Icons.directions_car;
      color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        color: color,
        size: 18,
      ),
    );
  }
}
