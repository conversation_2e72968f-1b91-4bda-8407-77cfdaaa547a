<?php

return [
    'name' => 'Customers',
    'create' => 'Create a customer',
    'edit' => 'Edit customer ":name"',
    'email' => 'Email',
    'email_placeholder' => 'Ex: <EMAIL>',
    'password' => 'Password',
    'change_password' => 'Change password?',
    'password_confirmation' => 'Password confirmation',
    'intro' => [
        'title' => 'Manage customers',
        'description' => 'When a customer buy your product(s), you will know their order histories.',
        'button_text' => 'Create customer',
    ],
    'phone' => 'Phone',
    'phone_placeholder' => 'Phone',
    'avatar' => 'Avatar',
    'dob' => 'Date of birth',
    'statuses' => [
        'activated' => 'Activated',
        'locked' => 'Locked',
    ],
    'email_verified' => 'Email verified?',
    'verify_email' => [
        'confirm_heading' => 'Verify email confirmation',
        'confirm_description' => 'Are you sure you want to verify email this customer?',
        'notification' => 'This customer is not verified email yet? :approve_link to verify email.',
        'approve_here' => 'click here',
        'confirm_button' => 'Verify',
    ],
    'private_notes' => 'Private notes',
    'private_notes_helper' => 'Private notes are only visible to admins.',
    'is_approved' => 'Approved by admin?',
    'approve_status' => [
        'pending' => 'Your account is pending approval from the administrator.',
        'approved' => 'Your account has been approved by the administrator.',
    ],
];
