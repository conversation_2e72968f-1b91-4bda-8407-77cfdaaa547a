import 'package:flutter/material.dart';
import 'featured_products_widget.dart';

class BestSellersWidget extends StatelessWidget {
  const BestSellersWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final products = _getBestSellers();
    
    return Container(
      height: 280,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: products.length,
        itemBuilder: (context, index) {
          return _buildProductCard(context, products[index], index + 1);
        },
      ),
    );
  }

  Widget _buildProductCard(BuildContext context, ProductData product, int rank) {
    return GestureDetector(
      onTap: () => _onProductTap(context, product),
      child: Container(
        width: 200,
        margin: const EdgeInsets.only(left: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المنتج
            Container(
              height: 140,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.trending_up,
                      size: 60,
                      color: Colors.grey[400],
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: _getRankColor(rank),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Center(
                        child: Text(
                          '#$rank',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // معلومات المنتج
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      product.brand,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const Spacer(),
                    Row(
                      children: [
                        Icon(
                          Icons.shopping_cart,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${product.reviewCount} مبيعة',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${product.price.toStringAsFixed(0)} د.ع',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1976D2),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return Colors.amber;
      case 2:
        return Colors.grey[400]!;
      case 3:
        return Colors.orange[300]!;
      default:
        return const Color(0xFF1976D2);
    }
  }

  List<ProductData> _getBestSellers() {
    return [
      ProductData(
        name: 'زيت محرك كاسترول',
        brand: 'كاسترول',
        price: 65000,
        originalPrice: 0,
        discount: 0,
        rating: 5,
        reviewCount: 156,
        isFavorite: false,
      ),
      ProductData(
        name: 'فلتر هواء مان',
        brand: 'مان',
        price: 25000,
        originalPrice: 0,
        discount: 0,
        rating: 4,
        reviewCount: 134,
        isFavorite: false,
      ),
      ProductData(
        name: 'شمعات إشعال NGK',
        brand: 'NGK',
        price: 15000,
        originalPrice: 0,
        discount: 0,
        rating: 5,
        reviewCount: 98,
        isFavorite: true,
      ),
      ProductData(
        name: 'بطارية فارتا',
        brand: 'فارتا',
        price: 110000,
        originalPrice: 0,
        discount: 0,
        rating: 4,
        reviewCount: 87,
        isFavorite: false,
      ),
    ];
  }

  void _onProductTap(BuildContext context, ProductData product) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض تفاصيل: ${product.name}'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
