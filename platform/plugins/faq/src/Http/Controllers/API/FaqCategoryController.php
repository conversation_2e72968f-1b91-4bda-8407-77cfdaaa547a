<?php

namespace Botble\Faq\Http\Controllers\API;

use Botble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Faq\Http\Resources\FaqCategoryResource;
use Bo<PERSON>ble\Faq\Http\Resources\FaqResource;
use Botble\Faq\Models\FaqCategory;
use Illuminate\Http\Request;

class FaqCategoryController extends BaseController
{
    /**
     * قائمة فئات الأسئلة الشائعة
     *
     * @group FAQ
     */
    public function index(Request $request)
    {
        $query = FaqCategory::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->withCount(['faqs' => function ($query) {
                $query->where('status', BaseStatusEnum::PUBLISHED);
            }]);

        // ترتيب النتائج
        $sortBy = $request->input('sort_by', 'order');
        $sortOrder = $request->input('sort_order', 'asc');
        
        if (in_array($sortBy, ['order', 'name', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder === 'desc' ? 'desc' : 'asc');
        }

        // إذا لم يكن هناك ترتيب محدد، نرتب حسب order ثم name
        if (!$request->has('sort_by')) {
            $query->orderBy('order', 'asc')->orderBy('name', 'asc');
        }

        $perPage = $request->integer('per_page', 20);
        $perPage = min($perPage, 50); // حد أقصى 50 عنصر

        $categories = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData(FaqCategoryResource::collection($categories))
            ->toApiResponse();
    }

    /**
     * عرض فئة محددة
     *
     * @group FAQ
     */
    public function show($id)
    {
        $category = FaqCategory::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->withCount(['faqs' => function ($query) {
                $query->where('status', BaseStatusEnum::PUBLISHED);
            }])
            ->findOrFail($id);

        return $this
            ->httpResponse()
            ->setData(new FaqCategoryResource($category))
            ->toApiResponse();
    }

    /**
     * الحصول على الأسئلة الشائعة لفئة محددة
     *
     * @group FAQ
     */
    public function getFaqs($id, Request $request)
    {
        $category = FaqCategory::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->findOrFail($id);

        $query = $category->faqs()
            ->where('status', BaseStatusEnum::PUBLISHED);

        // البحث في الأسئلة
        if ($request->has('search') && $request->input('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('question', 'LIKE', "%{$search}%")
                  ->orWhere('answer', 'LIKE', "%{$search}%");
            });
        }

        // ترتيب النتائج
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        
        if (in_array($sortBy, ['created_at', 'updated_at', 'question'])) {
            $query->orderBy($sortBy, $sortOrder === 'asc' ? 'asc' : 'desc');
        }

        $perPage = $request->integer('per_page', 10);
        $perPage = min($perPage, 50); // حد أقصى 50 عنصر

        $faqs = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData([
                'category' => new FaqCategoryResource($category),
                'faqs' => FaqResource::collection($faqs)
            ])
            ->toApiResponse();
    }
}
