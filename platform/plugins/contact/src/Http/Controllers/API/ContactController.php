<?php

namespace Bo<PERSON>ble\Contact\Http\Controllers\API;

use Botble\Base\Facades\EmailHandler;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Contact\Events\SentContactEvent;
use Botble\Contact\Forms\Fronts\ContactForm;
use Botble\Contact\Http\Requests\ContactRequest;
use Botble\Contact\Http\Resources\ContactResource;
use Botble\Contact\Http\Resources\CustomFieldResource;
use Botble\Contact\Models\Contact;
use Botble\Contact\Models\CustomField;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ContactController extends BaseController
{
    /**
     * إرسال رسالة اتصال جديدة
     *
     * @group Contact
     */
    public function store(ContactRequest $request)
    {
        // فحص القائمة السوداء للبريد الإلكتروني
        $blacklistDomains = setting('blacklist_email_domains');
        
        if ($blacklistDomains && $request->input('email')) {
            $emailDomain = Str::after(strtolower($request->input('email')), '@');
            $blacklistDomains = collect(json_decode($blacklistDomains, true))->pluck('value')->all();
            
            if (in_array($emailDomain, $blacklistDomains)) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setCode(422)
                    ->setMessage(__('Your email is in blacklist. Please use another email address.'));
            }
        }

        // فحص الكلمات المحظورة
        $blacklistKeywords = setting('blacklist_keywords');
        if ($blacklistKeywords) {
            $blacklistKeywords = collect(json_decode($blacklistKeywords, true))->pluck('value')->all();
            $content = strtolower($request->input('content'));
            
            foreach ($blacklistKeywords as $keyword) {
                if (Str::contains($content, strtolower($keyword))) {
                    return $this
                        ->httpResponse()
                        ->setError()
                        ->setCode(422)
                        ->setMessage(__('Your message contains prohibited content.'));
                }
            }
        }

        try {
            // إنشاء النموذج والحصول على البيانات
            $form = ContactForm::create();
            $data = $request->only([
                'name',
                'email', 
                'phone',
                'address',
                'subject',
                'content'
            ]);

            // معالجة الحقول المخصصة
            $customFields = [];
            $customFieldsData = $request->input('custom_fields', []);
            
            if ($customFieldsData) {
                $availableCustomFields = CustomField::query()
                    ->wherePublished()
                    ->get()
                    ->keyBy('name');
                    
                foreach ($customFieldsData as $fieldName => $fieldValue) {
                    if ($availableCustomFields->has($fieldName)) {
                        $customFields[$fieldName] = $fieldValue;
                    }
                }
            }
            
            $data['custom_fields'] = $customFields;

            // إنشاء جهة الاتصال
            $contact = new Contact();
            $contact->fill($data);
            $contact->save();

            // إرسال الحدث
            event(new SentContactEvent($contact));

            // إرسال البريد الإلكتروني
            $args = [];
            if ($contact->name && $contact->email) {
                $args = ['replyTo' => [$contact->name => $contact->email]];
            }

            $emailHandler = EmailHandler::setModule(CONTACT_MODULE_SCREEN_NAME)
                ->setVariableValues([
                    'contact_name' => $contact->name,
                    'contact_subject' => $contact->subject,
                    'contact_email' => $contact->email,
                    'contact_phone' => $contact->phone,
                    'contact_address' => $contact->address,
                    'contact_content' => $contact->content,
                    'contact_custom_fields' => $customFields,
                ]);

            if (setting('contact_form_send_to_admin', true)) {
                $emailHandler->sendUsingTemplate('contact', setting('email_from_address') ?: config('mail.from.address'), $args);
            }

            if (setting('contact_form_send_to_customer', false) && $contact->email) {
                $emailHandler->sendUsingTemplate('contact-customer', $contact->email);
            }

            return $this
                ->httpResponse()
                ->setData(new ContactResource($contact))
                ->setMessage(__('Send contact successfully!'));

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while sending your message. Please try again.'));
        }
    }

    /**
     * الحصول على مواضيع الاتصال المتاحة
     *
     * @group Contact
     */
    public function getSubjects()
    {
        $subjects = setting('contact_form_subjects', []);
        
        if (is_string($subjects)) {
            $subjects = json_decode($subjects, true) ?: [];
        }
        
        $subjectsList = collect($subjects)->pluck('value')->filter()->values();
        
        return $this
            ->httpResponse()
            ->setData($subjectsList)
            ->toApiResponse();
    }

    /**
     * الحصول على الحقول المخصصة للنموذج
     *
     * @group Contact
     */
    public function getCustomFields()
    {
        $customFields = CustomField::query()
            ->wherePublished()
            ->oldest('order')
            ->with('options')
            ->get();

        return $this
            ->httpResponse()
            ->setData(CustomFieldResource::collection($customFields))
            ->toApiResponse();
    }

    /**
     * فحص حالة البريد الإلكتروني
     *
     * @group Contact
     */
    public function checkEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        $email = $request->input('email');
        $emailDomain = Str::after(strtolower($email), '@');
        
        $blacklistDomains = setting('blacklist_email_domains');
        $isBlacklisted = false;
        
        if ($blacklistDomains) {
            $blacklistDomains = collect(json_decode($blacklistDomains, true))->pluck('value')->all();
            $isBlacklisted = in_array($emailDomain, $blacklistDomains);
        }

        return $this
            ->httpResponse()
            ->setData([
                'email' => $email,
                'domain' => $emailDomain,
                'is_blacklisted' => $isBlacklisted,
                'is_valid' => !$isBlacklisted
            ])
            ->toApiResponse();
    }
}
