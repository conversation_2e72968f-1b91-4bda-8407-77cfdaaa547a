@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">اختبار تحديث السلايدر مع Debug</h4>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif
                    
                    @if(session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif
                    
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <ul>
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    
                    <div class="alert alert-info">
                        <h5>معلومات Debug:</h5>
                        <p><strong>البيانات الحالية:</strong></p>
                        <pre>{{ json_encode(json_decode(\Botble\Setting\Facades\Setting::get('revo_home_sliders', '[]'), true), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                    </div>
                    
                    <form action="{{ route('revo-woocommerce.home-slider.update', 1) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="form-group mb-3">
                            <label for="title">العنوان الجديد</label>
                            <input type="text" class="form-control" id="title" name="title" value="تحديث جديد {{ now()->format('H:i:s') }}">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="image">الصورة الجديدة</label>
                            <input type="text" class="form-control" id="image" name="image" value="https://dalilakauto.com/storage/updated-{{ rand(1,100) }}.png" required>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="link">الرابط الجديد</label>
                            <input type="url" class="form-control" id="link" name="link" value="https://updated{{ rand(1,100) }}.com">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="order">الترتيب الجديد</label>
                            <input type="number" class="form-control" id="order" name="order" value="{{ rand(1,10) }}">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">تحديث السلايدر #1</button>
                    </form>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>اختبارات سريعة:</h5>
                            <a href="{{ route('revo-woocommerce.test-update', 1) }}" class="btn btn-info btn-sm">تحديث مباشر #1</a>
                            <a href="{{ route('revo-woocommerce.debug-sliders') }}" class="btn btn-secondary btn-sm">عرض البيانات</a>
                            <a href="/api/revo-admin/v1/home/<USER>" class="btn btn-success btn-sm" target="_blank">API</a>
                        </div>
                        <div class="col-md-6">
                            <h5>آخر تحديث:</h5>
                            <p>{{ now()->format('Y-m-d H:i:s') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
