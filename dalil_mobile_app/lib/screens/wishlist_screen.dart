import 'package:flutter/material.dart';
import '../models/product.dart';
import '../services/product_service_new.dart';
import '../widgets/product_grid_widget.dart';

class WishlistScreen extends StatefulWidget {
  const WishlistScreen({super.key});

  @override
  State<WishlistScreen> createState() => _WishlistScreenState();
}

class _WishlistScreenState extends State<WishlistScreen> {
  final ProductServiceNew _productService = ProductServiceNew();
  
  List<Product> _wishlistProducts = [];
  bool _isLoading = true;
  bool _isSelectionMode = false;
  Set<int> _selectedProducts = {};

  @override
  void initState() {
    super.initState();
    _loadWishlist();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _wishlistProducts.isEmpty
              ? _buildEmptyWishlist()
              : _buildWishlistContent(),
      bottomNavigationBar: _isSelectionMode && _selectedProducts.isNotEmpty
          ? _buildSelectionBottomBar()
          : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(_isSelectionMode 
          ? '${_selectedProducts.length} محدد'
          : 'المفضلة (${_wishlistProducts.length})'),
      actions: [
        if (_wishlistProducts.isNotEmpty && !_isSelectionMode)
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: _enterSelectionMode,
            tooltip: 'تحديد متعدد',
          ),
        if (_isSelectionMode) ...[
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: _selectAll,
            tooltip: 'تحديد الكل',
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: _exitSelectionMode,
            tooltip: 'إلغاء التحديد',
          ),
        ] else if (_wishlistProducts.isNotEmpty)
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'clear_all':
                  _showClearAllDialog();
                  break;
                case 'share':
                  _shareWishlist();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('مشاركة المفضلة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all, color: Colors.red),
                    SizedBox(width: 8),
                    Text('مسح الكل', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildWishlistContent() {
    return Column(
      children: [
        // معلومات المفضلة
        if (!_isSelectionMode)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1976D2).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF1976D2).withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.favorite,
                  color: const Color(0xFF1976D2),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'لديك ${_wishlistProducts.length} منتج في المفضلة',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                ),
                TextButton(
                  onPressed: _addAllToCart,
                  child: const Text('إضافة الكل للسلة'),
                ),
              ],
            ),
          ),

        // شبكة المنتجات
        Expanded(
          child: _isSelectionMode
              ? _buildSelectionGrid()
              : ProductGridWidget(
                  products: _wishlistProducts,
                  onProductTap: (product) {
                    // الانتقال لصفحة تفاصيل المنتج
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildSelectionGrid() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: _wishlistProducts.length,
      itemBuilder: (context, index) {
        final product = _wishlistProducts[index];
        final isSelected = _selectedProducts.contains(product.id);
        
        return GestureDetector(
          onTap: () => _toggleProductSelection(product.id),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected ? const Color(0xFF1976D2) : Colors.grey[200]!,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: isSelected ? [
                BoxShadow(
                  color: const Color(0xFF1976D2).withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ] : [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // صورة المنتج
                    Expanded(
                      flex: 3,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(16),
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(16),
                          ),
                          child: Image.network(
                            product.mainImageUrl,
                            width: double.infinity,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Center(
                                child: Icon(
                                  Icons.image_not_supported,
                                  size: 40,
                                  color: Colors.grey[400],
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                    
                    // معلومات المنتج
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              product.name,
                              style: const TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const Spacer(),
                            Text(
                              product.formattedPrice,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF1976D2),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                
                // أيقونة التحديد
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: isSelected ? const Color(0xFF1976D2) : Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? const Color(0xFF1976D2) : Colors.grey[400]!,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : null,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyWishlist() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite_border,
              size: 120,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 24),
            Text(
              'المفضلة فارغة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'ابدأ بإضافة المنتجات التي تعجبك للمفضلة',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: const Icon(Icons.shopping_bag),
              label: const Text('تصفح المنتجات'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _removeSelectedFromWishlist,
                icon: const Icon(Icons.delete_outline),
                label: Text('حذف (${_selectedProducts.length})'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: const BorderSide(color: Colors.red),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _addSelectedToCart,
                icon: const Icon(Icons.shopping_cart),
                label: Text('إضافة للسلة (${_selectedProducts.length})'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // === دوال الأحداث ===

  Future<void> _loadWishlist() async {
    setState(() => _isLoading = true);

    try {
      final response = await _productService.getWishlist();
      if (response.isSuccess) {
        setState(() {
          _wishlistProducts = response.data!;
        });
      } else {
        _showError(response.error ?? 'فشل في تحميل المفضلة');
      }
    } catch (e) {
      _showError('خطأ في تحميل المفضلة: ${e.toString()}');
    }

    setState(() => _isLoading = false);
  }

  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
      _selectedProducts.clear();
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedProducts.clear();
    });
  }

  void _toggleProductSelection(int productId) {
    setState(() {
      if (_selectedProducts.contains(productId)) {
        _selectedProducts.remove(productId);
      } else {
        _selectedProducts.add(productId);
      }
    });
  }

  void _selectAll() {
    setState(() {
      if (_selectedProducts.length == _wishlistProducts.length) {
        _selectedProducts.clear();
      } else {
        _selectedProducts = _wishlistProducts.map((p) => p.id).toSet();
      }
    });
  }

  void _addAllToCart() {
    _showSuccess('تم إضافة جميع المنتجات للسلة');
  }

  void _addSelectedToCart() {
    _showSuccess('تم إضافة ${_selectedProducts.length} منتج للسلة');
    _exitSelectionMode();
  }

  void _removeSelectedFromWishlist() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف من المفضلة'),
        content: Text('هل تريد حذف ${_selectedProducts.length} منتج من المفضلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performRemoveSelected();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _performRemoveSelected() {
    setState(() {
      _wishlistProducts.removeWhere((p) => _selectedProducts.contains(p.id));
    });
    _showSuccess('تم حذف ${_selectedProducts.length} منتج من المفضلة');
    _exitSelectionMode();
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح المفضلة'),
        content: const Text('هل تريد حذف جميع المنتجات من المفضلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _clearAllWishlist();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('مسح الكل'),
          ),
        ],
      ),
    );
  }

  void _clearAllWishlist() {
    setState(() {
      _wishlistProducts.clear();
    });
    _showSuccess('تم مسح جميع المنتجات من المفضلة');
  }

  void _shareWishlist() {
    _showSuccess('تم نسخ رابط المفضلة');
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
