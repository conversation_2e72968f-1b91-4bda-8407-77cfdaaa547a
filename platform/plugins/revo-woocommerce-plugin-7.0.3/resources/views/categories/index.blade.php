@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-category me-2"></i>
                        إدارة الفئات
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" role="alert">
                        <i class="ti ti-info-circle me-2"></i>
                        <strong>معلومة:</strong> يمكنك تخصيص ترتيب وعرض الفئات في التطبيق.
                    </div>

                    @if(count($categories) > 0)
                        <div class="row">
                            @foreach($categories as $category)
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <img src="{{ $category['image'] }}" alt="{{ $category['name'] }}" 
                                                 class="img-fluid rounded mb-3" style="width: 80px; height: 80px; object-fit: cover;">
                                            <h6 class="card-title">{{ $category['name'] }}</h6>
                                            <p class="text-muted small">{{ $category['products_count'] }} منتج</p>
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge bg-secondary">ترتيب: {{ $category['order'] }}</span>
                                                @if($category['visible_in_app'])
                                                    <span class="badge bg-success">ظاهر</span>
                                                @else
                                                    <span class="badge bg-danger">مخفي</span>
                                                @endif
                                            </div>
                                            
                                            <div class="mt-3">
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="toggleVisibility({{ $category['id'] }})">
                                                    <i class="ti ti-eye{{ $category['visible_in_app'] ? '-off' : '' }}"></i>
                                                    {{ $category['visible_in_app'] ? 'إخفاء' : 'إظهار' }}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-category" style="font-size: 4rem; color: #ccc;"></i>
                            <h5 class="mt-3">لا توجد فئات</h5>
                            <p class="text-muted">لا توجد فئات منتجات متاحة حالياً</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    function toggleVisibility(id) {
        $.ajax({
            url: '/admin/revo-woocommerce/categories/' + id + '/toggle-visibility',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.error) {
                    Botble.showError(response.message);
                } else {
                    Botble.showSuccess(response.message);
                    location.reload();
                }
            },
            error: function() {
                Botble.showError('حدث خطأ أثناء تحديث حالة الفئة');
            }
        });
    }
</script>
@endpush
