@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">🔍 Debug صفحة التعديل - السلايدر #1</h4>
                </div>
                <div class="card-body">
                    @php
                        // Get current slider data
                        $slidersJson = \Botble\Setting\Facades\Setting::get('revo_home_sliders', '[]');
                        $sliders = json_decode($slidersJson, true) ?: [];
                        $currentSlider = null;
                        
                        foreach ($sliders as $slider) {
                            if ((int)$slider['id'] === 1) {
                                $currentSlider = $slider;
                                break;
                            }
                        }
                    @endphp
                    
                    <div class="alert alert-info">
                        <h5>📊 البيانات الحالية للسلايدر #1:</h5>
                        <pre>{{ json_encode($currentSlider, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                    </div>
                    
                    @if(session('success'))
                        <div class="alert alert-success">✅ {{ session('success') }}</div>
                    @endif
                    
                    @if(session('error'))
                        <div class="alert alert-danger">❌ {{ session('error') }}</div>
                    @endif
                    
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <h6>🚨 أخطاء التحقق:</h6>
                            <ul>
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>📝 نموذج التعديل:</h5>
                            <form action="{{ route('revo-woocommerce.simple-update', 1) }}" method="POST" id="debug-form">
                                @csrf
                                @method('PUT')
                                
                                <div class="form-group mb-3">
                                    <label for="title">العنوان:</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="{{ $currentSlider['title'] ?? '' }}" required>
                                    <small class="text-muted">القيمة الحالية: {{ $currentSlider['title'] ?? 'غير محدد' }}</small>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="image">الصورة:</label>
                                    <input type="text" class="form-control" id="image" name="image" 
                                           value="{{ $currentSlider['image'] ?? '' }}" required>
                                    <small class="text-muted">القيمة الحالية: {{ $currentSlider['image'] ?? 'غير محدد' }}</small>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="link">الرابط:</label>
                                    <input type="url" class="form-control" id="link" name="link" 
                                           value="{{ $currentSlider['link'] ?? '' }}">
                                    <small class="text-muted">القيمة الحالية: {{ $currentSlider['link'] ?? 'غير محدد' }}</small>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="order">الترتيب:</label>
                                    <input type="number" class="form-control" id="order" name="order" 
                                           value="{{ $currentSlider['order'] ?? 1 }}">
                                    <small class="text-muted">القيمة الحالية: {{ $currentSlider['order'] ?? 'غير محدد' }}</small>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">💾 حفظ التعديلات</button>
                                <button type="button" class="btn btn-info" onclick="testUpdate()">🧪 اختبار التحديث</button>
                            </form>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>🔗 روابط سريعة:</h5>
                            <div class="d-grid gap-2">
                                <a href="/api/revo-admin/v1/home/<USER>" class="btn btn-success btn-sm" target="_blank">
                                    📡 عرض API
                                </a>
                                <a href="{{ route('revo-woocommerce.debug-sliders') }}" class="btn btn-secondary btn-sm" target="_blank">
                                    🔍 Debug البيانات
                                </a>
                                <a href="{{ route('revo-woocommerce.quick-update', 1) }}" class="btn btn-warning btn-sm" target="_blank">
                                    ⚡ تحديث سريع
                                </a>
                            </div>
                            
                            <hr>
                            
                            <h6>📈 إحصائيات:</h6>
                            <ul>
                                <li>عدد السلايدرز: {{ count($sliders) }}</li>
                                <li>آخر تحديث: {{ now()->format('Y-m-d H:i:s') }}</li>
                                <li>حالة السلايدر #1: {{ $currentSlider ? '✅ موجود' : '❌ غير موجود' }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testUpdate() {
            const formData = new FormData(document.getElementById('debug-form'));
            const data = Object.fromEntries(formData);
            
            console.log('🧪 بيانات النموذج:', data);
            alert('تحقق من Console للبيانات المرسلة');
        }
        
        // Log form submission
        document.getElementById('debug-form').addEventListener('submit', function(e) {
            console.log('📤 إرسال النموذج...');
            console.log('Action:', this.action);
            console.log('Method:', this.method);
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            console.log('البيانات المرسلة:', data);
        });
    </script>
@endsection
