$((function(){$(document).on("click","#confirm-vendor-button",(function(e){e.preventDefault();var o=$(e.currentTarget),t=o.closest("form"),n=o.closest(".modal");$httpClient.make().withButtonLoading(o).post(t.prop("action"),t.serialize()).then((function(e){var o=e.data;n.modal("hide"),o.error?Botble.showError(o.message):(Botble.showSuccess(o.message),setTimeout((function(){window.location.href=route("marketplace.unverified-vendors.index")}),3e3))}))}))}));