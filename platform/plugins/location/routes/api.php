<?php

use Botble\Location\Http\Controllers\API\CountryController;
use Botble\Location\Http\Controllers\API\StateController;
use Botble\Location\Http\Controllers\API\CityController;
use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => ['api', 'throttle:api'],
    'prefix' => 'api/v1/location',
    'namespace' => 'Botble\Location\Http\Controllers\API',
], function (): void {
    // Countries
    Route::get('/countries', [CountryController::class, 'index']);
    Route::get('/countries/{id}', [CountryController::class, 'show'])->wherePrimaryKey();
    Route::get('/countries/{id}/states', [CountryController::class, 'getStates'])->wherePrimaryKey();
    Route::get('/countries/{id}/cities', [CountryController::class, 'getCities'])->wherePrimaryKey();
    
    // States
    Route::get('/states', [StateController::class, 'index']);
    Route::get('/states/{id}', [StateController::class, 'show'])->wherePrimaryKey();
    Route::get('/states/{id}/cities', [StateController::class, 'getCities'])->wherePrimaryKey();
    
    // Cities
    Route::get('/cities', [CityController::class, 'index']);
    Route::get('/cities/{id}', [CityController::class, 'show'])->wherePrimaryKey();
    Route::get('/cities/search', [CityController::class, 'search']);
});
