<?php

namespace Bo<PERSON>ble\Ecommerce\Forms;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Botble\Base\Forms\FieldOptions\MediaImageFieldOption;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\TextareaFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\MediaImageField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextAreaField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Botble\Ecommerce\Http\Requests\CarMakeRequest;
use Botble\Ecommerce\Models\CarMake;

class CarMakeForm extends FormAbstract
{
    public function buildForm(): void
    {
        $this
            ->setupModel(new CarMake())
            ->setValidatorClass(CarMakeRequest::class)
            ->withCustomFields()
            ->add(
                'name',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('core/base::forms.name'))
                    ->placeholder(trans('core/base::forms.name_placeholder'))
                    ->required()
                    ->toArray()
            )
            ->add('slug', 'text', [
                'label' => trans('core/base::forms.slug'),
                'required' => true,
                'attr' => [
                    'data-slug-from' => 'name',
                    'placeholder' => trans('core/base::forms.slug_placeholder'),
                    'class' => 'form-control',
                    'data-counter' => 120,
                ],
            ])
            ->add(
                'description',
                TextAreaField::class,
                TextareaFieldOption::make()
                    ->label(trans('core/base::forms.description'))
                    ->placeholder(trans('core/base::forms.description_placeholder'))
                    ->toArray()
            )
            ->add(
                'logo',
                MediaImageField::class,
                MediaImageFieldOption::make()
                    ->label(trans('plugins/ecommerce::car-makes.form.logo'))
                    ->toArray()
            )
            ->add(
                'status',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('core/base::tables.status'))
                    ->choices(BaseStatusEnum::labels())
                    ->selected(BaseStatusEnum::PUBLISHED)
                    ->toArray()
            )
            ->setBreakFieldPoint('status')
            ->addHtml('
                <script>
                    document.addEventListener("DOMContentLoaded", function() {
                        const nameInput = document.querySelector("input[name=\'name\']");
                        const slugInput = document.querySelector("input[name=\'slug\']");

                        if (nameInput && slugInput) {
                            nameInput.addEventListener("input", function() {
                                const slug = nameInput.value
                                    .toLowerCase()
                                    .replace(/\s+/g, "-")
                                    .replace(/[^\w\-]+/g, "")
                                    .replace(/\-\-+/g, "-")
                                    .replace(/^-+/, "")
                                    .replace(/-+$/, "");

                                slugInput.value = slug;
                            });
                        }
                    });
                </script>
            ');
    }
}
