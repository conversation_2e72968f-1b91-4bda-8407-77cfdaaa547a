$((function(){function a(a,e){e.addClass("button-loading"),$.ajax({type:"POST",cache:!1,url:a.prop("action"),data:a.serialize(),success:function(a){a.error?Botble.showNotice("error",a.message):(Botble.showNotice("success",a.message),e.closest(".modal").modal("hide"),window.LaravelDataTables&&Object.keys(window.LaravelDataTables).map((function(a){window.LaravelDataTables[a].draw()})),a.data&&a.data.balance&&$(".vendor-balance").text(a.data.balance))},error:function(a){Botble.handleError(a)},complete:function(){e.removeClass("button-loading")}})}$(document).on("click","#confirm-update-amount-button",(function(e){e.preventDefault();var o=$(e.currentTarget);a($("#update-balance-modal .modal-body form"),o)})),$(document).on("submit","#update-balance-modal .modal-body form",(function(e){e.preventDefault(),a($(e.currentTarget),$("#confirm-update-amount-button"))}))}));