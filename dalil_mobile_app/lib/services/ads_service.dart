import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';

class AdsService {
  // جلب جميع الإعلانات
  static Future<List<Map<String, dynamic>>> getAllAds() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/ads'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }

      return [];
    } catch (e) {
      print('خطأ في جلب الإعلانات: $e');
      return [];
    }
  }

  // جلب إعلانات محددة بالمفاتيح
  static Future<List<Map<String, dynamic>>> getAdsByKeys(List<String> keys) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.apiBaseUrl}/ads'),
        headers: AppConfig.defaultHeaders,
        body: json.encode({
          'keys': keys
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }

      return [];
    } catch (e) {
      print('خطأ في جلب الإعلانات: $e');
      return [];
    }
  }

  // جلب إعلانات الصفحة الرئيسية
  static Future<List<Map<String, dynamic>>> getHomeBanners() async {
    try {
      final banners = await getAdsByKeys(['homepage-banner', 'home-banner', 'main-banner']);
      if (banners.isNotEmpty) {
        return banners;
      }

      // إذا لم توجد إعلانات من API، استخدم البيانات الافتراضية
      return getDefaultAds();
    } catch (e) {
      print('خطأ في جلب إعلانات الصفحة الرئيسية: $e');
      return getDefaultAds();
    }
  }

  // جلب إعلانات الشريط الجانبي
  static Future<List<Map<String, dynamic>>> getSidebarBanners() async {
    return await getAdsByKeys(['sidebar-banner', 'side-banner']);
  }

  // جلب إعلانات الفئات
  static Future<List<Map<String, dynamic>>> getCategoryBanners() async {
    return await getAdsByKeys(['category-banner', 'categories-banner']);
  }

  // تنسيق الإعلان للعرض
  static Map<String, dynamic> formatAd(Map<String, dynamic> ad) {
    return {
      'key': ad['key'] ?? '',
      'name': ad['name'] ?? '',
      'image': ad['image'] ?? '',
      'tablet_image': ad['tablet_image'] ?? ad['image'] ?? '',
      'mobile_image': ad['mobile_image'] ?? ad['tablet_image'] ?? ad['image'] ?? '',
      'link': ad['link'] ?? '',
      'order': ad['order'] ?? 0,
      'open_in_new_tab': ad['open_in_new_tab'] ?? true,
      'ads_type': ad['ads_type'] ?? 'custom_ad',
    };
  }

  // الحصول على الصورة المناسبة حسب نوع الجهاز
  static String getImageForDevice(Map<String, dynamic> ad, {String deviceType = 'mobile'}) {
    switch (deviceType) {
      case 'tablet':
        return ad['tablet_image'] ?? ad['image'] ?? '';
      case 'mobile':
        return ad['mobile_image'] ?? ad['tablet_image'] ?? ad['image'] ?? '';
      default:
        return ad['image'] ?? '';
    }
  }

  // الحصول على إعلانات افتراضية للتجربة
  static List<Map<String, dynamic>> getDefaultAds() {
    return [
      {
        'key': 'homepage-banner-1',
        'name': 'بنر الصفحة الرئيسية 1',
        'image': 'https://via.placeholder.com/600x200/1976D2/FFFFFF?text=عروض+خاصة',
        'tablet_image': 'https://via.placeholder.com/600x200/1976D2/FFFFFF?text=عروض+خاصة',
        'mobile_image': 'https://via.placeholder.com/600x200/1976D2/FFFFFF?text=عروض+خاصة',
        'link': '/products',
        'order': 1,
        'open_in_new_tab': false,
        'ads_type': 'custom_ad',
      },
      {
        'key': 'homepage-banner-2',
        'name': 'بنر الصفحة الرئيسية 2',
        'image': 'https://via.placeholder.com/600x200/4CAF50/FFFFFF?text=منتجات+جديدة',
        'tablet_image': 'https://via.placeholder.com/600x200/4CAF50/FFFFFF?text=منتجات+جديدة',
        'mobile_image': 'https://via.placeholder.com/600x200/4CAF50/FFFFFF?text=منتجات+جديدة',
        'link': '/categories',
        'order': 2,
        'open_in_new_tab': false,
        'ads_type': 'custom_ad',
      },
      {
        'key': 'category-banner',
        'name': 'بنر الفئات',
        'image': 'https://via.placeholder.com/600x200/FF9800/FFFFFF?text=خصومات+كبيرة',
        'tablet_image': 'https://via.placeholder.com/600x200/FF9800/FFFFFF?text=خصومات+كبيرة',
        'mobile_image': 'https://via.placeholder.com/600x200/FF9800/FFFFFF?text=خصومات+كبيرة',
        'link': '/special-offers',
        'order': 3,
        'open_in_new_tab': false,
        'ads_type': 'custom_ad',
      },
    ];
  }

  // فلترة الإعلانات حسب النوع
  static List<Map<String, dynamic>> filterAdsByType(List<Map<String, dynamic>> ads, String type) {
    return ads.where((ad) => ad['ads_type'] == type).toList();
  }

  // ترتيب الإعلانات حسب الترتيب
  static List<Map<String, dynamic>> sortAdsByOrder(List<Map<String, dynamic>> ads) {
    ads.sort((a, b) => (a['order'] ?? 0).compareTo(b['order'] ?? 0));
    return ads;
  }
}
