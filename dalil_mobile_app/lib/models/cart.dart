import 'product.dart';

class Cart {
  final List<CartItem> items;
  final double subtotal;
  final double tax;
  final double shipping;
  final double discount;
  final double total;
  final String? couponCode;
  final DateTime? updatedAt;

  Cart({
    required this.items,
    required this.subtotal,
    required this.tax,
    required this.shipping,
    required this.discount,
    required this.total,
    this.couponCode,
    this.updatedAt,
  });

  factory Cart.fromJson(Map<String, dynamic> json) {
    return Cart(
      items: (json['items'] as List?)
          ?.map((item) => CartItem.fromJson(item))
          .toList() ?? [],
      subtotal: (json['subtotal'] ?? 0).toDouble(),
      tax: (json['tax'] ?? 0).toDouble(),
      shipping: (json['shipping'] ?? 0).toDouble(),
      discount: (json['discount'] ?? 0).toDouble(),
      total: (json['total'] ?? 0).toDouble(),
      couponCode: json['coupon_code'],
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'shipping': shipping,
      'discount': discount,
      'total': total,
      'coupon_code': couponCode,
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // دوال مساعدة
  int get itemsCount => items.fold(0, (sum, item) => sum + item.quantity);
  
  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
  
  bool get hasDiscount => discount > 0;
  bool get hasCoupon => couponCode != null && couponCode!.isNotEmpty;
  bool get hasWholesaleDiscount => items.any((item) => item.hasWholesalePrice);
  
  String get formattedSubtotal => _formatPrice(subtotal);
  String get formattedTax => _formatPrice(tax);
  String get formattedShipping => _formatPrice(shipping);
  String get formattedDiscount => _formatPrice(discount);
  String get formattedTotal => _formatPrice(total);
  String get formattedSavings => _formatPrice(discount);

  double get wholesaleTotal {
    return items.fold(0.0, (sum, item) => sum + item.wholesaleSubtotal);
  }

  String get formattedWholesaleTotal => _formatPrice(wholesaleTotal);

  static String _formatPrice(double price) {
    return '${price.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} د.ع';
  }

  // إنشاء سلة فارغة
  factory Cart.empty() {
    return Cart(
      items: [],
      subtotal: 0,
      tax: 0,
      shipping: 0,
      discount: 0,
      total: 0,
    );
  }

  // نسخ السلة مع تعديلات
  Cart copyWith({
    List<CartItem>? items,
    double? subtotal,
    double? tax,
    double? shipping,
    double? discount,
    double? total,
    String? couponCode,
    DateTime? updatedAt,
  }) {
    return Cart(
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      shipping: shipping ?? this.shipping,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      couponCode: couponCode ?? this.couponCode,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Cart(items: ${items.length}, total: $formattedTotal)';
  }
}

class CartItem {
  final int id;
  final int productId;
  final Product product;
  final int quantity;
  final double unitPrice;
  final double wholesalePrice;
  final double subtotal;
  final String? variation;
  final Map<String, dynamic>? attributes;
  final DateTime? addedAt;

  CartItem({
    required this.id,
    required this.productId,
    required this.product,
    required this.quantity,
    required this.unitPrice,
    required this.wholesalePrice,
    required this.subtotal,
    this.variation,
    this.attributes,
    this.addedAt,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      id: json['id'] ?? 0,
      productId: json['product_id'] ?? 0,
      product: Product.fromJson(json['product'] ?? {}),
      quantity: json['quantity'] ?? 1,
      unitPrice: (json['unit_price'] ?? 0).toDouble(),
      wholesalePrice: (json['wholesale_price'] ?? 0).toDouble(),
      subtotal: (json['subtotal'] ?? 0).toDouble(),
      variation: json['variation'],
      attributes: json['attributes'],
      addedAt: json['added_at'] != null 
          ? DateTime.tryParse(json['added_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'product': product.toJson(),
      'quantity': quantity,
      'unit_price': unitPrice,
      'wholesale_price': wholesalePrice,
      'subtotal': subtotal,
      'variation': variation,
      'attributes': attributes,
      'added_at': addedAt?.toIso8601String(),
    };
  }

  // دوال مساعدة
  bool get hasWholesalePrice => wholesalePrice > 0 && wholesalePrice < unitPrice;
  
  double get wholesaleSubtotal => wholesalePrice * quantity;
  double get savings => hasWholesalePrice ? (unitPrice - wholesalePrice) * quantity : 0;
  
  String get formattedUnitPrice => _formatPrice(unitPrice);
  String get formattedWholesalePrice => _formatPrice(wholesalePrice);
  String get formattedSubtotal => _formatPrice(subtotal);
  String get formattedWholesaleSubtotal => _formatPrice(wholesaleSubtotal);
  String get formattedSavings => _formatPrice(savings);

  static String _formatPrice(double price) {
    return '${price.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} د.ع';
  }

  // نسخ العنصر مع تعديلات
  CartItem copyWith({
    int? id,
    int? productId,
    Product? product,
    int? quantity,
    double? unitPrice,
    double? wholesalePrice,
    double? subtotal,
    String? variation,
    Map<String, dynamic>? attributes,
    DateTime? addedAt,
  }) {
    return CartItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      wholesalePrice: wholesalePrice ?? this.wholesalePrice,
      subtotal: subtotal ?? this.subtotal,
      variation: variation ?? this.variation,
      attributes: attributes ?? this.attributes,
      addedAt: addedAt ?? this.addedAt,
    );
  }

  @override
  String toString() {
    return 'CartItem(id: $id, product: ${product.name}, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// نموذج كوبون الخصم
class Coupon {
  final String code;
  final String type; // 'percentage' or 'fixed'
  final double value;
  final double? minimumAmount;
  final double? maximumDiscount;
  final DateTime? expiresAt;
  final bool isValid;
  final String? description;

  Coupon({
    required this.code,
    required this.type,
    required this.value,
    this.minimumAmount,
    this.maximumDiscount,
    this.expiresAt,
    this.isValid = true,
    this.description,
  });

  factory Coupon.fromJson(Map<String, dynamic> json) {
    return Coupon(
      code: json['code'] ?? '',
      type: json['type'] ?? 'percentage',
      value: (json['value'] ?? 0).toDouble(),
      minimumAmount: json['minimum_amount']?.toDouble(),
      maximumDiscount: json['maximum_discount']?.toDouble(),
      expiresAt: json['expires_at'] != null 
          ? DateTime.tryParse(json['expires_at'])
          : null,
      isValid: json['is_valid'] ?? true,
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'type': type,
      'value': value,
      'minimum_amount': minimumAmount,
      'maximum_discount': maximumDiscount,
      'expires_at': expiresAt?.toIso8601String(),
      'is_valid': isValid,
      'description': description,
    };
  }

  // حساب قيمة الخصم
  double calculateDiscount(double subtotal) {
    if (!isValid || subtotal < (minimumAmount ?? 0)) {
      return 0;
    }

    double discount;
    if (type == 'percentage') {
      discount = subtotal * (value / 100);
    } else {
      discount = value;
    }

    if (maximumDiscount != null && discount > maximumDiscount!) {
      discount = maximumDiscount!;
    }

    return discount;
  }

  bool get isPercentage => type == 'percentage';
  bool get isFixed => type == 'fixed';
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);

  String get formattedValue {
    if (isPercentage) {
      return '${value.toInt()}%';
    } else {
      return '${value.toStringAsFixed(0)} د.ع';
    }
  }

  @override
  String toString() {
    return 'Coupon(code: $code, value: $formattedValue)';
  }
}
