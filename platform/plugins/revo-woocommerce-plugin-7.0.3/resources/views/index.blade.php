@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-device-mobile me-2"></i>
                        RevoWOO - إدارة تطبيق Flutter
                    </h4>
                    <div class="card-header-action">
                        <span class="badge bg-success">v7.0.3</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-success" role="alert">
                        <i class="ti ti-check-circle me-2"></i>
                        <strong>مرحباً بك!</strong> إضافة RevoWOO مثبتة ومفعلة بنجاح. يمكنك الآن إدارة تطبيق Flutter الخاص بك من هنا.
                    </div>

                    <div class="row">
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="ti ti-settings text-primary" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="card-title">إعدادات التطبيق</h5>
                                    <p class="card-text text-muted">قم بتكوين إعدادات التطبيق الأساسية مثل الشعار والعنوان ومعلومات الاتصال</p>
                                    <a href="{{ route('revo-woocommerce.settings') }}" class="btn btn-primary">
                                        <i class="ti ti-settings me-1"></i>
                                        إدارة الإعدادات
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="ti ti-presentation text-success" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="card-title">صفحات التعريف</h5>
                                    <p class="card-text text-muted">إدارة صفحات التعريف التي تظهر للمستخدمين الجدد عند فتح التطبيق لأول مرة</p>
                                    <a href="{{ route('revo-woocommerce.intro-pages.index') }}" class="btn btn-success">
                                        <i class="ti ti-presentation me-1"></i>
                                        إدارة صفحات التعريف
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="ti ti-photo text-warning" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="card-title">السلايدر الرئيسي</h5>
                                    <p class="card-text text-muted">إدارة الصور والبانرات التي تظهر في الصفحة الرئيسية للتطبيق</p>
                                    <a href="{{ route('revo-woocommerce.home-slider.index') }}" class="btn btn-warning">
                                        <i class="ti ti-photo me-1"></i>
                                        إدارة السلايدر
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="ti ti-category text-info" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="card-title">إدارة الفئات</h5>
                                    <p class="card-text text-muted">تخصيص ترتيب وعرض فئات المنتجات في التطبيق</p>
                                    <a href="{{ route('revo-woocommerce.categories.index') }}" class="btn btn-info">
                                        <i class="ti ti-category me-1"></i>
                                        إدارة الفئات
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="ti ti-ad text-purple" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="card-title">البانر المصغر</h5>
                                    <p class="card-text text-muted">إدارة البانرات الترويجية الصغيرة في التطبيق</p>
                                    <a href="{{ route('revo-woocommerce.mini-banner.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-ad me-1"></i>
                                        إدارة البانر المصغر
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="ti ti-bolt text-danger" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="card-title">التخفيضات السريعة</h5>
                                    <p class="card-text text-muted">إدارة عروض التخفيضات السريعة والمحدودة الوقت</p>
                                    <a href="{{ route('revo-woocommerce.flash-sale.index') }}" class="btn btn-danger">
                                        <i class="ti ti-bolt me-1"></i>
                                        إدارة التخفيضات
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="ti ti-bell text-warning" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="card-title">الإشعارات</h5>
                                    <p class="card-text text-muted">إرسال إشعارات push للمستخدمين عبر Firebase</p>
                                    <a href="{{ route('revo-woocommerce.notifications.index') }}" class="btn btn-warning">
                                        <i class="ti ti-bell me-1"></i>
                                        إدارة الإشعارات
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="ti ti-api text-dark" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="card-title">API Documentation</h5>
                                    <p class="card-text text-muted">عرض وثائق API للمطورين واختبار الـ endpoints</p>
                                    <a href="/api/revo-admin/v1/settings" target="_blank" class="btn btn-dark">
                                        <i class="ti ti-api me-1"></i>
                                        اختبار API
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Row -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-gradient-primary text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="ti ti-chart-bar me-2"></i>
                                        إحصائيات الإضافة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <div class="p-3">
                                                <i class="ti ti-check-circle text-success" style="font-size: 2rem;"></i>
                                                <h4 class="mt-2 mb-1">مفعلة</h4>
                                                <p class="text-muted mb-0">حالة الإضافة</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="p-3">
                                                <i class="ti ti-version text-info" style="font-size: 2rem;"></i>
                                                <h4 class="mt-2 mb-1">v7.0.3</h4>
                                                <p class="text-muted mb-0">الإصدار الحالي</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="p-3">
                                                <i class="ti ti-server text-warning" style="font-size: 2rem;"></i>
                                                <h4 class="mt-2 mb-1">يعمل</h4>
                                                <p class="text-muted mb-0">حالة API</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="p-3">
                                                <i class="ti ti-brand-firebase text-danger" style="font-size: 2rem;"></i>
                                                <h4 class="mt-2 mb-1">جاهز</h4>
                                                <p class="text-muted mb-0">Firebase</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="ti ti-link me-2"></i>
                                        روابط سريعة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>🔗 API Endpoints:</h6>
                                            <ul class="list-unstyled">
                                                <li><a href="/api/revo-admin/v1/settings" target="_blank" class="text-decoration-none">📱 إعدادات التطبيق</a></li>
                                                <li><a href="/api/revo-admin/v1/intro-pages" target="_blank" class="text-decoration-none">📋 صفحات التعريف</a></li>
                                                <li><a href="/api/revo-admin/v1/home/<USER>" target="_blank" class="text-decoration-none">🖼️ السلايدر الرئيسي</a></li>
                                                <li><a href="/api/revo-admin/v1/home/<USER>" target="_blank" class="text-decoration-none">📂 الفئات</a></li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>⚙️ إعدادات سريعة:</h6>
                                            <ul class="list-unstyled">
                                                <li><a href="{{ route('revo-woocommerce.settings') }}" class="text-decoration-none">🔧 الإعدادات العامة</a></li>
                                                <li><a href="#" class="text-decoration-none">🔥 Firebase Configuration</a></li>
                                                <li><a href="#" class="text-decoration-none">📊 تقارير الاستخدام</a></li>
                                                <li><a href="#" class="text-decoration-none">🔄 تحديث الإضافة</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    $(document).ready(function() {
        // Add some interactive features
        $('.card').hover(
            function() {
                $(this).addClass('shadow-lg').removeClass('shadow-sm');
            },
            function() {
                $(this).addClass('shadow-sm').removeClass('shadow-lg');
            }
        );

        // Show success message
        @if(session('success'))
            Botble.showSuccess('{{ session('success') }}');
        @endif
    });
</script>
@endpush
