import '../models/api_response.dart';
import '../models/user.dart';
import 'api_service.dart';
import '../utils/storage_helper.dart';

class AuthServiceNew {
  static final AuthServiceNew _instance = AuthServiceNew._internal();
  factory AuthServiceNew() => _instance;
  AuthServiceNew._internal();

  final ApiService _apiService = ApiService();

  // النماذج مستوردة من ملفات منفصلة

  // === حالة المصادقة ===
  User? _currentUser;
  String? _currentToken;
  bool _isLoggedIn = false;

  // Getters
  User? get currentUser => _currentUser;
  String? get currentToken => _currentToken;
  bool get isLoggedIn => _isLoggedIn && _currentToken != null;
  bool get isWholesaleCustomer => _currentUser?.isWholesaleCustomer ?? false;

  // === تهيئة الخدمة ===

  // تهيئة حالة المصادقة من التخزين المحلي
  Future<void> initialize() async {
    try {
      final token = await StorageHelper.getToken();
      final userData = await StorageHelper.getUser();

      if (token != null && userData != null) {
        _currentToken = token;
        _currentUser = User.fromJson(userData);
        _isLoggedIn = true;
      }
    } catch (e) {
      // في حالة وجود خطأ، نقوم بتسجيل الخروج
      await logout();
    }
  }

  // === تسجيل الدخول ===

  // تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<ApiResponse<LoginResponse>> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      final response = await _apiService.login(
        email: email,
        password: password,
        rememberMe: rememberMe,
      );

      if (response.isSuccess && response.data != null) {
        final loginResponse = LoginResponse.fromJson(response.data!);
        
        // حفظ بيانات المصادقة
        await _saveAuthData(loginResponse);
        
        return ApiResponse.success(loginResponse);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تسجيل الدخول');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تسجيل الدخول: ${e.toString()}');
    }
  }

  // === التسجيل ===

  // تسجيل حساب جديد
  Future<ApiResponse<LoginResponse>> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    String? phone,
    String accountType = 'customer',
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final response = await _apiService.register(
        name: name,
        email: email,
        password: password,
        passwordConfirmation: passwordConfirmation,
        phone: phone,
        accountType: accountType,
        additionalData: additionalData,
      );

      if (response.isSuccess && response.data != null) {
        final loginResponse = LoginResponse.fromJson(response.data!);
        
        // حفظ بيانات المصادقة
        await _saveAuthData(loginResponse);
        
        return ApiResponse.success(loginResponse);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في إنشاء الحساب');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في إنشاء الحساب: ${e.toString()}');
    }
  }

  // === تسجيل الخروج ===

  // تسجيل الخروج
  Future<ApiResponse<bool>> logout() async {
    try {
      // محاولة تسجيل الخروج من الخادم
      if (_currentToken != null) {
        await _apiService.logout();
      }
    } catch (e) {
      // تجاهل أخطاء الخادم في تسجيل الخروج
    }

    // مسح البيانات المحلية
    await _clearAuthData();
    
    return ApiResponse.success(true);
  }

  // === OTP ===

  // إرسال رمز OTP
  Future<ApiResponse<Map<String, dynamic>>> sendOtp({
    required String phone,
    String type = 'verification',
  }) async {
    try {
      final response = await _apiService.sendOtp(
        phone: phone,
        type: type,
      );

      return response;
    } catch (e) {
      return ApiResponse.error('خطأ في إرسال رمز التحقق: ${e.toString()}');
    }
  }

  // التحقق من رمز OTP
  Future<ApiResponse<Map<String, dynamic>>> verifyOtp({
    required String phone,
    required String code,
    String type = 'verification',
  }) async {
    try {
      final response = await _apiService.verifyOtp(
        phone: phone,
        code: code,
        type: type,
      );

      if (response.isSuccess) {
        // تحديث حالة التحقق من الهاتف
        if (_currentUser != null) {
          _currentUser = User.fromJson({
            ..._currentUser!.toJson(),
            'phone_verified': true,
          });
          await StorageHelper.saveUser(_currentUser!.toJson());
        }
      }

      return response;
    } catch (e) {
      return ApiResponse.error('خطأ في التحقق من الرمز: ${e.toString()}');
    }
  }

  // === الملف الشخصي ===

  // الحصول على الملف الشخصي
  Future<ApiResponse<User>> getProfile() async {
    try {
      final response = await _apiService.getProfile();

      if (response.isSuccess && response.data != null) {
        final user = User.fromJson(response.data!);
        
        // تحديث المستخدم الحالي
        _currentUser = user;
        await StorageHelper.saveUser(user.toJson());
        
        return ApiResponse.success(user);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل الملف الشخصي');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل الملف الشخصي: ${e.toString()}');
    }
  }

  // تحديث الملف الشخصي
  Future<ApiResponse<User>> updateProfile(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.updateProfile(data);

      if (response.isSuccess && response.data != null) {
        final user = User.fromJson(response.data!);
        
        // تحديث المستخدم الحالي
        _currentUser = user;
        await StorageHelper.saveUser(user.toJson());
        
        return ApiResponse.success(user);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحديث الملف الشخصي');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحديث الملف الشخصي: ${e.toString()}');
    }
  }

  // === دوال مساعدة خاصة ===

  // حفظ بيانات المصادقة
  Future<void> _saveAuthData(LoginResponse loginResponse) async {
    _currentUser = loginResponse.user;
    _currentToken = loginResponse.token;
    _isLoggedIn = true;

    await StorageHelper.saveToken(loginResponse.token);
    await StorageHelper.saveUser(loginResponse.user.toJson());
  }

  // مسح بيانات المصادقة
  Future<void> _clearAuthData() async {
    _currentUser = null;
    _currentToken = null;
    _isLoggedIn = false;

    await StorageHelper.removeToken();
    await StorageHelper.removeUser();
  }

  // === التحقق من صحة البيانات ===

  // التحقق من صحة البريد الإلكتروني
  bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  // التحقق من قوة كلمة المرور
  bool isValidPassword(String password) {
    return password.length >= 8;
  }

  // التحقق من صحة رقم الهاتف
  bool isValidPhone(String phone) {
    return RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(phone);
  }
}
