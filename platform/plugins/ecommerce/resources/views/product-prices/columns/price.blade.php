<div style="min-width: 120px;">
    @if ($product->variations_count > 0 && ! $product->is_variation)
        <span class="text-muted">&mdash;</span>
    @else
        <x-core::form.text-input
            :label="false"
            value="{{ $product->{$type} ?: null }}"
            data-bb-toggle="product-bulk-change"
            data-url="{{ route('ecommerce.product-prices.update', ['product' => $product->id]) }}"
            data-column="{{ $type }}"
            data-id="product-price-{{ $type }}-{{ $product->id }}"
            step="any"
            class="input-mask-number"
            :group-flat="true"
            :name="null"
        >
            <x-slot:prepend>
                <span class="input-group-text">{{ get_application_currency()->symbol }}</span>
            </x-slot:prepend>
        </x-core::form.text-input>

        @if ($type == 'price' && auth()->check() && auth()->user()->hasPermission('ecommerce.product-prices.index') && $product->wholesale_price > 0)
            <div class="mt-1 small text-info">
                <strong>{{ trans('plugins/ecommerce::products.form.wholesale_price') }}:</strong>
                {{ format_price($product->wholesale_price) }}
            </div>
        @endif

        @if ($type == 'wholesale_price' && auth()->check() && auth()->user()->hasPermission('ecommerce.product-prices.index'))
            <div class="mt-1 small text-info">
                <strong>{{ trans('plugins/ecommerce::products.form.price') }}:</strong>
                {{ format_price($product->price) }}
            </div>
        @endif
    @endif
</div>
