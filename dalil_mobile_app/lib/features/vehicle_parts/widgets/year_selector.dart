import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:easy_localization/easy_localization.dart';
import '../providers/vehicle_parts_provider.dart';
import '../models/vehicle_category_model.dart';

class YearSelector extends StatelessWidget {
  const YearSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<VehiclePartsProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'vehicle_parts_finder.year'.tr(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(
                  color: provider.selectedModel == null 
                      ? Colors.grey[300]! 
                      : Theme.of(context).primaryColor.withOpacity(0.3),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: _buildContent(context, provider),
            ),
          ],
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, VehiclePartsProvider provider) {
    if (provider.selectedModel == null) {
      return _buildDisabledState(context);
    }

    if (provider.isLoading && provider.years.isEmpty) {
      return _buildLoadingState(context);
    }

    if (provider.years.isEmpty) {
      return _buildEmptyState(context);
    }

    return _buildDropdown(context, provider);
  }

  Widget _buildDisabledState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          Icon(
            Icons.block,
            color: Colors.grey[400],
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            'اختر الموديل أولاً',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 12),
          Text(
            'تحميل السنوات...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.grey[600],
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            'لا توجد سنوات متاحة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdown(BuildContext context, VehiclePartsProvider provider) {
    return DropdownButtonHideUnderline(
      child: DropdownButton<VehicleCategoryModel>(
        value: provider.selectedYear,
        hint: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Icon(
                Icons.calendar_today,
                color: Colors.grey[600],
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'vehicle_parts_finder.select_year'.tr(),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        isExpanded: true,
        icon: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: Icon(
            Icons.keyboard_arrow_down,
            color: Colors.grey[600],
          ),
        ),
        items: provider.years.map((year) {
          return DropdownMenuItem<VehicleCategoryModel>(
            value: year,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: _getYearColor(year.name).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.calendar_today,
                      color: _getYearColor(year.name),
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      year.name,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  // إضافة تسمية للسنوات الحديثة
                  if (_isRecentYear(year.name))
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'جديد',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.green,
                          fontWeight: FontWeight.w600,
                          fontSize: 10,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        }).toList(),
        onChanged: (year) {
          if (year != null) {
            provider.selectYear(year);
          }
        },
        dropdownColor: Colors.white,
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        menuMaxHeight: 300,
      ),
    );
  }

  Color _getYearColor(String yearName) {
    final currentYear = DateTime.now().year;
    final year = int.tryParse(yearName) ?? 0;
    
    if (year >= currentYear - 2) {
      return Colors.green; // السنوات الحديثة
    } else if (year >= currentYear - 5) {
      return Colors.blue; // السنوات المتوسطة
    } else if (year >= currentYear - 10) {
      return Colors.orange; // السنوات القديمة نسبياً
    } else {
      return Colors.grey; // السنوات القديمة
    }
  }

  bool _isRecentYear(String yearName) {
    final currentYear = DateTime.now().year;
    final year = int.tryParse(yearName) ?? 0;
    return year >= currentYear - 2;
  }
}
