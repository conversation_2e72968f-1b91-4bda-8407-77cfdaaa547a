class BannerModel {
  final int id;
  final String title;
  final String? description;
  final String imageUrl;
  final String? linkUrl;
  final String type;
  final bool isActive;
  final int order;
  final DateTime? startDate;
  final DateTime? endDate;
  final DateTime? createdAt;

  BannerModel({
    required this.id,
    required this.title,
    this.description,
    required this.imageUrl,
    this.linkUrl,
    required this.type,
    this.isActive = true,
    this.order = 0,
    this.startDate,
    this.endDate,
    this.createdAt,
  });

  factory BannerModel.fromJson(Map<String, dynamic> json) {
    return BannerModel(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String,
      linkUrl: json['link_url'] as String?,
      type: json['type'] as String? ?? 'promotional',
      isActive: json['is_active'] as bool? ?? true,
      order: json['order'] as int? ?? 0,
      startDate: json['start_date'] != null 
          ? DateTime.parse(json['start_date'] as String)
          : null,
      endDate: json['end_date'] != null 
          ? DateTime.parse(json['end_date'] as String)
          : null,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image_url': imageUrl,
      'link_url': linkUrl,
      'type': type,
      'is_active': isActive,
      'order': order,
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // التحقق من صحة البانر (ضمن الفترة الزمنية)
  bool get isValid {
    final now = DateTime.now();
    
    if (startDate != null && now.isBefore(startDate!)) {
      return false;
    }
    
    if (endDate != null && now.isAfter(endDate!)) {
      return false;
    }
    
    return isActive;
  }

  // نوع البانر بالعربية
  String get typeArabic {
    switch (type) {
      case 'promotional':
        return 'ترويجي';
      case 'announcement':
        return 'إعلان';
      case 'offer':
        return 'عرض خاص';
      case 'news':
        return 'أخبار';
      default:
        return 'عام';
    }
  }

  BannerModel copyWith({
    int? id,
    String? title,
    String? description,
    String? imageUrl,
    String? linkUrl,
    String? type,
    bool? isActive,
    int? order,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? createdAt,
  }) {
    return BannerModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      linkUrl: linkUrl ?? this.linkUrl,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      order: order ?? this.order,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'BannerModel(id: $id, title: $title, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BannerModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
