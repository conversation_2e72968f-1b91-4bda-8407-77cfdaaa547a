{{-- <PERSON>WA Manifest للثيم الثاني المطور --}}
<link rel="manifest" href="{{ url('/pwa-copy/manifest.json') }}">
<meta name="theme-color" content="#2c3e50">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<meta name="apple-mobile-web-app-title" content="دليلك أوتو المطور">
<meta name="mobile-web-app-capable" content="yes">
<meta name="application-name" content="دليلك أوتو المطور">

{{-- PWA Icons --}}
<link rel="apple-touch-icon" href="{{ url('/pwa-copy/icon-192x192.png') }}">
<link rel="icon" type="image/png" sizes="192x192" href="{{ url('/pwa-copy/icon-192x192.png') }}">
<link rel="icon" type="image/png" sizes="512x512" href="{{ url('/pwa-copy/icon-512x512.png') }}">

{{-- PWA Enhanced Styles and Scripts --}}
<link rel="stylesheet" href="{{ url('/pwa-copy/pwa-styles.css') }}">
<script src="{{ url('/pwa-copy/pwa-enhanced.js') }}" defer></script>

<style>
    :root {
        --primary-color: {{ $primaryColor = theme_option('primary_color', '#0989ff') }};
        --primary-color-rgb: {{ implode(',', BaseHelper::hexToRgb($primaryColor)) }};
        --tp-theme-secondary: {{ theme_option('secondary_color', '#821f40') }};
        --footer-background-color: {{ theme_option('footer_background_color', '#fff') }};
        --footer-text-color: {{ theme_option('footer_text_color', '#010f1c') }};
        --footer-title-color: {{ theme_option('footer_title_color', '#010f1c') }};
        --footer-link-color: {{ theme_option('footer_link_color', '#010f1c') }};
        --footer-link-hover-color: {{ theme_option('footer_link_hover_color', '#0989ff') }};
        --footer-border-color: {{ theme_option('footer_border_color', '#e5e6e8') }};
    }
</style>
