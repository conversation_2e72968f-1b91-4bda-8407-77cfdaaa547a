import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../config/app_config.dart';
import '../services/product_service.dart';
import '../services/category_service.dart';
import 'product_detail_screen.dart';

class CategoryScreen extends StatefulWidget {
  final Map<String, dynamic> category;

  const CategoryScreen({
    super.key,
    required this.category,
  });

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen> {
  List<Map<String, dynamic>> _products = [];
  List<Map<String, dynamic>> _filteredProducts = [];
  List<Map<String, dynamic>> _subcategories = [];
  bool _isLoading = true;
  
  // فلاتر
  String _sortBy = 'default';
  String? _selectedBrand;
  double _minPrice = 0;
  double _maxPrice = 1000000;
  List<String> _availableBrands = [];
  
  // عرض
  bool _isGridView = true;
  int _itemsPerPage = 12;

  @override
  void initState() {
    super.initState();
    _loadCategoryData();
  }

  Future<void> _loadCategoryData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // جلب منتجات الفئة
      final products = await ProductService.getProductsByCategory(
        widget.category['slug'] ?? widget.category['id'].toString(),
      );
      
      // جلب الفئات الفرعية
      final subcategories = await CategoryService.getSubcategories(
        widget.category['id'].toString(),
      );

      // استخراج العلامات التجارية المتاحة
      final brands = <String>{};
      double maxPrice = 0;
      
      for (var product in products) {
        if (product['brand'] != null) {
          brands.add(product['brand']);
        }
        final price = (product['price'] ?? 0).toDouble();
        if (price > maxPrice) maxPrice = price;
      }

      setState(() {
        _products = products;
        _filteredProducts = products;
        _subcategories = subcategories;
        _availableBrands = brands.toList()..sort();
        _maxPrice = maxPrice > 0 ? maxPrice : 1000000;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل بيانات الفئة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredProducts = _products.where((product) {
        // فلتر العلامة التجارية
        if (_selectedBrand != null && product['brand'] != _selectedBrand) {
          return false;
        }
        
        // فلتر السعر
        final price = (product['price'] ?? 0).toDouble();
        if (price < _minPrice || price > _maxPrice) {
          return false;
        }
        
        return true;
      }).toList();

      // ترتيب النتائج
      switch (_sortBy) {
        case 'price_low':
          _filteredProducts.sort((a, b) => 
            (a['price'] ?? 0).compareTo(b['price'] ?? 0));
          break;
        case 'price_high':
          _filteredProducts.sort((a, b) => 
            (b['price'] ?? 0).compareTo(a['price'] ?? 0));
          break;
        case 'name':
          _filteredProducts.sort((a, b) => 
            (a['name'] ?? '').compareTo(b['name'] ?? ''));
          break;
        case 'newest':
          _filteredProducts.sort((a, b) => 
            (b['created_at'] ?? '').compareTo(a['created_at'] ?? ''));
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: _buildAppBar(),
        body: _isLoading ? _buildLoadingWidget() : _buildContent(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        widget.category['name'] ?? 'الفئة',
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: Color(AppConfig.primaryColorValue),
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // تبديل العرض
        IconButton(
          icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
          onPressed: () {
            setState(() {
              _isGridView = !_isGridView;
            });
          },
        ),
        
        // فلاتر
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFiltersBottomSheet,
        ),
      ],
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        // شريط المعلومات
        _buildInfoBar(),
        
        // الفئات الفرعية
        if (_subcategories.isNotEmpty) _buildSubcategories(),
        
        // المنتجات
        Expanded(
          child: _filteredProducts.isEmpty 
            ? _buildEmptyState()
            : _buildProductsList(),
        ),
      ],
    );
  }

  Widget _buildInfoBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${_filteredProducts.length} منتج',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          // ترتيب سريع
          DropdownButton<String>(
            value: _sortBy,
            onChanged: (value) {
              setState(() {
                _sortBy = value ?? 'default';
              });
              _applyFilters();
            },
            items: const [
              DropdownMenuItem(value: 'default', child: Text('الترتيب الافتراضي')),
              DropdownMenuItem(value: 'price_low', child: Text('السعر: من الأقل للأعلى')),
              DropdownMenuItem(value: 'price_high', child: Text('السعر: من الأعلى للأقل')),
              DropdownMenuItem(value: 'name', child: Text('الاسم: أ-ي')),
              DropdownMenuItem(value: 'newest', child: Text('الأحدث')),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSubcategories() {
    return Container(
      height: 60,
      color: Colors.white,
      margin: const EdgeInsets.only(top: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _subcategories.length,
        itemBuilder: (context, index) {
          final subcategory = _subcategories[index];
          return Container(
            margin: const EdgeInsets.only(left: 12),
            child: FilterChip(
              label: Text(subcategory['name'] ?? ''),
              onSelected: (selected) {
                if (selected) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CategoryScreen(category: subcategory),
                    ),
                  );
                }
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildProductsList() {
    if (_isGridView) {
      return GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: _filteredProducts.length,
        itemBuilder: (context, index) {
          return _buildProductCard(_filteredProducts[index]);
        },
      );
    } else {
      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredProducts.length,
        itemBuilder: (context, index) {
          return _buildProductListItem(_filteredProducts[index]);
        },
      );
    }
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProductDetailScreen(product: product),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المنتج
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                  child: ProductService.getImageUrl(product['image']).startsWith('assets/')
                      ? Image.asset(
                          ProductService.getImageUrl(product['image']),
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildPlaceholderImage();
                          },
                        )
                      : CachedNetworkImage(
                          imageUrl: ProductService.getImageUrl(product['image']),
                          fit: BoxFit.cover,
                          placeholder: (context, url) => _buildLoadingImage(),
                          errorWidget: (context, url, error) => _buildPlaceholderImage(),
                        ),
                ),
              ),
            ),
            
            // معلومات المنتج
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم المنتج
                    Text(
                      product['name'] ?? 'منتج غير محدد',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const Spacer(),
                    
                    // السعر
                    Text(
                      '${ProductService.formatPrice(product['price'] ?? 0)} د.ع',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(AppConfig.primaryColorValue),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductListItem(Map<String, dynamic> product) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProductDetailScreen(product: product),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // صورة المنتج
            Container(
              width: 100,
              height: 100,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.horizontal(right: Radius.circular(12)),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.horizontal(right: Radius.circular(12)),
                child: ProductService.getImageUrl(product['image']).startsWith('assets/')
                    ? Image.asset(
                        ProductService.getImageUrl(product['image']),
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildPlaceholderImage();
                        },
                      )
                    : CachedNetworkImage(
                        imageUrl: ProductService.getImageUrl(product['image']),
                        fit: BoxFit.cover,
                        placeholder: (context, url) => _buildLoadingImage(),
                        errorWidget: (context, url, error) => _buildPlaceholderImage(),
                      ),
              ),
            ),

            // معلومات المنتج
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم المنتج
                    Text(
                      product['name'] ?? 'منتج غير محدد',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    // العلامة التجارية
                    if (product['brand'] != null)
                      Text(
                        product['brand'],
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),

                    const SizedBox(height: 8),

                    // السعر
                    Text(
                      '${ProductService.formatPrice(product['price'] ?? 0)} د.ع',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(AppConfig.primaryColorValue),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد منتجات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير الفلاتر أو البحث عن شيء آخر',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingImage() {
    return Container(
      color: Colors.grey[100],
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      color: Colors.grey[200],
      child: const Icon(
        Icons.car_repair,
        size: 40,
        color: Colors.grey,
      ),
    );
  }

  void _showFiltersBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // مقبض السحب
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),

                // العنوان
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'الفلاتر',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        setState(() {
                          _selectedBrand = null;
                          _minPrice = 0;
                          _maxPrice = 1000000;
                          _sortBy = 'default';
                        });
                        _applyFilters();
                        Navigator.pop(context);
                      },
                      child: const Text('إعادة تعيين'),
                    ),
                  ],
                ),

                const Divider(),

                // محتوى الفلاتر
                Expanded(
                  child: ListView(
                    controller: scrollController,
                    children: [
                      // فلتر العلامة التجارية
                      if (_availableBrands.isNotEmpty) ...[
                        const Text(
                          'العلامة التجارية',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: _availableBrands.map((brand) {
                            return FilterChip(
                              label: Text(brand),
                              selected: _selectedBrand == brand,
                              onSelected: (selected) {
                                setState(() {
                                  _selectedBrand = selected ? brand : null;
                                });
                              },
                            );
                          }).toList(),
                        ),
                        const SizedBox(height: 24),
                      ],

                      // فلتر السعر
                      const Text(
                        'نطاق السعر',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 12),
                      RangeSlider(
                        values: RangeValues(_minPrice, _maxPrice),
                        min: 0,
                        max: _maxPrice > 0 ? _maxPrice : 1000000,
                        divisions: 20,
                        labels: RangeLabels(
                          ProductService.formatPrice(_minPrice),
                          ProductService.formatPrice(_maxPrice),
                        ),
                        onChanged: (values) {
                          setState(() {
                            _minPrice = values.start;
                            _maxPrice = values.end;
                          });
                        },
                      ),

                      const SizedBox(height: 24),

                      // أزرار التطبيق
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('إلغاء'),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                _applyFilters();
                                Navigator.pop(context);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Color(AppConfig.primaryColorValue),
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('تطبيق'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
