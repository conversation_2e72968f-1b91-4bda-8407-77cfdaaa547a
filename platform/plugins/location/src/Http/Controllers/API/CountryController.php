<?php

namespace Botble\Location\Http\Controllers\API;

use Botble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Location\Http\Resources\CountryResource;
use Botble\Location\Http\Resources\StateResource;
use Botble\Location\Http\Resources\CityResource;
use Botble\Location\Models\Country;
use Illuminate\Http\Request;

class CountryController extends BaseController
{
    /**
     * قائمة الدول
     *
     * @group Location
     */
    public function index(Request $request)
    {
        $query = Country::query()
            ->where('status', BaseStatusEnum::PUBLISHED);

        // البحث في اسم الدولة أو الكود
        if ($request->has('search') && $request->input('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('code', 'LIKE', "%{$search}%")
                  ->orWhere('nationality', 'LIKE', "%{$search}%");
            });
        }

        // فلترة حسب الكود
        if ($request->has('code') && $request->input('code')) {
            $query->where('code', $request->input('code'));
        }

        // ترتيب النتائج
        $sortBy = $request->input('sort_by', 'order');
        $sortOrder = $request->input('sort_order', 'asc');
        
        if (in_array($sortBy, ['order', 'name', 'code'])) {
            $query->orderBy($sortBy, $sortOrder === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy('order', 'asc')->orderBy('name', 'asc');
        }

        $perPage = $request->integer('per_page', 50);
        $perPage = min($perPage, 100); // حد أقصى 100 عنصر

        $countries = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData(CountryResource::collection($countries))
            ->toApiResponse();
    }

    /**
     * عرض دولة محددة
     *
     * @group Location
     */
    public function show($id)
    {
        $country = Country::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->withCount(['states', 'cities'])
            ->findOrFail($id);

        return $this
            ->httpResponse()
            ->setData(new CountryResource($country))
            ->toApiResponse();
    }

    /**
     * الحصول على ولايات/محافظات دولة محددة
     *
     * @group Location
     */
    public function getStates($id, Request $request)
    {
        $country = Country::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->findOrFail($id);

        $query = $country->states()
            ->where('status', BaseStatusEnum::PUBLISHED);

        // البحث في اسم الولاية
        if ($request->has('search') && $request->input('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('abbreviation', 'LIKE', "%{$search}%");
            });
        }

        // ترتيب النتائج
        $sortBy = $request->input('sort_by', 'order');
        $sortOrder = $request->input('sort_order', 'asc');
        
        if (in_array($sortBy, ['order', 'name'])) {
            $query->orderBy($sortBy, $sortOrder === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy('order', 'asc')->orderBy('name', 'asc');
        }

        $perPage = $request->integer('per_page', 50);
        $perPage = min($perPage, 100);

        $states = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData([
                'country' => new CountryResource($country),
                'states' => StateResource::collection($states)
            ])
            ->toApiResponse();
    }

    /**
     * الحصول على مدن دولة محددة
     *
     * @group Location
     */
    public function getCities($id, Request $request)
    {
        $country = Country::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->findOrFail($id);

        $query = $country->cities()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with('state');

        // البحث في اسم المدينة
        if ($request->has('search') && $request->input('search')) {
            $search = $request->input('search');
            $query->where('name', 'LIKE', "%{$search}%");
        }

        // فلترة حسب الولاية
        if ($request->has('state_id') && $request->input('state_id')) {
            $query->where('state_id', $request->input('state_id'));
        }

        // ترتيب النتائج
        $sortBy = $request->input('sort_by', 'order');
        $sortOrder = $request->input('sort_order', 'asc');
        
        if (in_array($sortBy, ['order', 'name'])) {
            $query->orderBy($sortBy, $sortOrder === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy('order', 'asc')->orderBy('name', 'asc');
        }

        $perPage = $request->integer('per_page', 50);
        $perPage = min($perPage, 100);

        $cities = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData([
                'country' => new CountryResource($country),
                'cities' => CityResource::collection($cities)
            ])
            ->toApiResponse();
    }
}
