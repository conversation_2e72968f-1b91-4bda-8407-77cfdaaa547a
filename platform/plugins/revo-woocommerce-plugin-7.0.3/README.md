# RevoWOO - Flutter Woocommerce Manager for Botble CMS

![Version](https://badgen.net/badge/Version/7.0.3/green)
![Platform](https://badgen.net/badge/Platform/Botble%20CMS/blue)

RevoWOO - Flutter Woocommerce Full App Manager converted for Botble CMS

## Installation

1. **Copy the plugin to plugins directory:**
   ```bash
   # The plugin should be placed in platform/plugins/revo-woocommerce-plugin-7.0.3/
   ```

2. **Install dependencies:**
   ```bash
   composer install
   ```

3. **Activate the plugin:**
   - Go to Admin Panel → Plugins
   - Find "RevoWOO - Flutter Woocommerce Manager"
   - Click "Activate"

4. **Run migrations:**
   ```bash
   php artisan migrate
   ```

## Requirements

- Botble CMS 7.3.0 or higher
- Ecommerce plugin must be activated
- PHP 8.1 or higher
- Laravel 10.0 or higher

## Features

### Admin Panel Features
- **Dashboard Settings**: Configure app logo, title, contact information
- **Intro Pages Management**: Create and manage app introduction screens
- **Home Slider**: Manage main banner images on home screen
- **Categories Management**: Custom categories display order
- **Mini Banner**: Small promotional banners management
- **Flash Sale**: Configure flash sale products and timing
- **Push Notifications**: Send notifications to app users via Firebase

### API Features

#### Authentication (`/api/revo-admin/v1/auth/`)
- User registration
- Username & Password Login
- Facebook Login
- Google Login
- Apple Login
- Firebase OTP Login
- Password reset

#### User Profile (`/api/revo-admin/v1/`)
- Get user profile
- Update user profile
- Get user points
- Firebase token management

#### Home Content (`/api/revo-admin/v1/home/<USER>
- Main slider images
- Custom categories
- Mini banners
- Flash sale products
- Additional products

#### Products (`/api/revo-admin/v1/products/`)
- Hit products
- Recent viewed products
- Wishlist management (add/remove/list)
- Product variations
- Wholesale pricing

#### Categories (`/api/revo-admin/v1/categories/`)
- Popular categories
- All categories list

#### Orders (`/api/revo-admin/v1/orders/`)
- Order listing
- Order details
- Order reviews
- Checkout with points

#### Notifications (`/api/revo-admin/v1/notifications/`)
- List notifications
- Mark as read
- Push notification management

## Configuration

### Firebase Setup
1. Go to Admin Panel → RevoWOO → Settings
2. Enter your Firebase Server Key
3. Enable Firebase notifications

### App Settings
- **App Title**: Set your mobile app title
- **App Logo**: Upload your app logo
- **Contact Info**: Add contact information
- **About URL**: Link to about page
- **Customer Support URL**: Link to support page

## Usage

### Accessing Admin Panel
After activation, you'll find "RevoWOO" in the admin menu with the following sections:
- **Settings**: Configure app settings and Firebase
- **Intro Pages**: Manage app introduction screens
- **Home Slider**: Manage banner images
- **Categories**: Organize category display
- **Mini Banner**: Manage promotional banners
- **Flash Sale**: Configure flash sales
- **Notifications**: Send push notifications

### API Usage
The plugin provides REST API endpoints at `/api/revo-admin/v1/` for mobile app integration.

## Troubleshooting

### Common Issues
1. **Plugin not appearing**: Ensure Ecommerce plugin is activated first
2. **API not working**: Check if API plugin is enabled in Botble
3. **Permissions error**: Verify user has proper permissions

### Support
For support and updates, visit: https://revoapps.net

## Changelog

### Version 7.0.3
- Converted from WordPress to Botble CMS
- Added Laravel-based architecture
- Integrated with Botble's plugin system
- Added proper permissions and menu structure