class VehicleCategoryModel {
  final int id;
  final String name;
  final String slug;
  final String? description;
  final String? imageUrl;
  final int? parentId;
  final bool hasChildren;
  final int childrenCount;
  final int order;
  final DateTime? createdAt;

  VehicleCategoryModel({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.imageUrl,
    this.parentId,
    this.hasChildren = false,
    this.childrenCount = 0,
    this.order = 0,
    this.createdAt,
  });

  factory VehicleCategoryModel.fromJson(Map<String, dynamic> json) {
    return VehicleCategoryModel(
      id: json['id'] as int,
      name: json['name'] as String,
      slug: json['slug'] as String,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      parentId: json['parent_id'] as int?,
      hasChildren: json['has_children'] as bool? ?? false,
      childrenCount: json['children_count'] as int? ?? 0,
      order: json['order'] as int? ?? 0,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'image_url': imageUrl,
      'parent_id': parentId,
      'has_children': hasChildren,
      'children_count': childrenCount,
      'order': order,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // التحقق من كون الفئة جذرية
  bool get isRoot => parentId == null;

  // الحصول على الصورة للعرض
  String? get displayImage => imageUrl;

  VehicleCategoryModel copyWith({
    int? id,
    String? name,
    String? slug,
    String? description,
    String? imageUrl,
    int? parentId,
    bool? hasChildren,
    int? childrenCount,
    int? order,
    DateTime? createdAt,
  }) {
    return VehicleCategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      slug: slug ?? this.slug,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      parentId: parentId ?? this.parentId,
      hasChildren: hasChildren ?? this.hasChildren,
      childrenCount: childrenCount ?? this.childrenCount,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'VehicleCategoryModel(id: $id, name: $name, slug: $slug)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VehicleCategoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
