import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/notification_provider.dart';
import '../providers/theme_provider.dart';
import '../services/notification_service.dart';
import '../widgets/loading_widget.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationProvider>().initialize();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<NotificationProvider, ThemeProvider>(
      builder: (context, notificationProvider, themeProvider, child) {
        final isDark = themeProvider.isDarkMode;
        
        return Scaffold(
          backgroundColor: isDark ? Colors.grey[900] : Colors.grey[50],
          appBar: AppBar(
            title: const Text('الإشعارات'),
            backgroundColor: isDark ? Colors.grey[850] : Colors.white,
            foregroundColor: isDark ? Colors.white : Colors.black,
            actions: [
              if (notificationProvider.notifications.isNotEmpty) ...[
                IconButton(
                  icon: const Icon(Icons.done_all),
                  onPressed: () => _markAllAsRead(notificationProvider),
                  tooltip: 'تمييز الكل كمقروء',
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMenuAction(value, notificationProvider),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'clear_all',
                      child: Row(
                        children: [
                          Icon(Icons.clear_all, color: Colors.red),
                          SizedBox(width: 8),
                          Text('مسح جميع الإشعارات'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'settings',
                      child: Row(
                        children: [
                          Icon(Icons.settings),
                          SizedBox(width: 8),
                          Text('إعدادات الإشعارات'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'test',
                      child: Row(
                        children: [
                          Icon(Icons.bug_report),
                          SizedBox(width: 8),
                          Text('إنشاء إشعارات تجريبية'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
          body: notificationProvider.isLoading
              ? const Center(child: LoadingWidget())
              : _buildBody(notificationProvider, isDark),
        );
      },
    );
  }

  Widget _buildBody(NotificationProvider provider, bool isDark) {
    if (provider.notifications.isEmpty) {
      return _buildEmptyState(isDark);
    }

    return Column(
      children: [
        // شريط البحث
        _buildSearchBar(isDark),
        
        // إحصائيات سريعة
        if (_searchQuery.isEmpty) _buildQuickStats(provider, isDark),
        
        // قائمة الإشعارات
        Expanded(child: _buildNotificationsList(provider, isDark)),
      ],
    );
  }

  Widget _buildEmptyState(bool isDark) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 120,
            color: isDark ? Colors.grey[600] : Colors.grey[400],
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.grey[300] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'ستظهر إشعاراتك هنا عند وصولها',
            style: TextStyle(
              fontSize: 16,
              color: isDark ? Colors.grey[400] : Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(bool isDark) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في الإشعارات...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildQuickStats(NotificationProvider provider, bool isDark) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            'المجموع',
            '${provider.notifications.length}',
            Icons.notifications,
            isDark,
          ),
          _buildStatItem(
            'غير مقروءة',
            '${provider.unreadCount}',
            Icons.mark_email_unread,
            isDark,
            color: Colors.red,
          ),
          _buildStatItem(
            'اليوم',
            '${_getTodayNotificationsCount(provider)}',
            Icons.today,
            isDark,
            color: Colors.green,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, bool isDark, {Color? color}) {
    return Column(
      children: [
        Icon(
          icon,
          color: color ?? (isDark ? Colors.grey[400] : Colors.grey[600]),
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color ?? (isDark ? Colors.white : Colors.black),
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isDark ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationsList(NotificationProvider provider, bool isDark) {
    final notifications = _searchQuery.isEmpty
        ? provider.notifications
        : provider.searchNotifications(_searchQuery);

    if (notifications.isEmpty) {
      return Center(
        child: Text(
          'لا توجد نتائج للبحث',
          style: TextStyle(
            color: isDark ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
      );
    }

    if (_searchQuery.isEmpty) {
      // تجميع حسب التاريخ
      final groupedNotifications = provider.groupNotificationsByDate();
      return ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: groupedNotifications.length,
        itemBuilder: (context, index) {
          final dateKey = groupedNotifications.keys.elementAt(index);
          final dayNotifications = groupedNotifications[dateKey]!;
          
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Text(
                  dateKey,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.grey[300] : Colors.grey[700],
                  ),
                ),
              ),
              ...dayNotifications.map((notification) => 
                _buildNotificationItem(notification, provider, isDark)),
              const SizedBox(height: 16),
            ],
          );
        },
      );
    } else {
      // قائمة بسيطة للبحث
      return ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: notifications.length,
        itemBuilder: (context, index) {
          return _buildNotificationItem(notifications[index], provider, isDark);
        },
      );
    }
  }

  Widget _buildNotificationItem(NotificationModel notification, NotificationProvider provider, bool isDark) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: notification.isRead 
            ? null 
            : Border.all(color: Theme.of(context).primaryColor.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: _getNotificationIcon(notification.type, notification.isRead),
        title: Text(
          notification.title,
          style: TextStyle(
            fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
            color: isDark ? Colors.white : Colors.black,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              notification.body,
              style: TextStyle(
                color: isDark ? Colors.grey[300] : Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(notification.createdAt),
              style: TextStyle(
                fontSize: 12,
                color: isDark ? Colors.grey[400] : Colors.grey[500],
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleNotificationAction(value, notification, provider),
          itemBuilder: (context) => [
            if (!notification.isRead)
              const PopupMenuItem(
                value: 'mark_read',
                child: Row(
                  children: [
                    Icon(Icons.mark_email_read),
                    SizedBox(width: 8),
                    Text('تمييز كمقروء'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف'),
                ],
              ),
            ),
          ],
        ),
        onTap: () {
          if (!notification.isRead) {
            provider.markAsRead(notification.id);
          }
          _handleNotificationTap(notification);
        },
      ),
    );
  }

  Widget _getNotificationIcon(String type, bool isRead) {
    IconData iconData;
    Color color;

    switch (type) {
      case NotificationService.typeNewProduct:
        iconData = Icons.new_releases;
        color = Colors.green;
        break;
      case NotificationService.typeOffer:
        iconData = Icons.local_offer;
        color = Colors.orange;
        break;
      case NotificationService.typeOrderUpdate:
        iconData = Icons.shopping_bag;
        color = Colors.blue;
        break;
      default:
        iconData = Icons.notifications;
        color = Colors.grey;
    }

    return CircleAvatar(
      backgroundColor: color.withOpacity(isRead ? 0.3 : 0.8),
      child: Icon(
        iconData,
        color: Colors.white,
        size: 20,
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return '${dateTime.day}/${dateTime.month} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  int _getTodayNotificationsCount(NotificationProvider provider) {
    final today = DateTime.now();
    return provider.notifications.where((n) {
      final notificationDate = n.createdAt;
      return notificationDate.year == today.year &&
             notificationDate.month == today.month &&
             notificationDate.day == today.day;
    }).length;
  }

  void _markAllAsRead(NotificationProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تمييز الكل كمقروء'),
        content: const Text('هل تريد تمييز جميع الإشعارات كمقروءة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              provider.markAllAsRead();
              Navigator.pop(context);
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action, NotificationProvider provider) {
    switch (action) {
      case 'clear_all':
        _showClearAllDialog(provider);
        break;
      case 'settings':
        _showNotificationSettings(provider);
        break;
      case 'test':
        provider.createSampleNotifications();
        break;
    }
  }

  void _showClearAllDialog(NotificationProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع الإشعارات'),
        content: const Text('هل أنت متأكد من مسح جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              provider.clearAllNotifications();
              Navigator.pop(context);
            },
            child: const Text('مسح', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings(NotificationProvider provider) {
    // يمكن إنشاء شاشة إعدادات منفصلة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات الإشعارات قريباً...')),
    );
  }

  void _handleNotificationAction(String action, NotificationModel notification, NotificationProvider provider) {
    switch (action) {
      case 'mark_read':
        provider.markAsRead(notification.id);
        break;
      case 'delete':
        provider.deleteNotification(notification.id);
        break;
    }
  }

  void _handleNotificationTap(NotificationModel notification) {
    // التعامل مع النقر على الإشعار حسب النوع
    if (notification.data != null) {
      switch (notification.type) {
        case NotificationService.typeNewProduct:
          // الانتقال لصفحة المنتج
          break;
        case NotificationService.typeOffer:
          // الانتقال لصفحة العروض
          break;
        case NotificationService.typeOrderUpdate:
          // الانتقال لصفحة الطلب
          break;
      }
    }
  }
}
