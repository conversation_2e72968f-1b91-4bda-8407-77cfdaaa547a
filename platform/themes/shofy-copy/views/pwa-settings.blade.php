@extends(Theme::getThemeNamespace('layouts.default'))

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient text-white text-center py-4" style="background: linear-gradient(135deg, #2c3e50, #34495e);">
                    <h2 class="mb-0">
                        <i class="fas fa-mobile-alt me-2"></i>
                        إعدادات تطبيق دليلك أوتو المطور
                    </h2>
                    <p class="mb-0 mt-2 opacity-75">إدارة إعدادات التطبيق التقدمي (PWA)</p>
                </div>
                
                <div class="card-body p-5">
                    <!-- PWA Status -->
                    <div class="alert alert-info border-0 rounded-3 mb-4">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle fa-2x text-info"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="alert-heading mb-1">حالة التطبيق التقدمي</h5>
                                <p class="mb-0">التطبيق التقدمي (PWA) مفعل ويعمل بشكل طبيعي</p>
                            </div>
                        </div>
                    </div>

                    <!-- PWA Features -->
                    <div class="row g-4 mb-5">
                        <div class="col-md-6">
                            <div class="feature-card h-100 p-4 border rounded-3 text-center">
                                <div class="feature-icon mb-3">
                                    <i class="fas fa-download fa-3x text-primary"></i>
                                </div>
                                <h5>قابل للتثبيت</h5>
                                <p class="text-muted mb-0">يمكن تثبيت التطبيق على الجهاز للوصول السريع</p>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="feature-card h-100 p-4 border rounded-3 text-center">
                                <div class="feature-icon mb-3">
                                    <i class="fas fa-wifi fa-3x text-success"></i>
                                </div>
                                <h5>يعمل بدون إنترنت</h5>
                                <p class="text-muted mb-0">إمكانية التصفح حتى بدون اتصال بالإنترنت</p>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="feature-card h-100 p-4 border rounded-3 text-center">
                                <div class="feature-icon mb-3">
                                    <i class="fas fa-sync-alt fa-3x text-warning"></i>
                                </div>
                                <h5>تحديثات تلقائية</h5>
                                <p class="text-muted mb-0">يتم تحديث التطبيق تلقائياً في الخلفية</p>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="feature-card h-100 p-4 border rounded-3 text-center">
                                <div class="feature-icon mb-3">
                                    <i class="fas fa-bell fa-3x text-danger"></i>
                                </div>
                                <h5>إشعارات فورية</h5>
                                <p class="text-muted mb-0">تلقي إشعارات حول العروض والمنتجات الجديدة</p>
                            </div>
                        </div>
                    </div>

                    <!-- PWA Actions -->
                    <div class="row g-3">
                        <div class="col-md-6">
                            <button class="btn btn-primary btn-lg w-100 rounded-3" onclick="testPWAInstall()">
                                <i class="fas fa-download me-2"></i>
                                اختبار التثبيت
                            </button>
                        </div>
                        
                        <div class="col-md-6">
                            <button class="btn btn-success btn-lg w-100 rounded-3" onclick="testOfflineMode()">
                                <i class="fas fa-wifi-slash me-2"></i>
                                اختبار الوضع بدون إنترنت
                            </button>
                        </div>
                        
                        <div class="col-md-6">
                            <button class="btn btn-warning btn-lg w-100 rounded-3" onclick="clearPWACache()">
                                <i class="fas fa-trash me-2"></i>
                                مسح ذاكرة التخزين المؤقت
                            </button>
                        </div>
                        
                        <div class="col-md-6">
                            <button class="btn btn-info btn-lg w-100 rounded-3" onclick="checkForUpdates()">
                                <i class="fas fa-sync me-2"></i>
                                فحص التحديثات
                            </button>
                        </div>
                    </div>

                    <!-- PWA Info -->
                    <div class="mt-5 p-4 bg-light rounded-3">
                        <h5 class="mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات التطبيق
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>اسم التطبيق:</strong> دليلك أوتو المطور<br>
                                <strong>الإصدار:</strong> 2.0<br>
                                <strong>نوع العرض:</strong> مستقل
                            </div>
                            <div class="col-md-6">
                                <strong>لون الثيم:</strong> #2c3e50<br>
                                <strong>لون الخلفية:</strong> #f8f9fa<br>
                                <strong>الاتجاه:</strong> عمودي
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testPWAInstall() {
    if (window.pwaApp && window.pwaApp.deferredPrompt) {
        window.pwaApp.installApp();
    } else {
        alert('التطبيق مثبت بالفعل أو غير متاح للتثبيت في الوقت الحالي');
    }
}

function testOfflineMode() {
    // Simulate offline mode
    if (navigator.onLine) {
        alert('لاختبار الوضع بدون إنترنت، قم بإيقاف الاتصال بالإنترنت وأعد تحميل الصفحة');
    } else {
        alert('أنت حالياً في الوضع بدون إنترنت!');
    }
}

function clearPWACache() {
    if ('caches' in window) {
        caches.keys().then(function(names) {
            for (let name of names) {
                caches.delete(name);
            }
            alert('تم مسح ذاكرة التخزين المؤقت بنجاح');
        });
    }
}

function checkForUpdates() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistration().then(function(registration) {
            if (registration) {
                registration.update();
                alert('تم فحص التحديثات');
            }
        });
    }
}
</script>

<style>
.feature-card {
    transition: all 0.3s ease;
    border: 2px solid #e9ecef !important;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    border-color: #007bff !important;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}
</style>
@endsection
