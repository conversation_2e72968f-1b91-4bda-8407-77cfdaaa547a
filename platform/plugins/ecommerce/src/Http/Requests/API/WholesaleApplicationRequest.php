<?php

namespace Botble\Ecommerce\Http\Requests\API;

use Botble\Support\Http\Requests\Request;

class WholesaleApplicationRequest extends Request
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'company' => 'required|string|max:255',
            'tax_id' => 'nullable|string|max:50',
            'business_license' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'business_type' => 'nullable|string|max:100',
            'years_in_business' => 'nullable|integer|min:0|max:100',
            'expected_monthly_volume' => 'nullable|numeric|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'company.required' => 'اسم الشركة مطلوب.',
            'company.string' => 'اسم الشركة يجب أن يكون نص.',
            'company.max' => 'اسم الشركة يجب ألا يتجاوز 255 حرف.',
            'tax_id.string' => 'الرقم الضريبي يجب أن يكون نص.',
            'tax_id.max' => 'الرقم الضريبي يجب ألا يتجاوز 50 حرف.',
            'business_license.string' => 'رقم الترخيص التجاري يجب أن يكون نص.',
            'business_license.max' => 'رقم الترخيص التجاري يجب ألا يتجاوز 100 حرف.',
            'notes.string' => 'الملاحظات يجب أن تكون نص.',
            'notes.max' => 'الملاحظات يجب ألا تتجاوز 1000 حرف.',
            'phone.string' => 'رقم الهاتف يجب أن يكون نص.',
            'phone.max' => 'رقم الهاتف يجب ألا يتجاوز 20 حرف.',
            'address.string' => 'العنوان يجب أن يكون نص.',
            'address.max' => 'العنوان يجب ألا يتجاوز 500 حرف.',
            'business_type.string' => 'نوع النشاط التجاري يجب أن يكون نص.',
            'business_type.max' => 'نوع النشاط التجاري يجب ألا يتجاوز 100 حرف.',
            'years_in_business.integer' => 'سنوات الخبرة يجب أن تكون رقم صحيح.',
            'years_in_business.min' => 'سنوات الخبرة يجب أن تكون 0 أو أكثر.',
            'years_in_business.max' => 'سنوات الخبرة يجب ألا تتجاوز 100 سنة.',
            'expected_monthly_volume.numeric' => 'الحجم الشهري المتوقع يجب أن يكون رقم.',
            'expected_monthly_volume.min' => 'الحجم الشهري المتوقع يجب أن يكون 0 أو أكثر.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'company' => 'اسم الشركة',
            'tax_id' => 'الرقم الضريبي',
            'business_license' => 'رقم الترخيص التجاري',
            'notes' => 'الملاحظات',
            'phone' => 'رقم الهاتف',
            'address' => 'العنوان',
            'business_type' => 'نوع النشاط التجاري',
            'years_in_business' => 'سنوات الخبرة',
            'expected_monthly_volume' => 'الحجم الشهري المتوقع',
        ];
    }
}
