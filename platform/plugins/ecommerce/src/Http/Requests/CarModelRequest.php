<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Requests;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class CarModelRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'make_id' => 'required|exists:car_makes,id',
            'slug' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status' => Rule::in(BaseStatusEnum::values()),
        ];
    }
}
