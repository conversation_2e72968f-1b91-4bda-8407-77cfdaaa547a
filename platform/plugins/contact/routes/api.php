<?php

use Botble\Contact\Http\Controllers\API\ContactController;
use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => ['api', 'throttle:api'],
    'prefix' => 'api/v1/contact',
    'namespace' => 'Botble\Contact\Http\Controllers\API',
], function (): void {
    // مسارات عامة (بدون مصادقة)
    Route::get('/subjects', [ContactController::class, 'getSubjects']);
    Route::get('/custom-fields', [ContactController::class, 'getCustomFields']);

    // مسارات تحتاج rate limiting إضافي
    Route::group(['middleware' => 'throttle:10,1'], function (): void {
        // إرسال رسالة اتصال جديدة (10 طلبات في الدقيقة)
        Route::post('/', [ContactController::class, 'store']);

        // فحص حالة البريد الإلكتروني (10 طلبات في الدقيقة)
        Route::post('/check-email', [ContactController::class, 'checkEmail']);
    });
});
