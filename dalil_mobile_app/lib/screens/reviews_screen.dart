import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/reviews_provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/custom_button.dart';
import '../widgets/loading_widget.dart';

class ReviewsScreen extends StatefulWidget {
  final int productId;
  final String productName;

  const ReviewsScreen({
    super.key,
    required this.productId,
    required this.productName,
  });

  @override
  State<ReviewsScreen> createState() => _ReviewsScreenState();
}

class _ReviewsScreenState extends State<ReviewsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _sortBy = 'newest';
  double? _filterRating;
  bool _verifiedOnly = false;
  bool _withImagesOnly = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ReviewsProvider>().loadReviews();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ReviewsProvider, ThemeProvider>(
      builder: (context, reviewsProvider, themeProvider, child) {
        final isDark = themeProvider.isDarkMode;
        
        return Scaffold(
          backgroundColor: isDark ? Colors.grey[900] : Colors.grey[50],
          appBar: AppBar(
            title: Text('تقييمات ${widget.productName}'),
            backgroundColor: isDark ? Colors.grey[850] : Colors.white,
            foregroundColor: isDark ? Colors.white : Colors.black,
            bottom: TabBar(
              controller: _tabController,
              labelColor: isDark ? Colors.white : Colors.black,
              unselectedLabelColor: isDark ? Colors.grey[400] : Colors.grey[600],
              indicatorColor: Theme.of(context).primaryColor,
              tabs: const [
                Tab(text: 'التقييمات', icon: Icon(Icons.star)),
                Tab(text: 'إضافة تقييم', icon: Icon(Icons.add_comment)),
              ],
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: () => _showFilterDialog(reviewsProvider),
              ),
              PopupMenuButton<String>(
                onSelected: (value) => _handleMenuAction(value, reviewsProvider),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'sort',
                    child: Row(
                      children: [
                        Icon(Icons.sort),
                        SizedBox(width: 8),
                        Text('ترتيب'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'sample',
                    child: Row(
                      children: [
                        Icon(Icons.bug_report),
                        SizedBox(width: 8),
                        Text('تقييمات تجريبية'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              _buildReviewsTab(reviewsProvider, isDark),
              _buildAddReviewTab(reviewsProvider, isDark),
            ],
          ),
        );
      },
    );
  }

  Widget _buildReviewsTab(ReviewsProvider provider, bool isDark) {
    if (provider.isLoading) {
      return const Center(child: LoadingWidget());
    }

    final reviews = _getFilteredAndSortedReviews(provider);
    
    if (reviews.isEmpty) {
      return _buildEmptyReviews(isDark);
    }

    return Column(
      children: [
        // إحصائيات التقييمات
        _buildReviewStats(provider, isDark),
        
        // قائمة التقييمات
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: reviews.length,
            itemBuilder: (context, index) {
              return _buildReviewCard(reviews[index], provider, isDark);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyReviews(bool isDark) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.rate_review_outlined,
            size: 120,
            color: isDark ? Colors.grey[600] : Colors.grey[400],
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد تقييمات بعد',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.grey[300] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'كن أول من يقيم هذا المنتج',
            style: TextStyle(
              fontSize: 16,
              color: isDark ? Colors.grey[400] : Colors.grey[500],
            ),
          ),
          const SizedBox(height: 32),
          CustomButton(
            text: 'إضافة تقييم',
            onPressed: () => _tabController.animateTo(1),
            backgroundColor: Theme.of(context).primaryColor,
            icon: Icons.add_comment,
          ),
        ],
      ),
    );
  }

  Widget _buildReviewStats(ReviewsProvider provider, bool isDark) {
    final stats = provider.getReviewStats(widget.productId);
    final distribution = stats['ratingDistribution'] as Map<int, int>;
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // التقييم العام
          Row(
            children: [
              Column(
                children: [
                  Text(
                    stats['averageRating'].toStringAsFixed(1),
                    style: TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      color: isDark ? Colors.white : Colors.black,
                    ),
                  ),
                  _buildStarRating(stats['averageRating']),
                  Text(
                    '${stats['totalReviews']} تقييم',
                    style: TextStyle(
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(width: 32),
              
              // توزيع التقييمات
              Expanded(
                child: Column(
                  children: [
                    for (int i = 5; i >= 1; i--)
                      _buildRatingBar(i, distribution[i] ?? 0, stats['totalReviews'], isDark),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // إحصائيات إضافية
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                'مشتريات موثقة',
                '${stats['verifiedPurchases']}',
                Icons.verified,
                isDark,
              ),
              _buildStatItem(
                'مع صور',
                '${_getReviewsWithImages(provider)}',
                Icons.image,
                isDark,
              ),
              _buildStatItem(
                'هذا الشهر',
                '${_getRecentReviews(provider)}',
                Icons.calendar_today,
                isDark,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStarRating(double rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < rating ? Icons.star : Icons.star_border,
          color: Colors.amber,
          size: 20,
        );
      }),
    );
  }

  Widget _buildRatingBar(int stars, int count, int total, bool isDark) {
    final percentage = total > 0 ? count / total : 0.0;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text('$stars'),
          const SizedBox(width: 4),
          Icon(Icons.star, color: Colors.amber, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: LinearProgressIndicator(
              value: percentage,
              backgroundColor: isDark ? Colors.grey[700] : Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.amber),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '$count',
            style: TextStyle(
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, bool isDark) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isDark ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildReviewCard(ReviewModel review, ReviewsProvider provider, bool isDark) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المراجع
          Row(
            children: [
              CircleAvatar(
                backgroundColor: Theme.of(context).primaryColor,
                child: Text(
                  review.userName.isNotEmpty ? review.userName[0].toUpperCase() : 'U',
                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          review.userName,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isDark ? Colors.white : Colors.black,
                          ),
                        ),
                        if (review.isVerifiedPurchase) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.green),
                            ),
                            child: const Text(
                              'مشترٍ موثق',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    Text(
                      _formatDate(review.createdAt),
                      style: TextStyle(
                        fontSize: 12,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              _buildStarRating(review.rating),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // التعليق
          Text(
            review.comment,
            style: TextStyle(
              fontSize: 14,
              height: 1.5,
              color: isDark ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          
          // الصور (إذا وجدت)
          if (review.images.isNotEmpty) ...[
            const SizedBox(height: 12),
            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: review.images.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    width: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey[200],
                    ),
                    child: const Icon(Icons.image),
                  );
                },
              ),
            ),
          ],
          
          const SizedBox(height: 12),
          
          // أزرار الإجراء
          Row(
            children: [
              TextButton.icon(
                onPressed: () => provider.toggleHelpful(review.id, widget.productId),
                icon: Icon(
                  review.isHelpful ? Icons.thumb_up : Icons.thumb_up_outlined,
                  size: 16,
                  color: review.isHelpful ? Theme.of(context).primaryColor : null,
                ),
                label: Text('مفيد (${review.helpfulCount})'),
              ),
              const Spacer(),
              Text(
                '${review.rating}/5',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddReviewTab(ReviewsProvider provider, bool isDark) {
    return AddReviewForm(
      productId: widget.productId,
      productName: widget.productName,
      onReviewAdded: () {
        _tabController.animateTo(0);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة تقييمك بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
      },
    );
  }

  List<ReviewModel> _getFilteredAndSortedReviews(ReviewsProvider provider) {
    var reviews = provider.getFilteredReviews(
      widget.productId,
      minRating: _filterRating,
      verifiedOnly: _verifiedOnly,
      withImages: _withImagesOnly,
    );
    
    return provider.getSortedReviews(widget.productId, _sortBy)
        .where((review) => reviews.contains(review))
        .toList();
  }

  int _getReviewsWithImages(ReviewsProvider provider) {
    return provider.getProductReviews(widget.productId)
        .where((review) => review.images.isNotEmpty)
        .length;
  }

  int _getRecentReviews(ReviewsProvider provider) {
    final now = DateTime.now();
    final thisMonth = DateTime(now.year, now.month);
    
    return provider.getProductReviews(widget.productId)
        .where((review) => review.createdAt.isAfter(thisMonth))
        .length;
  }

  void _showFilterDialog(ReviewsProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة التقييمات'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // فلتر التقييم
                DropdownButtonFormField<double?>(
                  value: _filterRating,
                  decoration: const InputDecoration(labelText: 'التقييم الأدنى'),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('جميع التقييمات')),
                    for (int i = 1; i <= 5; i++)
                      DropdownMenuItem(
                        value: i.toDouble(),
                        child: Text('$i نجوم فأكثر'),
                      ),
                  ],
                  onChanged: (value) => setState(() => _filterRating = value),
                ),
                
                // فلتر المشتريات الموثقة
                CheckboxListTile(
                  title: const Text('مشتريات موثقة فقط'),
                  value: _verifiedOnly,
                  onChanged: (value) => setState(() => _verifiedOnly = value ?? false),
                ),
                
                // فلتر الصور
                CheckboxListTile(
                  title: const Text('مع صور فقط'),
                  value: _withImagesOnly,
                  onChanged: (value) => setState(() => _withImagesOnly = value ?? false),
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {});
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action, ReviewsProvider provider) {
    switch (action) {
      case 'sort':
        _showSortDialog();
        break;
      case 'sample':
        provider.createSampleReviews(widget.productId);
        break;
    }
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('ترتيب التقييمات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSortOption('الأحدث', 'newest'),
            _buildSortOption('الأقدم', 'oldest'),
            _buildSortOption('أعلى تقييم', 'highest_rating'),
            _buildSortOption('أقل تقييم', 'lowest_rating'),
            _buildSortOption('الأكثر فائدة', 'most_helpful'),
            _buildSortOption('المشتريات الموثقة أولاً', 'verified_first'),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOption(String label, String value) {
    return RadioListTile<String>(
      title: Text(label),
      value: value,
      groupValue: _sortBy,
      onChanged: (newValue) {
        setState(() {
          _sortBy = newValue ?? 'newest';
        });
        Navigator.pop(context);
      },
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 30) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

// نموذج إضافة تقييم
class AddReviewForm extends StatefulWidget {
  final int productId;
  final String productName;
  final VoidCallback onReviewAdded;

  const AddReviewForm({
    super.key,
    required this.productId,
    required this.productName,
    required this.onReviewAdded,
  });

  @override
  State<AddReviewForm> createState() => _AddReviewFormState();
}

class _AddReviewFormState extends State<AddReviewForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _commentController = TextEditingController();
  double _rating = 5.0;

  @override
  void dispose() {
    _nameController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ReviewsProvider, ThemeProvider>(
      builder: (context, reviewsProvider, themeProvider, child) {
        final isDark = themeProvider.isDarkMode;
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'شارك تجربتك مع ${widget.productName}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black,
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // اختيار التقييم
                Text(
                  'تقييمك للمنتج',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDark ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: List.generate(5, (index) {
                    return GestureDetector(
                      onTap: () => setState(() => _rating = index + 1.0),
                      child: Icon(
                        index < _rating ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                        size: 32,
                      ),
                    );
                  }),
                ),
                
                const SizedBox(height: 24),
                
                // اسم المراجع
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسمك',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال اسمك';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // التعليق
                TextFormField(
                  controller: _commentController,
                  decoration: const InputDecoration(
                    labelText: 'تعليقك على المنتج',
                    border: OutlineInputBorder(),
                    alignLabelWithHint: true,
                  ),
                  maxLines: 4,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى كتابة تعليق';
                    }
                    if (value.trim().length < 10) {
                      return 'التعليق قصير جداً (10 أحرف على الأقل)';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 32),
                
                // زر الإرسال
                SizedBox(
                  width: double.infinity,
                  child: CustomButton(
                    text: 'إضافة التقييم',
                    onPressed: reviewsProvider.isSubmitting ? null : _submitReview,
                    isLoading: reviewsProvider.isSubmitting,
                    backgroundColor: Theme.of(context).primaryColor,
                    icon: Icons.send,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _submitReview() async {
    if (!_formKey.currentState!.validate()) return;

    final success = await context.read<ReviewsProvider>().addReview(
      productId: widget.productId,
      userName: _nameController.text.trim(),
      rating: _rating,
      comment: _commentController.text.trim(),
      isVerifiedPurchase: true, // يمكن تحديد هذا بناءً على حالة المستخدم
    );

    if (success) {
      widget.onReviewAdded();
      _nameController.clear();
      _commentController.clear();
      setState(() {
        _rating = 5.0;
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('حدث خطأ أثناء إضافة التقييم'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
