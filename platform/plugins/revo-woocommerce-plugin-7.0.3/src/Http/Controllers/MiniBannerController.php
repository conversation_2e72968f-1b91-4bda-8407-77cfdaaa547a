<?php

namespace Botble\RevoWoocommerce\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;
use Illuminate\Contracts\View\View;

class MiniBannerController extends BaseController
{
    public function index(): View
    {
        $this->pageTitle('إدارة البانر المصغر');

        $banners = $this->getBanners();

        return view('plugins/revo-woocommerce-plugin-7.0.3::mini-banner.index', compact('banners'));
    }

    public function create(): View
    {
        $this->pageTitle('إضافة بانر مصغر جديد');

        return view('plugins/revo-woocommerce-plugin-7.0.3::mini-banner.create');
    }

    public function store(Request $request, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->validate([
            'title' => 'nullable|string|max:255',
            'image' => 'required|url',
            'link' => 'nullable|url',
            'position' => 'required|in:top,middle,bottom',
            'order' => 'nullable|integer|min:0',
        ], [
            'image.required' => 'رابط الصورة مطلوب',
            'image.url' => 'رابط الصورة يجب أن يكون رابط صحيح',
            'link.url' => 'الرابط يجب أن يكون رابط صحيح',
            'position.required' => 'موقع البانر مطلوب',
            'position.in' => 'موقع البانر يجب أن يكون أعلى، وسط، أو أسفل',
            'order.integer' => 'الترتيب يجب أن يكون رقم صحيح',
        ]);

        $this->storeBanner($request->all());

        return $response
            ->setMessage('تم إضافة البانر المصغر بنجاح!')
            ->setNextUrl(route('revo-woocommerce.mini-banner.index'));
    }

    public function edit($id): View
    {
        $this->pageTitle('تعديل البانر المصغر');

        $banner = $this->getBanner($id);

        return view('plugins/revo-woocommerce-plugin-7.0.3::mini-banner.edit', compact('banner'));
    }

    public function update(Request $request, $id, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->validate([
            'title' => 'nullable|string|max:255',
            'image' => 'required|url',
            'link' => 'nullable|url',
            'position' => 'required|in:top,middle,bottom',
            'order' => 'nullable|integer|min:0',
        ], [
            'image.required' => 'رابط الصورة مطلوب',
            'image.url' => 'رابط الصورة يجب أن يكون رابط صحيح',
            'link.url' => 'الرابط يجب أن يكون رابط صحيح',
            'position.required' => 'موقع البانر مطلوب',
            'position.in' => 'موقع البانر يجب أن يكون أعلى، وسط، أو أسفل',
            'order.integer' => 'الترتيب يجب أن يكون رقم صحيح',
        ]);

        $this->updateBanner($id, $request->all());

        return $response
            ->setMessage('تم تحديث البانر المصغر بنجاح!')
            ->setNextUrl(route('revo-woocommerce.mini-banner.index'));
    }

    public function destroy($id, BaseHttpResponse $response): BaseHttpResponse
    {
        $this->deleteBanner($id);

        return $response
            ->setMessage('تم حذف البانر المصغر بنجاح!');
    }

    protected function getBanners(): array
    {
        return [
            [
                'id' => 1,
                'title' => 'عرض خاص - خصم 50%',
                'image' => 'https://via.placeholder.com/300x150/007bff/ffffff?text=Special+Offer',
                'link' => 'https://example.com/special-offer',
                'position' => 'top',
                'order' => 1,
                'status' => true,
            ],
            [
                'id' => 2,
                'title' => 'شحن مجاني',
                'image' => 'https://via.placeholder.com/300x150/28a745/ffffff?text=Free+Shipping',
                'link' => 'https://example.com/free-shipping',
                'position' => 'middle',
                'order' => 2,
                'status' => true,
            ],
            [
                'id' => 3,
                'title' => 'منتجات جديدة',
                'image' => 'https://via.placeholder.com/300x150/ffc107/ffffff?text=New+Products',
                'link' => 'https://example.com/new-products',
                'position' => 'bottom',
                'order' => 3,
                'status' => false,
            ],
        ];
    }

    protected function getBanner($id): array
    {
        $banners = $this->getBanners();
        return collect($banners)->firstWhere('id', $id) ?? [];
    }

    protected function storeBanner(array $data): void
    {
        // Placeholder implementation - would store in database
    }

    protected function updateBanner($id, array $data): void
    {
        // Placeholder implementation - would update in database
    }

    protected function deleteBanner($id): void
    {
        // Placeholder implementation - would delete from database
    }
}
