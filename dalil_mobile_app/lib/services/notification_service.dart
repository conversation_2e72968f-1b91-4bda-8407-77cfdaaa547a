import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:math';

class NotificationModel {
  final String id;
  final String title;
  final String body;
  final String type;
  final Map<String, dynamic>? data;
  final DateTime createdAt;
  bool isRead;

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    this.data,
    required this.createdAt,
    this.isRead = false,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'body': body,
    'type': type,
    'data': data,
    'createdAt': createdAt.toIso8601String(),
    'isRead': isRead,
  };

  factory NotificationModel.fromJson(Map<String, dynamic> json) => NotificationModel(
    id: json['id'],
    title: json['title'],
    body: json['body'],
    type: json['type'],
    data: json['data'],
    createdAt: DateTime.parse(json['createdAt']),
    isRead: json['isRead'] ?? false,
  );
}

class NotificationService {
  static const String _notificationsKey = 'notifications';
  static const String _settingsKey = 'notification_settings';
  
  // أنواع الإشعارات
  static const String typeNewProduct = 'new_product';
  static const String typeOffer = 'offer';
  static const String typeOrderUpdate = 'order_update';
  static const String typeGeneral = 'general';

  // إعدادات الإشعارات
  Map<String, bool> _settings = {
    'newProducts': true,
    'offers': true,
    'orderUpdates': true,
    'general': true,
  };

  Map<String, bool> get settings => Map.unmodifiable(_settings);

  // تحميل الإعدادات
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        _settings = settingsMap.map((key, value) => MapEntry(key, value as bool));
      }
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    }
  }

  // حفظ الإعدادات
  Future<void> saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_settingsKey, json.encode(_settings));
    } catch (e) {
      debugPrint('Error saving notification settings: $e');
    }
  }

  // تحديث إعداد معين
  Future<void> updateSetting(String key, bool value) async {
    _settings[key] = value;
    await saveSettings();
  }

  // الحصول على جميع الإشعارات
  Future<List<NotificationModel>> getNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList(_notificationsKey) ?? [];
      
      return notificationsJson
          .map((json) => NotificationModel.fromJson(jsonDecode(json)))
          .toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    } catch (e) {
      debugPrint('Error loading notifications: $e');
      return [];
    }
  }

  // إضافة إشعار جديد
  Future<void> addNotification(NotificationModel notification) async {
    try {
      final notifications = await getNotifications();
      notifications.insert(0, notification);
      
      // الاحتفاظ بآخر 100 إشعار فقط
      if (notifications.length > 100) {
        notifications.removeRange(100, notifications.length);
      }
      
      await _saveNotifications(notifications);
      
      // عرض الإشعار إذا كان التطبيق مفتوح
      _showInAppNotification(notification);
    } catch (e) {
      debugPrint('Error adding notification: $e');
    }
  }

  // حفظ الإشعارات
  Future<void> _saveNotifications(List<NotificationModel> notifications) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = notifications
          .map((notification) => json.encode(notification.toJson()))
          .toList();
      
      await prefs.setStringList(_notificationsKey, notificationsJson);
    } catch (e) {
      debugPrint('Error saving notifications: $e');
    }
  }

  // تمييز إشعار كمقروء
  Future<void> markAsRead(String notificationId) async {
    try {
      final notifications = await getNotifications();
      final index = notifications.indexWhere((n) => n.id == notificationId);
      
      if (index >= 0) {
        notifications[index].isRead = true;
        await _saveNotifications(notifications);
      }
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    }
  }

  // تمييز جميع الإشعارات كمقروءة
  Future<void> markAllAsRead() async {
    try {
      final notifications = await getNotifications();
      for (var notification in notifications) {
        notification.isRead = true;
      }
      await _saveNotifications(notifications);
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
    }
  }

  // حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      final notifications = await getNotifications();
      notifications.removeWhere((n) => n.id == notificationId);
      await _saveNotifications(notifications);
    } catch (e) {
      debugPrint('Error deleting notification: $e');
    }
  }

  // مسح جميع الإشعارات
  Future<void> clearAllNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_notificationsKey);
    } catch (e) {
      debugPrint('Error clearing notifications: $e');
    }
  }

  // الحصول على عدد الإشعارات غير المقروءة
  Future<int> getUnreadCount() async {
    try {
      final notifications = await getNotifications();
      return notifications.where((n) => !n.isRead).length;
    } catch (e) {
      debugPrint('Error getting unread count: $e');
      return 0;
    }
  }

  // إنشاء إشعار منتج جديد
  Future<void> createNewProductNotification(String productName, String productId) async {
    if (!_settings['newProducts']!) return;
    
    final notification = NotificationModel(
      id: _generateId(),
      title: 'منتج جديد متاح!',
      body: 'تم إضافة $productName إلى المتجر',
      type: typeNewProduct,
      data: {'productId': productId},
      createdAt: DateTime.now(),
    );
    
    await addNotification(notification);
  }

  // إنشاء إشعار عرض خاص
  Future<void> createOfferNotification(String title, String description, {Map<String, dynamic>? data}) async {
    if (!_settings['offers']!) return;
    
    final notification = NotificationModel(
      id: _generateId(),
      title: title,
      body: description,
      type: typeOffer,
      data: data,
      createdAt: DateTime.now(),
    );
    
    await addNotification(notification);
  }

  // إنشاء إشعار تحديث الطلب
  Future<void> createOrderUpdateNotification(String orderId, String status) async {
    if (!_settings['orderUpdates']!) return;
    
    final notification = NotificationModel(
      id: _generateId(),
      title: 'تحديث الطلب',
      body: 'تم تحديث حالة طلبك #$orderId إلى: $status',
      type: typeOrderUpdate,
      data: {'orderId': orderId, 'status': status},
      createdAt: DateTime.now(),
    );
    
    await addNotification(notification);
  }

  // إنشاء إشعار عام
  Future<void> createGeneralNotification(String title, String body, {Map<String, dynamic>? data}) async {
    if (!_settings['general']!) return;
    
    final notification = NotificationModel(
      id: _generateId(),
      title: title,
      body: body,
      type: typeGeneral,
      data: data,
      createdAt: DateTime.now(),
    );
    
    await addNotification(notification);
  }

  // عرض إشعار داخل التطبيق
  void _showInAppNotification(NotificationModel notification) {
    // يمكن تطبيق منطق عرض الإشعار هنا
    // مثل استخدام SnackBar أو Dialog
  }

  // توليد معرف فريد
  String _generateId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }

  // محاكاة إشعارات تجريبية
  Future<void> createSampleNotifications() async {
    await createNewProductNotification('بروجكتر دعامية يسار', '123');
    await Future.delayed(const Duration(seconds: 1));
    
    await createOfferNotification(
      'خصم 20% على جميع المنتجات',
      'استفد من العرض المحدود لفترة محدودة',
      data: {'discountPercent': 20},
    );
    await Future.delayed(const Duration(seconds: 1));
    
    await createOrderUpdateNotification('ORD001', 'قيد التحضير');
    await Future.delayed(const Duration(seconds: 1));
    
    await createGeneralNotification(
      'مرحباً بك في دليل',
      'نشكرك لاستخدام تطبيقنا لقطع غيار السيارات',
    );
  }
}
