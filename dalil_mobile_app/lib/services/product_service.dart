import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';

class ProductService {



  // الحصول على المنتجات المميزة
  static Future<List<Map<String, dynamic>>> getFeaturedProducts({int limit = 10}) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}${AppConfig.productsEndpoint}?limit=$limit'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          List<Map<String, dynamic>> products = List<Map<String, dynamic>>.from(data['data']);

          // تحويل البيانات لتتوافق مع التطبيق
          return products.map((product) => _convertProductData(product)).toList();
        }
      }

      return _getDefaultProducts();
    } catch (e) {
      print('خطأ في جلب المنتجات المميزة: $e');
      return _getDefaultProducts();
    }
  }

  // تحويل بيانات المنتج من API إلى تنسيق التطبيق
  static Map<String, dynamic> _convertProductData(Map<String, dynamic> apiProduct) {
    return {
      'id': apiProduct['id'],
      'name': apiProduct['name'] ?? 'منتج غير محدد',
      'slug': apiProduct['slug'],
      'price': apiProduct['price'] ?? 0,
      'sale_price': apiProduct['price'] ?? 0,
      'original_price': apiProduct['original_price'],
      'image': apiProduct['image_url'] ?? (apiProduct['images']?.isNotEmpty == true ? apiProduct['images'][0] : null),
      'images': apiProduct['images'] ?? [],
      'discount_percentage': _calculateDiscountFromPrices(apiProduct['original_price'], apiProduct['price']),
      'in_stock': apiProduct['stock_status_label'] == 'متوفر' || !apiProduct['is_out_of_stock'],
      'stock_quantity': apiProduct['quantity'] ?? 0,
      'rating': apiProduct['reviews_avg'] ?? 4.5,
      'reviews_count': apiProduct['reviews_count'] ?? 0,
      'description': apiProduct['description'] ?? '',
      'short_description': apiProduct['content']?.replaceAll(RegExp(r'<[^>]*>'), '') ?? '',
      'sku': apiProduct['sku'],
      'brand': _extractBrandFromName(apiProduct['name'] ?? ''),
      'category': 'قطع غيار',
      'tags': _extractTagsFromName(apiProduct['name'] ?? ''),
      'is_featured': true,
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  // حساب نسبة الخصم من الأسعار
  static int? _calculateDiscountFromPrices(dynamic originalPrice, dynamic currentPrice) {
    if (originalPrice == null || currentPrice == null) return null;

    final original = originalPrice is String ? double.tryParse(originalPrice.toString()) ?? 0 : originalPrice.toDouble();
    final current = currentPrice is String ? double.tryParse(currentPrice.toString()) ?? 0 : currentPrice.toDouble();

    if (original <= current || original == 0) return null;

    return ((original - current) / original * 100).round();
  }

  // استخراج العلامة التجارية من اسم المنتج
  static String _extractBrandFromName(String name) {
    final brands = ['تويوتا', 'نيسان', 'كيا', 'هيونداي', 'مازدا', 'هوندا', 'فورد', 'شيفروليه', 'بي ام دبليو', 'مرسيدس'];
    for (String brand in brands) {
      if (name.contains(brand)) {
        return brand;
      }
    }
    return 'غير محدد';
  }

  // استخراج الكلمات المفتاحية من اسم المنتج
  static List<String> _extractTagsFromName(String name) {
    final commonTags = ['فلتر', 'زيت', 'فرامل', 'بطارية', 'إطار', 'محرك', 'كهربائي'];
    List<String> tags = [];

    for (String tag in commonTags) {
      if (name.contains(tag)) {
        tags.add(tag);
      }
    }

    return tags.isEmpty ? ['قطع غيار'] : tags;
  }

  // الحصول على جميع المنتجات
  static Future<List<Map<String, dynamic>>> getProducts({
    int page = 1,
    int limit = 20,
    String? categoryId,
    String? search,
  }) async {
    try {
      String url = '${AppConfig.apiBaseUrl}${AppConfig.productsEndpoint}?page=$page&limit=$limit';
      
      if (categoryId != null) {
        url += '&category_id=$categoryId';
      }
      
      if (search != null && search.isNotEmpty) {
        url += '&search=$search';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }
      
      return _getDefaultProducts();
    } catch (e) {
      print('خطأ في جلب المنتجات: $e');
      return _getDefaultProducts();
    }
  }

  // البحث عن قطع الغيار
  static Future<List<Map<String, dynamic>>> searchVehicleParts({
    required String makeId,
    required String modelId,
    required String year,
    String? partType,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.apiBaseUrl}${AppConfig.vehiclePartsEndpoint}'),
        headers: AppConfig.defaultHeaders,
        body: json.encode({
          'make_id': makeId,
          'model_id': modelId,
          'year': year,
          'part_type': partType,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }
      
      return _getDefaultProducts();
    } catch (e) {
      print('خطأ في البحث عن قطع الغيار: $e');
      return _getDefaultProducts();
    }
  }

  // الحصول على منتجات فئة معينة
  static Future<List<Map<String, dynamic>>> getCategoryProducts(String categoryId) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}${AppConfig.categoriesEndpoint}/$categoryId/products'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }
      
      return _getDefaultProducts();
    } catch (e) {
      print('خطأ في جلب منتجات الفئة: $e');
      return _getDefaultProducts();
    }
  }

  // منتجات افتراضية في حالة فشل الـ API
  static List<Map<String, dynamic>> _getDefaultProducts() {
    return [
      {
        'id': 1,
        'name': 'فلتر هواء تويوتا كامري 2020',
        'slug': 'toyota-camry-air-filter-2020',
        'price': 45000,
        'sale_price': 45000,
        'original_price': 55000,
        'image': 'https://via.placeholder.com/300x300/1976D2/FFFFFF?text=قطع+غيار',
        'images': ['https://via.placeholder.com/300x300/1976D2/FFFFFF?text=قطع+غيار'],
        'discount_percentage': 18,
        'in_stock': true,
        'stock_quantity': 25,
        'rating': 4.8,
        'reviews_count': 124,
        'description': 'فلتر هواء أصلي لسيارة تويوتا كامري موديل 2020',
        'short_description': 'فلتر هواء أصلي عالي الجودة',
        'sku': 'TOY-CAM-AF-2020',
        'brand': 'Toyota',
        'category': 'فلاتر',
        'tags': ['فلتر', 'هواء', 'تويوتا', 'كامري'],
        'is_featured': true,
        'created_at': '2024-01-01T00:00:00Z',
      },
      {
        'id': 2,
        'name': 'فرامل كيا سبورتاج الأمامية',
        'slug': 'kia-sportage-front-brakes',
        'price': 120000,
        'sale_price': 120000,
        'original_price': 140000,
        'image': 'https://via.placeholder.com/300x300/4CAF50/FFFFFF?text=فلتر+هواء',
        'images': ['https://via.placeholder.com/300x300/4CAF50/FFFFFF?text=فلتر+هواء'],
        'discount_percentage': 14,
        'in_stock': true,
        'stock_quantity': 15,
        'rating': 4.6,
        'reviews_count': 89,
        'description': 'فرامل أمامية أصلية لسيارة كيا سبورتاج',
        'short_description': 'فرامل أمامية عالية الأداء',
        'sku': 'KIA-SPO-FB-001',
        'brand': 'Kia',
        'category': 'فرامل',
        'tags': ['فرامل', 'كيا', 'سبورتاج', 'أمامية'],
        'is_featured': true,
        'created_at': '2024-01-02T00:00:00Z',
      },
      {
        'id': 3,
        'name': 'زيت محرك تويوتا برادو 5W-30',
        'slug': 'toyota-prado-engine-oil-5w30',
        'price': 85000,
        'sale_price': 85000,
        'original_price': null,
        'image': 'https://via.placeholder.com/300x300/FF9800/FFFFFF?text=زيت+محرك',
        'images': ['https://via.placeholder.com/300x300/FF9800/FFFFFF?text=زيت+محرك'],
        'discount_percentage': null,
        'in_stock': true,
        'stock_quantity': 50,
        'rating': 4.9,
        'reviews_count': 203,
        'description': 'زيت محرك أصلي لسيارة تويوتا برادو 5W-30',
        'short_description': 'زيت محرك عالي الجودة',
        'sku': 'TOY-PRA-OIL-5W30',
        'brand': 'Toyota',
        'category': 'زيوت',
        'tags': ['زيت', 'محرك', 'تويوتا', 'برادو'],
        'is_featured': true,
        'created_at': '2024-01-03T00:00:00Z',
      },
      {
        'id': 4,
        'name': 'بطارية نيسان باترول 12V',
        'slug': 'nissan-patrol-battery-12v',
        'price': 180000,
        'sale_price': 180000,
        'original_price': 200000,
        'image': 'https://via.placeholder.com/300x300/9C27B0/FFFFFF?text=بطارية',
        'images': ['https://via.placeholder.com/300x300/9C27B0/FFFFFF?text=بطارية'],
        'discount_percentage': 10,
        'in_stock': false,
        'stock_quantity': 0,
        'rating': 4.7,
        'reviews_count': 67,
        'description': 'بطارية أصلية لسيارة نيسان باترول 12V',
        'short_description': 'بطارية قوية وموثوقة',
        'sku': 'NIS-PAT-BAT-12V',
        'brand': 'Nissan',
        'category': 'كهربائية',
        'tags': ['بطارية', 'نيسان', 'باترول', '12V'],
        'is_featured': true,
        'created_at': '2024-01-04T00:00:00Z',
      },
    ];
  }

  // تحويل URL الصورة
  static String getImageUrl(String? imagePath) {
    return AppConfig.getImageUrl(imagePath);
  }

  // تنسيق السعر
  static String formatPrice(dynamic price) {
    return AppConfig.formatPrice(price);
  }

  // حساب نسبة الخصم
  static int? calculateDiscountPercentage(dynamic originalPrice, dynamic salePrice) {
    if (originalPrice == null || salePrice == null) return null;

    final original = originalPrice is String ? double.tryParse(originalPrice) ?? 0 : originalPrice.toDouble();
    final sale = salePrice is String ? double.tryParse(salePrice) ?? 0 : salePrice.toDouble();

    if (original <= sale) return null;

    return ((original - sale) / original * 100).round();
  }

  // جلب منتجات حسب الفئة
  static Future<List<Map<String, dynamic>>> getProductsByCategory(String categorySlug) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/ecommerce/products?category=$categorySlug'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }

      // في حالة عدم وجود منتجات، نعيد قائمة فارغة
      return [];
    } catch (e) {
      print('خطأ في جلب منتجات الفئة: $e');
      return [];
    }
  }

  // البحث في المنتجات
  static Future<List<Map<String, dynamic>>> searchProducts(String query) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/ecommerce/products?search=${Uri.encodeComponent(query)}'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }

      return [];
    } catch (e) {
      print('خطأ في البحث: $e');
      return [];
    }
  }

  // جلب جميع المنتجات
  static Future<List<Map<String, dynamic>>> getAllProducts() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}${AppConfig.productsEndpoint}'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          List<Map<String, dynamic>> products = List<Map<String, dynamic>>.from(data['data']);
          return products.map((product) => _convertProductData(product)).toList();
        }
      }

      return _getDefaultProducts();
    } catch (e) {
      print('خطأ في جلب المنتجات: $e');
      return _getDefaultProducts();
    }
  }

  // جلب جميع المنتجات مع pagination
  static Future<List<Map<String, dynamic>>> getAllProductsWithPagination({int? limit}) async {
    try {
      List<Map<String, dynamic>> allProducts = [];
      int currentPage = 1;
      bool hasMorePages = true;

      while (hasMorePages) {
        final response = await http.get(
          Uri.parse('${AppConfig.apiBaseUrl}/ecommerce/products?page=$currentPage&per_page=50'),
          headers: AppConfig.defaultHeaders,
        );

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          if (data['data'] != null) {
            final pageProducts = List<Map<String, dynamic>>.from(data['data']);
            allProducts.addAll(pageProducts);

            // فحص إذا كان هناك صفحة تالية
            hasMorePages = data['meta']?['current_page'] < data['meta']?['last_page'];
            currentPage++;

            // إذا كان هناك حد أقصى وتم الوصول إليه
            if (limit != null && allProducts.length >= limit) {
              allProducts = allProducts.take(limit).toList();
              break;
            }
          } else {
            hasMorePages = false;
          }
        } else {
          hasMorePages = false;
        }
      }

      return allProducts;
    } catch (e) {
      print('خطأ في جلب المنتجات: $e');
      return [];
    }
  }

  // جلب أحدث المنتجات
  static Future<List<Map<String, dynamic>>> getLatestProducts({int limit = 10}) async {
    try {
      final allProducts = await getAllProducts();
      // ترتيب حسب ID (الأحدث أولاً)
      allProducts.sort((a, b) => (b['id'] ?? 0).compareTo(a['id'] ?? 0));
      return allProducts.take(limit).toList();
    } catch (e) {
      print('خطأ في جلب أحدث المنتجات: $e');
      return [];
    }
  }

  // جلب المنتجات الأكثر مبيعاً (محاكاة)
  static Future<List<Map<String, dynamic>>> getBestSellingProducts({int limit = 10}) async {
    try {
      final allProducts = await getAllProducts();
      // ترتيب حسب السعر (افتراض أن الأغلى أكثر مبيعاً)
      allProducts.sort((a, b) => (b['price'] ?? 0).compareTo(a['price'] ?? 0));
      return allProducts.take(limit).toList();
    } catch (e) {
      print('خطأ في جلب الأكثر مبيعاً: $e');
      return [];
    }
  }

  // جلب المنتجات المخفضة
  static Future<List<Map<String, dynamic>>> getDiscountedProducts({int limit = 10}) async {
    try {
      final allProducts = await getAllProducts();
      // فلترة المنتجات التي لها خصم (محاكاة - نأخذ منتجات عشوائية)
      allProducts.shuffle();
      return allProducts.take(limit).toList();
    } catch (e) {
      print('خطأ في جلب المنتجات المخفضة: $e');
      return [];
    }
  }

  // جلب منتجات عشوائية
  static Future<List<Map<String, dynamic>>> getRandomProducts({int limit = 10}) async {
    try {
      final allProducts = await getAllProducts();
      allProducts.shuffle();
      return allProducts.take(limit).toList();
    } catch (e) {
      print('خطأ في جلب المنتجات العشوائية: $e');
      return [];
    }
  }
}
