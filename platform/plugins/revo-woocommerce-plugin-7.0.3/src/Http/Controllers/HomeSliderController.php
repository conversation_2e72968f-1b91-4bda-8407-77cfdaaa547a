<?php

namespace Botble\RevoWoocommerce\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;
use Illuminate\Contracts\View\View;
use Botble\Setting\Facades\Setting;

class HomeSliderController extends BaseController
{
    public function index(): View
    {
        $this->pageTitle('إدارة السلايدر الرئيسي');

        // Get sliders from database (placeholder data for now)
        $sliders = $this->getSliders();

        return view('plugins/revo-woocommerce-plugin-7.0.3::home-slider.index', compact('sliders'));
    }

    public function create(): View
    {
        $this->pageTitle('إضافة سلايدر جديد');

        return view('plugins/revo-woocommerce-plugin-7.0.3::home-slider.create');
    }

    public function store(Request $request, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->validate([
            'title' => 'nullable|string|max:255',
            'image' => 'required|string',
            'link' => 'nullable|url',
            'order' => 'nullable|integer|min:0',
        ], [
            'image.required' => 'صورة السلايدر مطلوبة',
            'link.url' => 'الرابط يجب أن يكون رابط صحيح',
            'order.integer' => 'الترتيب يجب أن يكون رقم صحيح',
        ]);

        try {
            // Store slider data
            $this->storeSlider($request->all());

            return $response
                ->setMessage('تم إضافة السلايدر بنجاح!')
                ->setNextUrl(route('revo-woocommerce.home-slider.index'));
        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء حفظ السلايدر: ' . $e->getMessage());
        }
    }

    public function edit($id): View
    {
        $this->pageTitle('تعديل السلايدر');

        $slider = $this->getSlider($id);

        return view('plugins/revo-woocommerce-plugin-7.0.3::home-slider.edit', compact('slider'));
    }

    public function update(Request $request, $id, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->validate([
            'title' => 'nullable|string|max:255',
            'image' => 'required|string',
            'link' => 'nullable|url',
            'order' => 'nullable|integer|min:0',
        ], [
            'image.required' => 'صورة السلايدر مطلوبة',
            'link.url' => 'الرابط يجب أن يكون رابط صحيح',
            'order.integer' => 'الترتيب يجب أن يكون رقم صحيح',
        ]);

        try {
            // Log the update request
            \Log::info('🔄 UPDATE REQUEST RECEIVED:', [
                'id' => $id,
                'request_data' => $request->all(),
                'request_method' => $request->method(),
                'request_url' => $request->url()
            ]);

            // Get current data before update
            $currentSlider = $this->getSlider($id);
            \Log::info('📊 CURRENT SLIDER DATA:', ['current' => $currentSlider]);

            // Update slider data
            $this->updateSlider($id, $request->all());

            // Get data after update to verify
            $updatedSlider = $this->getSlider($id);
            \Log::info('✅ UPDATED SLIDER DATA:', ['updated' => $updatedSlider]);

            return $response
                ->setMessage('تم تحديث السلايدر بنجاح!')
                ->setNextUrl(route('revo-woocommerce.home-slider.index'));
        } catch (\Exception $e) {
            \Log::error('❌ ERROR UPDATING SLIDER:', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء تحديث السلايدر: ' . $e->getMessage());
        }
    }

    public function destroy($id, BaseHttpResponse $response): BaseHttpResponse
    {
        // Delete slider (placeholder implementation)
        $this->deleteSlider($id);

        return $response
            ->setMessage('تم حذف السلايدر بنجاح!');
    }

    protected function getSliders(): array
    {
        // Get sliders from settings
        $slidersJson = Setting::get('revo_home_sliders', '[]');
        $sliders = json_decode($slidersJson, true) ?: [];

        // Normalize all IDs to integers and clean data
        $normalizedSliders = [];
        foreach ($sliders as $slider) {
            $normalizedSliders[] = [
                'id' => (int)$slider['id'],
                'title' => $slider['title'] ?? '',
                'image' => $slider['image'] ?? '',
                'link' => $slider['link'] ?? '',
                'order' => (int)($slider['order'] ?? 1),
                'status' => (bool)($slider['status'] ?? true),
            ];
        }

        // If no sliders exist, return empty array (no default data)
        return $normalizedSliders;
    }

    protected function getSlider($id): array
    {
        $sliders = $this->getSliders();

        // Convert ID to int for comparison
        $id = (int)$id;

        foreach ($sliders as $slider) {
            if ((int)$slider['id'] === $id) {
                \Log::info('Found slider:', ['id' => $id, 'slider' => $slider]);
                return $slider;
            }
        }

        \Log::warning('Slider not found:', ['id' => $id, 'available_sliders' => $sliders]);
        return [];
    }

    protected function storeSlider(array $data): void
    {
        // Store slider data in settings
        $sliders = $this->getSliders();

        // Generate new ID (find max ID and add 1)
        $maxId = 0;
        foreach ($sliders as $slider) {
            if ($slider['id'] > $maxId) {
                $maxId = $slider['id'];
            }
        }
        $newId = $maxId + 1;

        $newSlider = [
            'id' => $newId,
            'title' => $data['title'] ?? '',
            'image' => $data['image'],
            'link' => $data['link'] ?? '',
            'order' => (int)($data['order'] ?? $newId),
            'status' => true,
        ];

        $sliders[] = $newSlider;

        // Debug logging
        \Log::info('Storing slider data:', [
            'new_slider' => $newSlider,
            'all_sliders' => $sliders
        ]);

        Setting::set('revo_home_sliders', json_encode($sliders));
        Setting::save();

        // Verify save
        $saved = Setting::get('revo_home_sliders');
        \Log::info('Saved slider data:', ['saved_data' => $saved]);
    }

    protected function updateSlider($id, array $data): void
    {
        // Update slider data in settings
        $sliders = $this->getSliders();
        $found = false;

        \Log::info('🔍 BEFORE UPDATE - Current sliders:', ['sliders' => $sliders]);
        file_put_contents(storage_path('logs/revo-debug.log'),
            "[" . now() . "] 🔍 BEFORE UPDATE - ID: $id, Data: " . json_encode($data) . "\n",
            FILE_APPEND
        );

        foreach ($sliders as $key => $slider) {
            if ((int)$slider['id'] === (int)$id) {
                $sliders[$key] = [
                    'id' => (int)$id,
                    'title' => $data['title'] ?? '',
                    'image' => $data['image'],
                    'link' => $data['link'] ?? '',
                    'order' => $data['order'] ?? $slider['order'],
                    'status' => $slider['status'] ?? true,
                ];
                $found = true;
                \Log::info('✅ SLIDER UPDATED:', ['updated_slider' => $sliders[$key]]);
                file_put_contents(storage_path('logs/revo-debug.log'),
                    "[" . now() . "] ✅ SLIDER UPDATED: " . json_encode($sliders[$key]) . "\n",
                    FILE_APPEND
                );
                break;
            }
        }

        if (!$found) {
            \Log::error('Slider not found for update:', ['id' => $id, 'available_ids' => array_column($sliders, 'id')]);
            throw new \Exception("السلايدر غير موجود");
        }

        \Log::info('After update - All sliders:', ['sliders' => $sliders]);

        Setting::set('revo_home_sliders', json_encode($sliders));
        Setting::save();

        // Verify save
        $saved = Setting::get('revo_home_sliders');
        \Log::info('Saved slider data after update:', ['saved_data' => $saved]);
    }

    protected function deleteSlider($id): void
    {
        // Delete slider from settings
        $sliders = $this->getSliders();

        $sliders = array_filter($sliders, function($slider) use ($id) {
            return $slider['id'] != $id;
        });

        Setting::set('revo_home_sliders', json_encode(array_values($sliders)));
        Setting::save();
    }
}
