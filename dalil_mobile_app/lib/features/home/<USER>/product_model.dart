class ProductModel {
  final int id;
  final String name;
  final String description;
  final String sku;
  final double price;
  final double? salePrice;
  final String imageUrl;
  final List<String> images;
  final bool inStock;
  final int stockQuantity;
  final double rating;
  final int reviewsCount;
  final List<int> categoryIds;
  final Map<String, dynamic> attributes;
  final bool isFeatured;
  final bool isOnSale;
  final DateTime? createdAt;

  ProductModel({
    required this.id,
    required this.name,
    required this.description,
    required this.sku,
    required this.price,
    this.salePrice,
    required this.imageUrl,
    this.images = const [],
    this.inStock = true,
    this.stockQuantity = 0,
    this.rating = 0.0,
    this.reviewsCount = 0,
    this.categoryIds = const [],
    this.attributes = const {},
    this.isFeatured = false,
    this.isOnSale = false,
    this.createdAt,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      sku: json['sku'] as String,
      price: (json['price'] as num).toDouble(),
      salePrice: json['sale_price'] != null 
          ? (json['sale_price'] as num).toDouble()
          : null,
      imageUrl: json['image_url'] as String,
      images: json['images'] != null
          ? List<String>.from(json['images'])
          : [],
      inStock: json['in_stock'] as bool? ?? true,
      stockQuantity: json['stock_quantity'] as int? ?? 0,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewsCount: json['reviews_count'] as int? ?? 0,
      categoryIds: json['category_ids'] != null
          ? List<int>.from(json['category_ids'])
          : [],
      attributes: json['attributes'] as Map<String, dynamic>? ?? {},
      isFeatured: json['is_featured'] as bool? ?? false,
      isOnSale: json['is_on_sale'] as bool? ?? false,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'sku': sku,
      'price': price,
      'sale_price': salePrice,
      'image_url': imageUrl,
      'images': images,
      'in_stock': inStock,
      'stock_quantity': stockQuantity,
      'rating': rating,
      'reviews_count': reviewsCount,
      'category_ids': categoryIds,
      'attributes': attributes,
      'is_featured': isFeatured,
      'is_on_sale': isOnSale,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // السعر الفعلي (مع الخصم إن وجد)
  double get effectivePrice => salePrice ?? price;

  // نسبة الخصم
  double get discountPercentage {
    if (salePrice == null || salePrice! >= price) return 0.0;
    return ((price - salePrice!) / price) * 100;
  }

  // مبلغ الخصم
  double get discountAmount {
    if (salePrice == null || salePrice! >= price) return 0.0;
    return price - salePrice!;
  }

  // تنسيق السعر
  String get formattedPrice => '${effectivePrice.toStringAsFixed(0)} د.ع';

  // تنسيق السعر الأصلي
  String get formattedOriginalPrice => '${price.toStringAsFixed(0)} د.ع';

  // تنسيق التقييم
  String get formattedRating => rating.toStringAsFixed(1);

  // حالة المخزون
  String get stockStatus {
    if (!inStock) return 'غير متوفر';
    if (stockQuantity == 0) return 'نفد المخزون';
    if (stockQuantity < 5) return 'كمية محدودة';
    return 'متوفر';
  }

  // لون حالة المخزون
  String get stockStatusColor {
    if (!inStock || stockQuantity == 0) return 'red';
    if (stockQuantity < 5) return 'orange';
    return 'green';
  }

  // التحقق من توفر المنتج
  bool get isAvailable => inStock && stockQuantity > 0;

  // الحصول على صفة معينة
  T? getAttribute<T>(String key) {
    return attributes[key] as T?;
  }

  // التحقق من وجود صفة
  bool hasAttribute(String key) {
    return attributes.containsKey(key);
  }

  // الحصول على جميع الصور
  List<String> get allImages {
    List<String> result = [imageUrl];
    result.addAll(images);
    return result.toSet().toList(); // إزالة المكررات
  }

  // التحقق من كون المنتج جديد (أقل من 30 يوم)
  bool get isNew {
    if (createdAt == null) return false;
    final now = DateTime.now();
    final difference = now.difference(createdAt!);
    return difference.inDays <= 30;
  }

  // الحصول على تصنيفات النجوم
  List<bool> get starRatings {
    List<bool> stars = [];
    for (int i = 1; i <= 5; i++) {
      stars.add(rating >= i);
    }
    return stars;
  }

  ProductModel copyWith({
    int? id,
    String? name,
    String? description,
    String? sku,
    double? price,
    double? salePrice,
    String? imageUrl,
    List<String>? images,
    bool? inStock,
    int? stockQuantity,
    double? rating,
    int? reviewsCount,
    List<int>? categoryIds,
    Map<String, dynamic>? attributes,
    bool? isFeatured,
    bool? isOnSale,
    DateTime? createdAt,
  }) {
    return ProductModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      sku: sku ?? this.sku,
      price: price ?? this.price,
      salePrice: salePrice ?? this.salePrice,
      imageUrl: imageUrl ?? this.imageUrl,
      images: images ?? this.images,
      inStock: inStock ?? this.inStock,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      categoryIds: categoryIds ?? this.categoryIds,
      attributes: attributes ?? this.attributes,
      isFeatured: isFeatured ?? this.isFeatured,
      isOnSale: isOnSale ?? this.isOnSale,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, sku: $sku, price: $price)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
