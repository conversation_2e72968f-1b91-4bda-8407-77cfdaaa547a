import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';

class CategoryService {
  
  // الحصول على فئات المنتجات
  static Future<List<Map<String, dynamic>>> getProductCategories() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}${AppConfig.categoriesEndpoint}?limit=${AppConfig.categoriesLimit}'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          List<Map<String, dynamic>> categories = List<Map<String, dynamic>>.from(data['data']);

          // تحويل البيانات وتجميع الفئات المتشابهة
          Map<String, Map<String, dynamic>> uniqueCategories = {};

          for (var category in categories) {
            String categoryName = _normalizeCategoryName(category['name'] ?? '');
            if (categoryName.isNotEmpty && !uniqueCategories.containsKey(categoryName)) {
              uniqueCategories[categoryName] = _convertCategoryData(category, categoryName);
            }
          }

          // إرجاع أول 6 فئات فقط للعرض
          return uniqueCategories.values.take(6).toList();
        }
      }

      // إذا فشل الطلب، إرجاع قائمة فارغة
      return [];
    } catch (e) {
      print('خطأ في جلب الفئات: $e');
      return [];
    }
  }

  // جلب الفئات الرئيسية فقط (parent_id = 0 أو null)
  static Future<List<Map<String, dynamic>>> getMainCategories() async {
    try {
      final allCategories = await getProductCategories();

      // فلترة الفئات الرئيسية فقط
      final mainCategories = allCategories.where((category) {
        final parentId = category['parent_id'];
        return parentId == null || parentId == 0;
      }).toList();

      return mainCategories.take(6).toList(); // أول 6 فئات رئيسية
    } catch (e) {
      print('خطأ في جلب الفئات الرئيسية: $e');
      return [];
    }
  }

  // تحويل بيانات الفئة من API إلى تنسيق التطبيق
  static Map<String, dynamic> _convertCategoryData(Map<String, dynamic> apiCategory, String normalizedName) {
    return {
      'id': apiCategory['id'],
      'name': normalizedName,
      'slug': apiCategory['slug'],
      'icon': _getCategoryIcon(normalizedName),
      'color': _getCategoryColor(normalizedName),
      'image': apiCategory['icon_image'] != null ? AppConfig.getCategoryImageUrl(apiCategory['icon_image']) : _getDefaultCategoryImage(normalizedName),
    };
  }

  // تطبيع أسماء الفئات لتجميع المتشابهة
  static String _normalizeCategoryName(String name) {
    // إزالة السنوات والأرقام
    name = name.replaceAll(RegExp(r'\d{4}[\s\-]*\d{0,4}'), '').trim();
    name = name.replaceAll(RegExp(r'\d+'), '').trim();
    name = name.replaceAll(RegExp(r'[\-\s]+'), ' ').trim();

    // تجميع الأسماء المتشابهة
    if (name.contains('محرك') || name.contains('موتور')) return 'قطع المحرك';
    if (name.contains('فرامل') || name.contains('بريك')) return 'قطع الفرامل';
    if (name.contains('كهرب') || name.contains('بطارية') || name.contains('لايت')) return 'القطع الكهربائية';
    if (name.contains('هيكل') || name.contains('بودي') || name.contains('باب')) return 'قطع الهيكل';
    if (name.contains('فلتر') || name.contains('فيلتر')) return 'الفلاتر';
    if (name.contains('زيت') || name.contains('أويل')) return 'الزيوت';
    if (name.contains('إطار') || name.contains('تاير')) return 'الإطارات';
    if (name.contains('تكييف') || name.contains('مكيف')) return 'التكييف';

    return name.isNotEmpty ? name : 'فئة أخرى';
  }

  // الحصول على أيقونة الفئة
  static String _getCategoryIcon(String categoryName) {
    switch (categoryName) {
      case 'قطع المحرك': return 'settings';
      case 'قطع الفرامل': return 'disc_full';
      case 'القطع الكهربائية': return 'electrical_services';
      case 'قطع الهيكل': return 'car_crash';
      case 'الفلاتر': return 'filter_alt';
      case 'الزيوت': return 'opacity';
      case 'الإطارات': return 'tire_repair';
      case 'التكييف': return 'ac_unit';
      default: return 'category';
    }
  }

  // الحصول على لون الفئة
  static String _getCategoryColor(String categoryName) {
    switch (categoryName) {
      case 'قطع المحرك': return 'red';
      case 'قطع الفرامل': return 'orange';
      case 'القطع الكهربائية': return 'blue';
      case 'قطع الهيكل': return 'purple';
      case 'الفلاتر': return 'green';
      case 'الزيوت': return 'amber';
      case 'الإطارات': return 'brown';
      case 'التكييف': return 'cyan';
      default: return 'grey';
    }
  }

  // الحصول على صورة افتراضية للفئة
  static String _getDefaultCategoryImage(String categoryName) {
    final colorMap = {
      'قطع المحرك': 'F44336',
      'قطع الفرامل': 'FF9800',
      'القطع الكهربائية': '2196F3',
      'قطع الهيكل': '9C27B0',
      'الفلاتر': '4CAF50',
      'الزيوت': 'FFC107',
      'الإطارات': '795548',
      'التكييف': '00BCD4',
    };

    final color = colorMap[categoryName] ?? '9E9E9E';
    final encodedName = Uri.encodeComponent(categoryName);
    return 'https://via.placeholder.com/200x200/$color/FFFFFF?text=$encodedName';
  }

  // الحصول على فئات Vehicle Parts Finder
  static Future<List<Map<String, dynamic>>> getVehicleCategories() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/vehicle-parts/root-categories'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }
      
      return [];
    } catch (e) {
      print('خطأ في جلب فئات المركبات: $e');
      return [];
    }
  }

  // الحصول على الماركات
  static Future<List<Map<String, dynamic>>> getVehicleMakes() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/vehicle-parts/makes'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }
      
      return [];
    } catch (e) {
      print('خطأ في جلب الماركات: $e');
      return [];
    }
  }




  // تحويل URL الصورة
  static String getImageUrl(String? imagePath) {
    return AppConfig.getCategoryImageUrl(imagePath);
  }

  // جلب الفئات الفرعية
  static Future<List<Map<String, dynamic>>> getSubcategories(String parentCategoryId) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/ecommerce/product-categories?parent_id=$parentCategoryId'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }

      return [];
    } catch (e) {
      print('خطأ في جلب الفئات الفرعية: $e');
      return [];
    }
  }

  // جلب فئة واحدة بالتفصيل
  static Future<Map<String, dynamic>?> getCategoryDetails(String categoryId) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/ecommerce/product-categories/$categoryId'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['data'];
      }

      return null;
    } catch (e) {
      print('خطأ في جلب تفاصيل الفئة: $e');
      return null;
    }
  }

  // البحث في الفئات
  static Future<List<Map<String, dynamic>>> searchCategories(String query) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/ecommerce/product-categories?search=${Uri.encodeComponent(query)}'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }

      return [];
    } catch (e) {
      print('خطأ في البحث في الفئات: $e');
      return [];
    }
  }
}
