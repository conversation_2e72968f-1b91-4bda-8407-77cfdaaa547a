import 'package:flutter/material.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/models/user_model.dart';

class AuthProvider extends ChangeNotifier {
  // حالة التحميل
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // المستخدم الحالي
  UserModel? _currentUser;
  UserModel? get currentUser => _currentUser;

  // حالة تسجيل الدخول
  bool _isLoggedIn = false;
  bool get isLoggedIn => _isLoggedIn;

  AuthProvider() {
    _checkAuthStatus();
  }

  // التحقق من حالة المصادقة
  Future<void> _checkAuthStatus() async {
    try {
      _isLoggedIn = await AuthService.instance.isLoggedIn();
      if (_isLoggedIn) {
        _currentUser = await AuthService.instance.getCurrentUser();
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error checking auth status: $e');
    }
  }

  // تسجيل الدخول
  Future<AuthResult> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    _setLoading(true);

    try {
      final result = await AuthService.instance.login(
        email: email,
        password: password,
        rememberMe: rememberMe,
      );

      if (result.success) {
        _currentUser = result.user;
        _isLoggedIn = true;
        notifyListeners();
      }

      return result;
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء تسجيل الدخول',
      );
    } finally {
      _setLoading(false);
    }
  }

  // تسجيل حساب جديد
  Future<AuthResult> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    required String phone,
    String? address,
    String userType = 'customer',
  }) async {
    _setLoading(true);

    try {
      final result = await AuthService.instance.register(
        name: name,
        email: email,
        password: password,
        passwordConfirmation: passwordConfirmation,
        phone: phone,
        address: address,
        userType: userType,
      );

      if (result.success) {
        _currentUser = result.user;
        _isLoggedIn = true;
        notifyListeners();
      }

      return result;
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء إنشاء الحساب',
      );
    } finally {
      _setLoading(false);
    }
  }

  // تسجيل الخروج
  Future<bool> logout() async {
    _setLoading(true);

    try {
      final success = await AuthService.instance.logout();
      
      if (success) {
        _currentUser = null;
        _isLoggedIn = false;
        notifyListeners();
      }

      return success;
    } catch (e) {
      debugPrint('Error during logout: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث الملف الشخصي
  Future<AuthResult> updateProfile({
    required String name,
    required String email,
    required String phone,
    String? address,
  }) async {
    _setLoading(true);

    try {
      final result = await AuthService.instance.updateProfile(
        name: name,
        email: email,
        phone: phone,
        address: address,
      );

      if (result.success) {
        _currentUser = result.user;
        notifyListeners();
      }

      return result;
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء تحديث البيانات',
      );
    } finally {
      _setLoading(false);
    }
  }

  // تغيير كلمة المرور
  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
    required String passwordConfirmation,
  }) async {
    _setLoading(true);

    try {
      final result = await AuthService.instance.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        passwordConfirmation: passwordConfirmation,
      );

      return result;
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء تغيير كلمة المرور',
      );
    } finally {
      _setLoading(false);
    }
  }

  // إعادة تعيين كلمة المرور
  Future<AuthResult> forgotPassword({required String email}) async {
    _setLoading(true);

    try {
      final result = await AuthService.instance.forgotPassword(email: email);
      return result;
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء إرسال طلب إعادة التعيين',
      );
    } finally {
      _setLoading(false);
    }
  }

  // التحقق من البريد الإلكتروني
  Future<AuthResult> verifyEmail({required String token}) async {
    _setLoading(true);

    try {
      final result = await AuthService.instance.verifyEmail(token: token);
      return result;
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء التحقق من البريد الإلكتروني',
      );
    } finally {
      _setLoading(false);
    }
  }

  // إعادة إرسال رمز التحقق
  Future<AuthResult> resendVerificationCode() async {
    _setLoading(true);

    try {
      final result = await AuthService.instance.resendVerificationCode();
      return result;
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء إرسال رمز التحقق',
      );
    } finally {
      _setLoading(false);
    }
  }

  // تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // التحقق من نوع المستخدم
  bool get isWholesaleUser => _currentUser?.isWholesale ?? false;

  // التحقق من التحقق من البريد الإلكتروني
  bool get isEmailVerified => _currentUser?.emailVerified ?? false;

  // الحصول على اسم المستخدم
  String get userName => _currentUser?.name ?? '';

  // الحصول على بريد المستخدم
  String get userEmail => _currentUser?.email ?? '';

  // الحصول على هاتف المستخدم
  String get userPhone => _currentUser?.phone ?? '';

  // الحصول على عنوان المستخدم
  String get userAddress => _currentUser?.address ?? '';

  // الحصول على الأحرف الأولى من الاسم
  String get userInitials => _currentUser?.initials ?? 'U';
}
