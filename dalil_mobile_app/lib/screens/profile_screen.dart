import 'package:flutter/material.dart';
import '../services/auth_service_new.dart';
import '../models/user.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final AuthServiceNew _authService = AuthServiceNew();
  User? _user;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editProfile,
            tooltip: 'تعديل الملف الشخصي',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : !_authService.isLoggedIn
              ? _buildLoginPrompt()
              : _buildProfileContent(),
    );
  }

  Widget _buildProfileContent() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // معلومات المستخدم الأساسية
          _buildUserHeader(),
          
          const SizedBox(height: 20),
          
          // إحصائيات المستخدم
          _buildUserStats(),
          
          const SizedBox(height: 20),
          
          // قائمة الخيارات
          _buildMenuOptions(),
          
          const SizedBox(height: 20),
          
          // إعدادات التطبيق
          _buildAppSettings(),
          
          const SizedBox(height: 20),
          
          // معلومات إضافية
          _buildAdditionalInfo(),
          
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  Widget _buildUserHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF1976D2), Color(0xFF1565C0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF1976D2).withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        children: [
          // صورة المستخدم
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipOval(
              child: _user?.avatar != null
                  ? Image.network(
                      _user!.avatar!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildDefaultAvatar();
                      },
                    )
                  : _buildDefaultAvatar(),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // اسم المستخدم
          Text(
            _user?.displayName ?? 'المستخدم',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 4),
          
          // البريد الإلكتروني
          Text(
            _user?.email ?? '',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // نوع الحساب
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
            decoration: BoxDecoration(
              color: _authService.isWholesaleCustomer
                  ? Colors.green
                  : Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _authService.isWholesaleCustomer ? 'عميل جملة' : 'عميل عادي',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      color: const Color(0xFF1976D2),
      child: Center(
        child: Text(
          _user?.displayName?.isNotEmpty == true
              ? _user!.displayName![0].toUpperCase()
              : 'ع',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 36,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildUserStats() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              icon: Icons.shopping_bag,
              title: 'الطلبات',
              value: '12',
              color: Colors.blue,
            ),
          ),
          Container(
            width: 1,
            height: 50,
            color: Colors.grey[200],
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.favorite,
              title: 'المفضلة',
              value: '8',
              color: Colors.red,
            ),
          ),
          Container(
            width: 1,
            height: 50,
            color: Colors.grey[200],
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.star,
              title: 'المراجعات',
              value: '5',
              color: Colors.amber,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 28),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildMenuOptions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuItem(
            icon: Icons.shopping_bag_outlined,
            title: 'طلباتي',
            subtitle: 'عرض جميع الطلبات',
            onTap: () => _navigateToOrders(),
          ),
          _buildMenuItem(
            icon: Icons.location_on_outlined,
            title: 'العناوين',
            subtitle: 'إدارة عناوين التوصيل',
            onTap: () => _navigateToAddresses(),
          ),
          _buildMenuItem(
            icon: Icons.payment_outlined,
            title: 'طرق الدفع',
            subtitle: 'إدارة بطاقات الدفع',
            onTap: () => _navigateToPaymentMethods(),
          ),
          _buildMenuItem(
            icon: Icons.local_offer_outlined,
            title: 'كوبونات الخصم',
            subtitle: 'عرض الكوبونات المتاحة',
            onTap: () => _navigateToCoupons(),
          ),
          _buildMenuItem(
            icon: Icons.help_outline,
            title: 'المساعدة والدعم',
            subtitle: 'الأسئلة الشائعة والدعم',
            onTap: () => _navigateToSupport(),
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool showDivider = true,
  }) {
    return Column(
      children: [
        ListTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF1976D2).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: const Color(0xFF1976D2),
              size: 24,
            ),
          ),
          title: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          subtitle: Text(
            subtitle,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: onTap,
        ),
        if (showDivider)
          Divider(
            height: 1,
            indent: 72,
            color: Colors.grey[200],
          ),
      ],
    );
  }

  Widget _buildAppSettings() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'إعدادات التطبيق',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          _buildMenuItem(
            icon: Icons.notifications_outlined,
            title: 'الإشعارات',
            subtitle: 'إدارة إشعارات التطبيق',
            onTap: () => _navigateToNotificationSettings(),
          ),
          _buildMenuItem(
            icon: Icons.language_outlined,
            title: 'اللغة',
            subtitle: 'العربية',
            onTap: () => _showLanguageDialog(),
          ),
          _buildMenuItem(
            icon: Icons.dark_mode_outlined,
            title: 'المظهر',
            subtitle: 'فاتح',
            onTap: () => _showThemeDialog(),
          ),
          _buildMenuItem(
            icon: Icons.privacy_tip_outlined,
            title: 'الخصوصية والأمان',
            subtitle: 'إعدادات الخصوصية',
            onTap: () => _navigateToPrivacySettings(),
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuItem(
            icon: Icons.info_outline,
            title: 'حول التطبيق',
            subtitle: 'الإصدار 1.0.0',
            onTap: () => _showAboutDialog(),
          ),
          _buildMenuItem(
            icon: Icons.share_outlined,
            title: 'مشاركة التطبيق',
            subtitle: 'شارك التطبيق مع الأصدقاء',
            onTap: () => _shareApp(),
          ),
          _buildMenuItem(
            icon: Icons.star_outline,
            title: 'تقييم التطبيق',
            subtitle: 'قيم تجربتك معنا',
            onTap: () => _rateApp(),
          ),
          _buildMenuItem(
            icon: Icons.logout,
            title: 'تسجيل الخروج',
            subtitle: 'الخروج من الحساب',
            onTap: () => _showLogoutDialog(),
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildLoginPrompt() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_outline,
              size: 120,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 24),
            Text(
              'يرجى تسجيل الدخول',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'سجل دخولك للوصول إلى ملفك الشخصي وطلباتك',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                // الانتقال لصفحة تسجيل الدخول
              },
              icon: const Icon(Icons.login),
              label: const Text('تسجيل الدخول'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // === دوال الأحداث ===

  Future<void> _loadUserProfile() async {
    setState(() => _isLoading = true);

    try {
      if (_authService.isLoggedIn) {
        _user = _authService.currentUser;
      }
    } catch (e) {
      _showError('خطأ في تحميل الملف الشخصي: ${e.toString()}');
    }

    setState(() => _isLoading = false);
  }

  void _editProfile() {
    _showSuccess('سيتم فتح صفحة تعديل الملف الشخصي');
  }

  void _navigateToOrders() {
    _showSuccess('الانتقال لصفحة الطلبات');
  }

  void _navigateToAddresses() {
    _showSuccess('الانتقال لصفحة العناوين');
  }

  void _navigateToPaymentMethods() {
    _showSuccess('الانتقال لصفحة طرق الدفع');
  }

  void _navigateToCoupons() {
    _showSuccess('الانتقال لصفحة الكوبونات');
  }

  void _navigateToSupport() {
    _showSuccess('الانتقال لصفحة الدعم');
  }

  void _navigateToNotificationSettings() {
    _showSuccess('الانتقال لإعدادات الإشعارات');
  }

  void _navigateToPrivacySettings() {
    _showSuccess('الانتقال لإعدادات الخصوصية');
  }

  void _showLanguageDialog() {
    _showSuccess('عرض خيارات اللغة');
  }

  void _showThemeDialog() {
    _showSuccess('عرض خيارات المظهر');
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'دليل لقطع غيار السيارات',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.car_repair, size: 48),
      children: [
        const Text('تطبيق متخصص في بيع قطع غيار السيارات بأفضل الأسعار وأعلى جودة.'),
      ],
    );
  }

  void _shareApp() {
    _showSuccess('تم نسخ رابط التطبيق');
  }

  void _rateApp() {
    _showSuccess('الانتقال لمتجر التطبيقات للتقييم');
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _logout();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  void _logout() async {
    await _authService.logout();
    setState(() {
      _user = null;
    });
    _showSuccess('تم تسجيل الخروج بنجاح');
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
