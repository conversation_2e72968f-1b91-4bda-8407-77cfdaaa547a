<?php

return [
    'name' => 'Withdrawals',
    'edit' => 'Edit withdrawal',
    'statuses' => [
        'pending' => 'Pending',
        'processing' => 'Processing',
        'completed' => 'Completed',
        'canceled' => 'Canceled',
        'refused' => 'Refused',
    ],
    'amount' => 'Amount',
    'customer' => 'Customer',
    'vendor' => 'Vendor',
    'currency' => 'Currency',
    'forms' => [
        'amount' => 'Amount',
        'amount_placeholder' => 'Enter withdrawal amount',
        'amount_with_balance' => 'Amount (Current balance: :balance)',
        'fee' => 'Fee',
        'fee_fixed_helper' => 'Fixed fee: :fee',
        'fee_percentage_helper' => 'Fee: :fee%',
        'pending_status_helper' => 'To withdraw money, please provide us with your bank information or your PayPal email.',
        'processing_status_helper' => 'We are processing your request. Please wait for us to confirm and send money to your account.',
        'description' => 'Description',
        'created_success_message' => 'Your withdrawal request has been created successfully.',
        'payment_channel' => 'Payment Channel',
        'payment_channel_placeholder' => 'Payment Channel',
        'transaction_id' => 'Transaction ID',
        'transaction_id_placeholder' => 'Transaction ID',
    ],
    'new_withdrawal_request_notifications' => [
        'new_withdrawal_request' => 'New withdrawal request',
        'view' => 'View',
        'description' => ':customer has requested a withdrawal.',
    ],
    'invoice' => [
        'invoice_template_label' => 'Payout Invoice',
        'title' => 'Payout Invoice',
        'created_at' => 'Issued',
        'customer_name' => 'Bill to',
        'payment_method' => 'Payment Method',
        'earnings' => 'Earnings',
        'fee' => 'Withdrawal Fee',
        'total' => 'Total',
        'total_due' => 'Total Amount Due',
        'notes' => 'Notes',
        'payment_instructions' => 'Payment Instructions',
        'bank_name' => 'Bank Name',
        'bank_account_name' => 'Account Name',
        'bank_account_number' => 'Account Number',
        'variables' => [
            'company_logo' => 'Company logo',
            'company_name' => 'Company name',
            'company_address' => 'Company address',
            'company_state' => 'Company state',
            'company_city' => 'Company city',
            'company_zipcode' => 'Company zipcode',
            'company_phone' => 'Company phone',
            'company_email' => 'Company email',
            'company_tax_id' => 'Company tax ID',
            'withdrawal_id' => 'Withdrawal ID',
            'withdrawal_created_at' => 'Withdrawal creation date',
            'withdrawal_customer_name' => 'Customer name',
            'withdrawal_payment_channel' => 'Payment channel',
            'withdrawal_amount' => 'Withdrawal amount',
            'withdrawal_fee' => 'Withdrawal fee',
            'withdrawal_fee_percentage' => 'Withdrawal fee percentage',
            'withdrawal_status' => 'Withdrawal status',
            'withdrawal_description' => 'Withdrawal description',
            'withdrawal_bank_info_name' => 'Bank name',
            'withdrawal_bank_info_number' => 'Bank account number',
            'withdrawal_bank_info_full_name' => 'Account holder name',
        ],
    ],
];
