<?php

namespace Bo<PERSON>ble\Ecommerce\Tables;

use Botble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Ecommerce\Enums\CustomerStatusEnum;
use Bo<PERSON>ble\Ecommerce\Facades\EcommerceHelper;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Botble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Botble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\EmailBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\EmailColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\PhoneColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\Columns\YesNoColumn;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Validation\Rule;
use Symfony\Component\HttpFoundation\Response;

class CustomerTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Customer::class)
            ->addActions([
                EditAction::make()->route('customers.edit'),
                DeleteAction::make()->route('customers.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('avatar', function (Customer $item) {
                if ($this->isExportingToCSV() || $this->isExportingToExcel()) {
                    return $item->avatar_url;
                }

                return Html::tag(
                    'img',
                    '',
                    ['src' => $item->avatar_url, 'alt' => BaseHelper::clean($item->name), 'width' => 50]
                );
            })
            ->editColumn('is_wholesale', function (Customer $item) {
                if ($this->isExportingToCSV() || $this->isExportingToExcel()) {
                    if ($item->is_wholesale === true) {
                        return 'نعم';
                    } elseif ($item->is_wholesale === false) {
                        return 'قيد الموافقة';
                    }
                    return 'لا';
                }

                if ($item->is_wholesale === true) {
                    return Html::tag('span', 'عميل جملة', ['class' => 'badge badge-success']);
                } elseif ($item->is_wholesale === false) {
                    $approveUrl = route('customers.approve-wholesale', $item->id);

                    return Html::tag(
                        'div',
                        Html::tag('span', 'طلب جديد', ['class' => 'badge badge-warning me-1']) .
                        Html::tag(
                            'button',
                            'الموافقة',
                            [
                                'class' => 'btn btn-sm btn-success approve-wholesale-button',
                                'data-url' => $approveUrl,
                                'data-id' => $item->id,
                            ]
                        ),
                        ['class' => 'd-flex align-items-center']
                    );
                }

                return Html::tag('span', 'لا', ['class' => 'badge badge-secondary']);
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'name',
                'email',
                'phone',
                'avatar',
                'created_at',
                'status',
                'confirmed_at',
                'is_wholesale',
            ]);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        $columns = [
            IdColumn::make(),
            Column::make('avatar')
                ->title(trans('plugins/ecommerce::customer.avatar')),
            NameColumn::make()->route('customers.edit'),
        ];

        if (EcommerceHelper::isLoginUsingPhone()) {
            $columns[] = PhoneColumn::make();
        } else {
            $columns[] = EmailColumn::make();

            if (EcommerceHelper::isEnableEmailVerification()) {
                $columns = array_merge($columns, [
                    YesNoColumn::make('confirmed_at')
                        ->title(trans('plugins/ecommerce::customer.email_verified')),
                ]);
            }
        }

        return array_merge($columns, [
            Column::make('is_wholesale')
                ->title('عميل جملة')
                ->searchable(false)
                ->orderable(false),
            CreatedAtColumn::make(),
            StatusColumn::make(),
        ]);
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('customers.create'), 'customers.create');
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('customers.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            NameBulkChange::make(),
            EmailBulkChange::make(),
            StatusBulkChange::make()
                ->choices(CustomerStatusEnum::labels())
                ->validate(['required', Rule::in(CustomerStatusEnum::values())]),
            CreatedAtBulkChange::make(),
        ];
    }

    public function getFilters(): array
    {
        $filters = parent::getFilters();

        if (EcommerceHelper::isEnableEmailVerification()) {
            $filters['confirmed_at'] = [
                'title' => trans('plugins/ecommerce::customer.email_verified'),
                'type' => 'select',
                'choices' => [1 => trans('core/base::base.yes'), 0 => trans('core/base::base.no')],
                'validate' => 'required|in:1,0',
            ];
        }

        $filters['is_wholesale'] = [
            'title' => 'عميل جملة',
            'type' => 'select',
            'choices' => [
                1 => 'عميل جملة',
                0 => 'طلب قيد الموافقة',
                'null' => 'عميل عادي',
            ],
            'validate' => 'nullable|in:1,0,null',
        ];

        return $filters;
    }

    public function renderTable($data = [], $mergeData = []): View|Factory|Response
    {
        if ($this->isEmpty()) {
            return view('plugins/ecommerce::customers.intro');
        }

        $this->scripts = $this->scripts . '
        <script>
            $(document).ready(function() {
                $(document).on("click", ".approve-wholesale-button", function(e) {
                    e.preventDefault();
                    const button = $(this);
                    const url = button.data("url");
                    const customerId = button.data("id");

                    if (confirm("هل أنت متأكد من الموافقة على هذا العميل كعميل جملة؟")) {
                        $.ajax({
                            type: "POST",
                            url: url,
                            data: {_token: $("meta[name=\'csrf-token\']").attr("content")},
                            success: function(res) {
                                if (!res.error) {
                                    button.closest("tr").find(".is_wholesale-column").html(\'<span class="badge badge-success">عميل جملة</span>\');

                                    Botble.showSuccess(res.message);
                                } else {
                                    Botble.showError(res.message);
                                }
                            },
                            error: function(res) {
                                Botble.handleError(res);
                            }
                        });
                    }
                });
            });
        </script>';

        return parent::renderTable($data, $mergeData);
    }

    public function getDefaultButtons(): array
    {
        return array_merge(['export'], parent::getDefaultButtons());
    }

    public function applyFilterCondition(
        Relation|Builder|QueryBuilder $query,
        string $key,
        string $operator,
        ?string $value
    ) {
        if (EcommerceHelper::isEnableEmailVerification() && $key === 'confirmed_at') {
            return $value ? $query->whereNotNull($key) : $query->whereNull($key);
        }

        if ($key === 'is_wholesale') {
            if ($value === 'null') {
                return $query->whereNull($key);
            }

            return $query->where($key, $value);
        }

        return parent::applyFilterCondition($query, $key, $operator, $value);
    }
}
