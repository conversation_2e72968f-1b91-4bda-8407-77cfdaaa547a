import 'package:flutter/material.dart';
import '../models/product.dart';
import '../services/product_service_new.dart';
import '../widgets/product_grid_widget.dart';
import '../widgets/search_filters_widget.dart';

class SearchScreen extends StatefulWidget {
  final String? initialQuery;

  const SearchScreen({
    super.key,
    this.initialQuery,
  });

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final ProductServiceNew _productService = ProductServiceNew();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  List<Product> _products = [];
  List<Category> _categories = [];
  List<Brand> _brands = [];
  
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreProducts = true;
  int _currentPage = 1;
  
  // فلاتر البحث
  String _searchQuery = '';
  List<int> _selectedCategoryIds = [];
  List<int> _selectedBrandIds = [];
  double? _minPrice;
  double? _maxPrice;
  String _sortBy = 'relevance';
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialQuery != null) {
      _searchController.text = widget.initialQuery!;
      _searchQuery = widget.initialQuery!;
    }
    _loadInitialData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // شريط البحث والفلاتر
          _buildSearchBar(),
          
          // فلاتر البحث (قابلة للطي)
          if (_showFilters)
            SearchFiltersWidget(
              categories: _categories,
              brands: _brands,
              selectedCategoryIds: _selectedCategoryIds,
              selectedBrandIds: _selectedBrandIds,
              minPrice: _minPrice,
              maxPrice: _maxPrice,
              sortBy: _sortBy,
              onCategoriesChanged: (categoryIds) {
                setState(() {
                  _selectedCategoryIds = categoryIds;
                });
                _performSearch(resetPage: true);
              },
              onBrandsChanged: (brandIds) {
                setState(() {
                  _selectedBrandIds = brandIds;
                });
                _performSearch(resetPage: true);
              },
              onPriceRangeChanged: (minPrice, maxPrice) {
                setState(() {
                  _minPrice = minPrice;
                  _maxPrice = maxPrice;
                });
                _performSearch(resetPage: true);
              },
              onSortChanged: (sortBy) {
                setState(() {
                  _sortBy = sortBy;
                });
                _performSearch(resetPage: true);
              },
              onClearFilters: _clearFilters,
            ),
          
          // نتائج البحث
          Expanded(
            child: _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('البحث'),
      actions: [
        IconButton(
          icon: Icon(
            _showFilters ? Icons.filter_list : Icons.filter_list_outlined,
            color: _showFilters ? const Color(0xFF1976D2) : null,
          ),
          onPressed: () {
            setState(() {
              _showFilters = !_showFilters;
            });
          },
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث عن قطع الغيار...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                          _performSearch(resetPage: true);
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF1976D2)),
                ),
              ),
              onSubmitted: (query) {
                setState(() {
                  _searchQuery = query;
                });
                _performSearch(resetPage: true);
              },
            ),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _searchQuery = _searchController.text;
              });
              _performSearch(resetPage: true);
            },
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isLoading && _products.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_products.isEmpty && !_isLoading) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // عدد النتائج وخيارات الترتيب
        _buildResultsHeader(),
        
        // شبكة المنتجات
        Expanded(
          child: ProductGridWidget(
            products: _products,
            scrollController: _scrollController,
            isLoadingMore: _isLoadingMore,
            hasMoreProducts: _hasMoreProducts,
            onProductTap: (product) {
              // الانتقال لصفحة تفاصيل المنتج
            },
          ),
        ),
      ],
    );
  }

  Widget _buildResultsHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'النتائج: ${_products.length} منتج',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
          DropdownButton<String>(
            value: _sortBy,
            underline: const SizedBox.shrink(),
            items: const [
              DropdownMenuItem(value: 'relevance', child: Text('الأكثر صلة')),
              DropdownMenuItem(value: 'price_low_high', child: Text('السعر: من الأقل للأعلى')),
              DropdownMenuItem(value: 'price_high_low', child: Text('السعر: من الأعلى للأقل')),
              DropdownMenuItem(value: 'name_a_z', child: Text('الاسم: أ-ي')),
              DropdownMenuItem(value: 'rating', child: Text('الأعلى تقييماً')),
              DropdownMenuItem(value: 'newest', child: Text('الأحدث')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _sortBy = value;
                });
                _performSearch(resetPage: true);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد نتائج',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _searchQuery.isNotEmpty
                  ? 'لم نجد أي منتجات تطابق "${_searchQuery}"'
                  : 'ابحث عن المنتجات التي تحتاجها',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _clearFilters,
              icon: const Icon(Icons.refresh),
              label: const Text('مسح الفلاتر'),
            ),
          ],
        ),
      ),
    );
  }

  // === دوال البحث والتحميل ===

  Future<void> _loadInitialData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل الفئات والعلامات التجارية للفلاتر
      final categoriesResponse = await _productService.getCategories();
      if (categoriesResponse.isSuccess) {
        _categories = categoriesResponse.data!;
      }

      final brandsResponse = await _productService.getBrands();
      if (brandsResponse.isSuccess) {
        _brands = brandsResponse.data!;
      }

      // إجراء البحث الأولي
      if (_searchQuery.isNotEmpty) {
        await _performSearch(resetPage: true);
      }
    } catch (e) {
      _showError('خطأ في تحميل البيانات: ${e.toString()}');
    }

    setState(() => _isLoading = false);
  }

  Future<void> _performSearch({bool resetPage = false}) async {
    if (resetPage) {
      _currentPage = 1;
      _products.clear();
      _hasMoreProducts = true;
    }

    if (!_hasMoreProducts) return;

    setState(() {
      if (resetPage) {
        _isLoading = true;
      } else {
        _isLoadingMore = true;
      }
    });

    try {
      final response = await _productService.searchProducts(
        query: _searchQuery,
        page: _currentPage,
        categoryIds: _selectedCategoryIds.isNotEmpty ? _selectedCategoryIds : null,
        brandIds: _selectedBrandIds.isNotEmpty ? _selectedBrandIds : null,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
        sortBy: _sortBy,
      );

      if (response.isSuccess && response.data != null) {
        final newProducts = response.data!.results;
        
        setState(() {
          if (resetPage) {
            _products = newProducts;
          } else {
            _products.addAll(newProducts);
          }
          
          _hasMoreProducts = newProducts.length >= 12; // حجم الصفحة
          _currentPage++;
        });
      }
    } catch (e) {
      _showError('خطأ في البحث: ${e.toString()}');
    }

    setState(() {
      _isLoading = false;
      _isLoadingMore = false;
    });
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMoreProducts) {
        _performSearch();
      }
    }
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _searchQuery = '';
      _selectedCategoryIds.clear();
      _selectedBrandIds.clear();
      _minPrice = null;
      _maxPrice = null;
      _sortBy = 'relevance';
      _products.clear();
    });
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
