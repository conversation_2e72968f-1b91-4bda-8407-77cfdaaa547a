/* PWA Enhanced Styles for Dalilak Auto Theme 2 */

/* PWA Install Banner */
.pwa-install-banner {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 15px 20px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    display: none;
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.pwa-install-banner .banner-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.pwa-install-banner .banner-text {
    flex: 1;
}

.pwa-install-banner .banner-title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 5px;
}

.pwa-install-banner .banner-description {
    font-size: 14px;
    opacity: 0.9;
}

.pwa-install-banner .banner-actions {
    display: flex;
    gap: 10px;
}

.pwa-install-banner .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pwa-install-banner .btn-install {
    background: #e74c3c;
    color: white;
}

.pwa-install-banner .btn-install:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.pwa-install-banner .btn-dismiss {
    background: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.pwa-install-banner .btn-dismiss:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* PWA Status Indicator */
.pwa-status {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    z-index: 9998;
    display: none;
}

.pwa-status.online {
    background: rgba(46, 204, 113, 0.9);
}

.pwa-status.offline {
    background: rgba(231, 76, 60, 0.9);
}

/* PWA Loading Spinner */
.pwa-loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10000;
    display: none;
}

.pwa-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #2c3e50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* PWA Update Available Banner */
.pwa-update-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 12px 20px;
    text-align: center;
    z-index: 9997;
    display: none;
    animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.pwa-update-banner .update-text {
    font-size: 14px;
    font-weight: 600;
}

.pwa-update-banner .btn-update {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    margin-left: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pwa-update-banner .btn-update:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .pwa-install-banner {
        left: 10px;
        right: 10px;
        bottom: 10px;
    }
    
    .pwa-install-banner .banner-content {
        flex-direction: column;
        text-align: center;
    }
    
    .pwa-install-banner .banner-actions {
        width: 100%;
        justify-content: center;
    }
    
    .pwa-status {
        top: 10px;
        right: 10px;
    }
}

/* RTL Support */
[dir="rtl"] .pwa-install-banner {
    left: 20px;
    right: 20px;
}

[dir="rtl"] .pwa-status {
    right: auto;
    left: 20px;
}

@media (max-width: 768px) {
    [dir="rtl"] .pwa-install-banner {
        left: 10px;
        right: 10px;
    }
    
    [dir="rtl"] .pwa-status {
        left: 10px;
        right: auto;
    }
}
