<?php

namespace Botble\Analytics\Http\Controllers\API;

use Botble\Analytics\Facades\Analytics;
use Bo<PERSON>ble\Analytics\Period;
use Botble\Base\Http\Controllers\BaseController;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AnalyticsController extends BaseController
{
    /**
     * إحصائيات عامة
     *
     * @group Analytics
     */
    public function getGeneralStats(Request $request)
    {
        try {
            $period = $this->getPeriodFromRequest($request);
            
            $totalQuery = Analytics::performQuery(
                $period, 
                ['sessions', 'totalUsers', 'screenPageViews', 'bounceRate']
            )->toArray();

            $sessions = 0;
            $totalUsers = 0;
            $screenPageViews = 0;
            $bounceRate = 0;

            foreach ($totalQuery as $item) {
                $sessions += $item['sessions'];
                $totalUsers += $item['totalUsers'];
                $screenPageViews += $item['screenPageViews'];
                $bounceRate += $item['bounceRate'] ?? 0;
            }

            return $this
                ->httpResponse()
                ->setData([
                    'period' => [
                        'start_date' => $period->startDate->toDateString(),
                        'end_date' => $period->endDate->toDateString(),
                    ],
                    'stats' => [
                        'sessions' => $sessions,
                        'total_users' => $totalUsers,
                        'page_views' => $screenPageViews,
                        'bounce_rate' => count($totalQuery) > 0 ? round($bounceRate / count($totalQuery), 2) : 0,
                    ]
                ])
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching analytics data.'));
        }
    }

    /**
     * أكثر الصفحات زيارة
     *
     * @group Analytics
     */
    public function getTopPages(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'limit' => 'nullable|integer|min:1|max:50'
        ]);

        if ($validator->fails()) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(422)
                ->setMessage($validator->errors()->first());
        }

        try {
            $period = $this->getPeriodFromRequest($request);
            $limit = $request->integer('limit', 20);
            
            $pages = Analytics::fetchMostVisitedPages($period, $limit);

            return $this
                ->httpResponse()
                ->setData([
                    'period' => [
                        'start_date' => $period->startDate->toDateString(),
                        'end_date' => $period->endDate->toDateString(),
                    ],
                    'pages' => $pages->map(function ($page) {
                        return [
                            'title' => $page['pageTitle'] ?? 'Unknown',
                            'url' => $page['fullPageUrl'] ?? '',
                            'page_views' => $page['screenPageViews'] ?? 0,
                        ];
                    })
                ])
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching top pages data.'));
        }
    }

    /**
     * أهم المصادر المرجعية
     *
     * @group Analytics
     */
    public function getTopReferrers(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'limit' => 'nullable|integer|min:1|max:50'
        ]);

        if ($validator->fails()) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(422)
                ->setMessage($validator->errors()->first());
        }

        try {
            $period = $this->getPeriodFromRequest($request);
            $limit = $request->integer('limit', 20);
            
            $referrers = Analytics::fetchTopReferrers($period, $limit);

            return $this
                ->httpResponse()
                ->setData([
                    'period' => [
                        'start_date' => $period->startDate->toDateString(),
                        'end_date' => $period->endDate->toDateString(),
                    ],
                    'referrers' => $referrers->map(function ($referrer) {
                        return [
                            'source' => $referrer['sessionSource'] ?? 'Unknown',
                            'page_views' => $referrer['screenPageViews'] ?? 0,
                        ];
                    })
                ])
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching top referrers data.'));
        }
    }

    /**
     * أهم المتصفحات
     *
     * @group Analytics
     */
    public function getTopBrowsers(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'limit' => 'nullable|integer|min:1|max:20'
        ]);

        if ($validator->fails()) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(422)
                ->setMessage($validator->errors()->first());
        }

        try {
            $period = $this->getPeriodFromRequest($request);
            $limit = $request->integer('limit', 10);
            
            $browsers = Analytics::fetchTopBrowsers($period, $limit);

            return $this
                ->httpResponse()
                ->setData([
                    'period' => [
                        'start_date' => $period->startDate->toDateString(),
                        'end_date' => $period->endDate->toDateString(),
                    ],
                    'browsers' => $browsers->map(function ($browser) {
                        return [
                            'browser' => $browser['browser'] ?? 'Unknown',
                            'sessions' => $browser['sessions'] ?? 0,
                        ];
                    })
                ])
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching top browsers data.'));
        }
    }

    /**
     * إحصائيات الزوار حسب الفترة
     *
     * @group Analytics
     */
    public function getVisitorsStats(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'dimensions' => 'nullable|in:date,hour'
        ]);

        if ($validator->fails()) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(422)
                ->setMessage($validator->errors()->first());
        }

        try {
            $period = $this->getPeriodFromRequest($request);
            $dimensions = $request->input('dimensions', 'date');
            
            $visitors = Analytics::performQuery($period, ['totalUsers', 'screenPageViews'], $dimensions);

            return $this
                ->httpResponse()
                ->setData([
                    'period' => [
                        'start_date' => $period->startDate->toDateString(),
                        'end_date' => $period->endDate->toDateString(),
                    ],
                    'dimensions' => $dimensions,
                    'visitors' => $visitors->map(function ($visitor) use ($dimensions) {
                        $dateKey = $dimensions === 'date' ? 'date' : 'hour';
                        return [
                            $dateKey => $visitor[$dateKey] ?? 'Unknown',
                            'visitors' => $visitor['totalUsers'] ?? 0,
                            'page_views' => $visitor['screenPageViews'] ?? 0,
                        ];
                    })
                ])
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching visitors data.'));
        }
    }

    /**
     * إحصائيات الدول
     *
     * @group Analytics
     */
    public function getCountriesStats(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'limit' => 'nullable|integer|min:1|max:50'
        ]);

        if ($validator->fails()) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(422)
                ->setMessage($validator->errors()->first());
        }

        try {
            $period = $this->getPeriodFromRequest($request);
            $limit = $request->integer('limit', 20);
            
            $countries = Analytics::performQuery($period, ['sessions'], 'country')
                ->sortByDesc('sessions')
                ->take($limit);

            return $this
                ->httpResponse()
                ->setData([
                    'period' => [
                        'start_date' => $period->startDate->toDateString(),
                        'end_date' => $period->endDate->toDateString(),
                    ],
                    'countries' => $countries->map(function ($country) {
                        return [
                            'country' => $country['country'] ?? 'Unknown',
                            'sessions' => $country['sessions'] ?? 0,
                        ];
                    })->values()
                ])
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching countries data.'));
        }
    }

    /**
     * استعلام مخصص
     *
     * @group Analytics
     */
    public function customQuery(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'metrics' => 'required|array',
            'metrics.*' => 'string',
            'dimensions' => 'nullable|array',
            'dimensions.*' => 'string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date'
        ]);

        if ($validator->fails()) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(422)
                ->setMessage($validator->errors()->first())
                ->setData($validator->errors());
        }

        try {
            $period = $this->getPeriodFromRequest($request);
            $metrics = $request->input('metrics');
            $dimensions = $request->input('dimensions', []);
            
            $results = Analytics::performQuery($period, $metrics, $dimensions);

            return $this
                ->httpResponse()
                ->setData([
                    'period' => [
                        'start_date' => $period->startDate->toDateString(),
                        'end_date' => $period->endDate->toDateString(),
                    ],
                    'query' => [
                        'metrics' => $metrics,
                        'dimensions' => $dimensions,
                    ],
                    'results' => $results
                ])
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while executing custom query.'));
        }
    }

    /**
     * الحصول على الفترة الزمنية من الطلب
     */
    protected function getPeriodFromRequest(Request $request): Period
    {
        $startDate = $request->input('start_date') 
            ? Carbon::parse($request->input('start_date'))
            : Carbon::now()->subDays(30);
            
        $endDate = $request->input('end_date')
            ? Carbon::parse($request->input('end_date'))
            : Carbon::now();

        return Period::create($startDate, $endDate);
    }
}
