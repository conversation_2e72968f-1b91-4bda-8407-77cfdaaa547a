(()=>{"use strict";var t={66262:(t,e)=>{e.A=(t,e)=>{const r=t.__vccOpts||t;for(const[t,n]of e)r[t]=n;return r}}},e={};const r=Vue;var n={key:0,class:"btn-group d-block text-end"},a={class:"btn btn-sm btn-secondary",href:"javascript:","data-bs-toggle":"dropdown","aria-expanded":"false"},o={class:"dropdown-menu float-end"},i=["onClick"],c={key:1,class:"row px-3"},l={class:"col-12"},s={class:"list-unstyled"};const u={props:{url:{type:String,default:null,required:!0},date_from:{type:String,default:null,required:!0},date_to:{type:String,default:null,required:!0},format:{type:String,default:"dd/MM/yy",required:!1},filters:{type:Array,default:function(){return[]},required:!1},filterDefault:{type:String,default:"",required:!1}},data:function(){return{isLoading:!0,earningSales:[],colors:["#fcb800","#80bc00"],chart:null,filtering:"",chartFromDate:null,chartToDate:null}},mounted:function(){var t=this;this.setFiltering(),this.chartFromDate=this.date_from,this.chartToDate=this.date_to,this.renderChart(),$event.on("sales-report-chart:reload",(function(e){t.chartFromDate=e.date_from,t.chartToDate=e.date_to,t.renderChart()}))},methods:{setFiltering:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(t||(t=this.filterDefault),this.filters.length){var e=this.filters.find((function(e){return e.key==t}));this.filtering=e?e.text:t}},renderChart:function(){var t=this;this.url&&axios.get(this.url+"?date_from="+this.chartFromDate+"&date_to="+this.chartToDate).then((function(e){if(e.data.error)Botble.showError(e.data.message);else{t.earningSales=e.data.data.earningSales;var r=e.data.data.series,n=e.data.data.colors,a=e.data.data.dates;null===t.chart?(t.chart=new ApexCharts(t.$el.querySelector(".sales-reports-chart"),{series:r,chart:{height:350,type:"area",toolbar:{show:!1}},dataLabels:{enabled:!1},stroke:{curve:"smooth"},colors:n,xaxis:{type:"datetime",categories:a},tooltip:{x:{format:t.format}},noData:{text:BotbleVariables.languages.tables.no_data}}),t.chart.render()):t.chart.updateOptions({series:r,colors:n,xaxis:{type:"datetime",categories:a}})}}))},clickFilter:function(t,e){var r=this;e.preventDefault(),this.setFiltering("...");var n=this;axios.get(n.url+"?date_from="+this.chartFromDate+"&date_to="+this.chartToDate,{params:{filter:t}}).then((function(e){if(e.data.error)Botble.showError(e.data.message);else{n.earningSales=e.data.data.earningSales;var a={xaxis:{type:"datetime",categories:e.data.data.dates},series:e.data.data.series};e.data.data.colors&&(a.colors=e.data.data.colors),r.chart.updateOptions(a)}r.setFiltering(t)}))}}};var f=function r(n){var a=e[n];if(void 0!==a)return a.exports;var o=e[n]={exports:{}};return t[n](o,o.exports,r),o.exports}(66262);const h=(0,f.A)(u,[["render",function(t,e,u,f,h,d){return(0,r.openBlock)(),(0,r.createElementBlock)("div",null,[u.filters.length?((0,r.openBlock)(),(0,r.createElementBlock)("div",n,[(0,r.createElementVNode)("a",a,[e[0]||(e[0]=(0,r.createElementVNode)("i",{class:"fa fa-filter","aria-hidden":"true"},null,-1)),(0,r.createElementVNode)("span",null,(0,r.toDisplayString)(t.filtering),1),e[1]||(e[1]=(0,r.createElementVNode)("i",{class:"fa fa-angle-down"},null,-1))]),(0,r.createElementVNode)("ul",o,[((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(u.filters,(function(t){return(0,r.openBlock)(),(0,r.createElementBlock)("li",{key:t.key},[(0,r.createElementVNode)("a",{href:"#",onClick:function(e){return d.clickFilter(t.key,e)}},(0,r.toDisplayString)(t.text),9,i)])})),128))])])):(0,r.createCommentVNode)("",!0),e[2]||(e[2]=(0,r.createElementVNode)("div",{class:"sales-reports-chart"},null,-1)),t.earningSales.length?((0,r.openBlock)(),(0,r.createElementBlock)("div",c,[(0,r.createElementVNode)("div",l,[(0,r.createElementVNode)("ul",s,[((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(t.earningSales,(function(t){return(0,r.openBlock)(),(0,r.createElementBlock)("li",{key:t.text},[(0,r.createElementVNode)("i",{class:"icon ti ti-circle-filled",style:(0,r.normalizeStyle)({color:t.color})},null,4),(0,r.createTextVNode)(" "+(0,r.toDisplayString)(t.text),1)])})),128))])])])):(0,r.createCommentVNode)("",!0),e[3]||(e[3]=(0,r.createElementVNode)("div",{class:"loading"},null,-1))])}]]);var d={ref:"chartRef",class:"revenue-chart"};function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function m(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */m=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),c=new B(n||[]);return a(i,"_invoke",{value:Y(t,r,c)}),i}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var h="suspendedStart",d="suspendedYield",y="executing",v="completed",g={};function b(){}function w(){}function x(){}var _={};s(_,i,(function(){return this}));var k=Object.getPrototypeOf,E=k&&k(k(F([])));E&&E!==r&&n.call(E,i)&&(_=E);var D=x.prototype=b.prototype=Object.create(_);function L(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(a,o,i,c){var l=f(t[a],t,o);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==p(u)&&n.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):e.resolve(u).then((function(t){s.value=t,i(s)}),(function(t){return r("throw",t,i,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function Y(e,r,n){var a=h;return function(o,i){if(a===y)throw Error("Generator is already running");if(a===v){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var l=O(c,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=y;var s=f(e,r,n);if("normal"===s.type){if(a=n.done?v:d,s.arg===g)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=v,n.method="throw",n.arg=s.arg)}}}function O(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=f(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function B(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function F(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(p(e)+" is not iterable")}return w.prototype=x,a(D,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=s(x,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,s(t,l,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},L(S.prototype),s(S.prototype,c,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new S(u(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},L(D),s(D,l,"Generator"),s(D,i,(function(){return this})),s(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=F,B.prototype={constructor:B,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),N(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;N(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:F(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function y(t,e,r,n,a,o,i){try{var c=t[o](i),l=c.value}catch(t){return void r(t)}c.done?e(l):Promise.resolve(l).then(n,a)}Vue.nextTick;const v={props:{data:{type:Array,default:function(){return[]},required:!0}},data:function(){return{chartData:this.data,chartInstance:null}},mounted:function(){var t=this;this.renderChart(),$event.on("revenue-chart:reload",(function(e){t.chartData=e,t.renderChart()}))},methods:{renderChart:function(){var t,e=this;return(t=m().mark((function t(){var r,n,a,o;return m().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.chartData.length){t.next=2;break}return t.abrupt("return");case 2:r=[],n=[],a=[],o=0,e.chartData.map((function(t){o+=parseFloat(t.value),a.push(t.label),n.push(t.color)})),0===o?e.chartData.map((function(){r.push(0)})):e.chartData.map((function(t){r.push(100/o*parseFloat(t.value))})),null===e.chartInstance?(e.chartInstance=new ApexCharts(e.$refs.chartRef,{series:r,colors:n,chart:{height:"250",type:"donut"},chartOptions:{labels:a},plotOptions:{pie:{donut:{size:"71%",polygons:{strokeWidth:0}},expandOnClick:!0}},states:{hover:{filter:{type:"darken",value:.9}}},dataLabels:{enabled:!1},legend:{show:!1},tooltip:{enabled:!1}}),e.chartInstance.render()):e.chartInstance.updateOptions({series:r,colors:n,chartOptions:{labels:a}}),jQuery&&jQuery().tooltip&&$('[data-bs-toggle="tooltip"]').tooltip({placement:"top",boundary:"window"});case 10:case"end":return t.stop()}}),t)})),function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){y(o,n,a,i,c,"next",t)}function c(t){y(o,n,a,i,c,"throw",t)}i(void 0)}))})()}}},g=(0,f.A)(v,[["render",function(t,e,n,a,o,i){return(0,r.openBlock)(),(0,r.createElementBlock)("div",null,[(0,r.createElementVNode)("div",d,null,512)])}]]);function b(t){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},b(t)}function w(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=b(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==b(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}"undefined"!=typeof vueApp&&vueApp.booting((function(t){t.component("sales-reports-chart",h),t.component("revenue-chart",g)})),$((function(){if(window.moment&&jQuery().daterangepicker){moment.locale($("html").attr("lang"));var t=$(document).find(".date-range-picker"),e=t.data("format")||"YYYY-MM-DD",r=t.data("start-date")||moment().subtract(29,"days"),n=moment(),a=moment().endOf("month");a>n&&(a=n);var o=BotbleVariables.languages.reports,i=w(w(w(w(w(w({},o.today,[n,n]),o.this_week,[moment().startOf("week"),n]),o.last_7_days,[moment().subtract(6,"days"),n]),o.last_30_days,[moment().subtract(29,"days"),n]),o.this_month,[moment().startOf("month"),a]),o.this_year,[moment().startOf("year"),moment().endOf("year")]);t.daterangepicker({ranges:i,alwaysShowCalendars:!0,startDate:r,endDate:a,maxDate:a,opens:"left",drops:"auto",locale:{format:e},autoUpdateInput:!1},(function(e,r,n){$.ajax({url:t.data("href"),data:{date_from:e.format("YYYY-MM-DD"),date_to:r.format("YYYY-MM-DD"),predefined_range:n},type:"GET",success:function(t){if(t.error)Botble.showError(t.message);else{if($("#report-stats-content").length)$(".widget-item").each((function(e,r){var n=$(r).prop("id");$("#".concat(n)).replaceWith($(t.data).find("#".concat(n)))}));else{var n=new URL(window.location.href);n.searchParams.set("date_from",e.format("YYYY-MM-DD")),n.searchParams.set("date_to",r.format("YYYY-MM-DD")),history.pushState({urlPath:n.href},"",n.href),window.location.reload()}window.LaravelDataTables&&Object.keys(window.LaravelDataTables).map((function(t){var n=window.LaravelDataTables[t],a=new URL(n.ajax.url());a.searchParams.set("date_from",e.format("YYYY-MM-DD")),a.searchParams.set("date_to",r.format("YYYY-MM-DD")),n.ajax.url(a.href).load()}))}},error:function(t){Botble.handleError(t)}})})),t.on("apply.daterangepicker",(function(t,r){var n=$(this),a=n.data("format-value");a||(a="__from__ - __to__");var o=a.replace("__from__",r.startDate.format(e)).replace("__to__",r.endDate.format(e));n.find("span").text(o)}))}}))})();