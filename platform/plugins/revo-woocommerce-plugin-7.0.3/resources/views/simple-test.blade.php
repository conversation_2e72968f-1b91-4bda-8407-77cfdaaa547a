@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">🧪 اختبار بسيط للتحديث</h4>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">✅ {{ session('success') }}</div>
                    @endif
                    
                    @if(session('error'))
                        <div class="alert alert-danger">❌ {{ session('error') }}</div>
                    @endif
                    
                    <form action="{{ route('revo-woocommerce.simple-update', 1) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="form-group mb-3">
                            <label>العنوان الجديد:</label>
                            <input type="text" name="title" class="form-control" value="اختبار {{ now()->format('H:i:s') }}" required>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label>الصورة الجديدة:</label>
                            <input type="text" name="image" class="form-control" value="https://dalilakauto.com/storage/test-{{ rand(1,100) }}.png" required>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label>الرابط الجديد:</label>
                            <input type="url" name="link" class="form-control" value="https://test{{ rand(1,100) }}.com">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label>الترتيب:</label>
                            <input type="number" name="order" class="form-control" value="{{ rand(1,5) }}">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">💾 حفظ التحديث</button>
                    </form>
                    
                    <hr>
                    
                    <h5>البيانات الحالية:</h5>
                    <pre>{{ json_encode(json_decode(\Botble\Setting\Facades\Setting::get('revo_home_sliders', '[]'), true), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                </div>
            </div>
        </div>
    </div>
@endsection
