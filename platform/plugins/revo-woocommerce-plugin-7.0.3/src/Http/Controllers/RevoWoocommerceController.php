<?php

namespace Botble\RevoWoocommerce\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;
use Illuminate\Contracts\View\View;
use Botble\Setting\Facades\Setting;

class RevoWoocommerceController extends BaseController
{
    public function index(): View
    {
        $this->pageTitle('RevoWOO - إدارة تطبيق Flutter');

        return view('plugins/revo-woocommerce-plugin-7.0.3::index');
    }

    public function settings(): View
    {
        $this->pageTitle('إعدادات RevoWOO');

        $settings = $this->getSettings();

        return view('plugins/revo-woocommerce-plugin-7.0.3::settings', compact('settings'));
    }

    public function saveSettings(Request $request, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->validate([
            'app_title' => 'required|string|max:255',
            'app_logo' => 'nullable|url',
            'contact_info' => 'nullable|string',
            'about_url' => 'nullable|url',
            'customer_support_url' => 'nullable|url',
            'firebase_server_key' => 'nullable|string',
        ], [
            'app_title.required' => 'عنوان التطبيق مطلوب',
            'app_title.max' => 'عنوان التطبيق يجب أن يكون أقل من 255 حرف',
            'app_logo.url' => 'رابط الشعار يجب أن يكون رابط صحيح',
            'about_url.url' => 'رابط حول التطبيق يجب أن يكون رابط صحيح',
            'customer_support_url.url' => 'رابط دعم العملاء يجب أن يكون رابط صحيح',
        ]);

        $settings = [
            'app_title' => $request->input('app_title'),
            'app_logo' => $request->input('app_logo'),
            'contact_info' => $request->input('contact_info'),
            'about_url' => $request->input('about_url'),
            'customer_support_url' => $request->input('customer_support_url'),
            'firebase_server_key' => $request->input('firebase_server_key'),
            'firebase_enabled' => $request->boolean('firebase_enabled'),
        ];

        $this->storeSettings($settings);

        return $response
            ->setMessage('تم حفظ الإعدادات بنجاح!');
    }

    protected function getSettings(): array
    {
        return [
            'app_title' => Setting::get('revo_woocommerce_app_title', 'RevoWOO App'),
            'app_logo' => Setting::get('revo_woocommerce_app_logo', ''),
            'contact_info' => Setting::get('revo_woocommerce_contact_info', ''),
            'about_url' => Setting::get('revo_woocommerce_about_url', ''),
            'customer_support_url' => Setting::get('revo_woocommerce_customer_support_url', ''),
            'firebase_server_key' => Setting::get('revo_woocommerce_firebase_server_key', ''),
            'firebase_enabled' => Setting::get('revo_woocommerce_firebase_enabled', false),
        ];
    }

    protected function storeSettings(array $settings): void
    {
        foreach ($settings as $key => $value) {
            Setting::set('revo_woocommerce_' . $key, $value);
        }

        Setting::save();
    }
}
