<?php

use Illuminate\Support\Facades\Route;

Route::group([
    'namespace' => 'Botble\RevoWoocommerce\Http\Controllers\Api',
    'prefix' => 'api/revo-admin/v1',
    'middleware' => ['api'],
], function () {
        
        // App settings
        Route::get('settings', 'SettingsController@index');
        Route::get('intro-pages', 'SettingsController@introPages');
        Route::get('splash-screen', 'SettingsController@splashScreen');
        
        // Home content
        Route::get('home/slider', 'HomeController@slider');
        Route::get('home/categories', 'HomeController@categories');
        Route::get('home/mini-banner', 'HomeController@miniBanner');
        Route::get('home/flash-sale', 'HomeController@flashSale');
        Route::get('home/additional-products', 'HomeController@additionalProducts');
        
        // Products
        Route::get('products/hit', 'ProductController@hitProducts');
        Route::get('products/recent-view', 'ProductController@recentViewProducts');
        Route::post('products/add-wishlist', 'ProductController@addToWishlist');
        Route::post('products/remove-wishlist', 'ProductController@removeFromWishlist');
        Route::get('products/wishlist', 'ProductController@getWishlist');
        Route::get('products/variations/{id}', 'ProductController@getVariations');
        
        // Categories
        Route::get('categories/popular', 'CategoryController@popular');
        Route::get('categories/all', 'CategoryController@all');
        
        // Orders
        Route::middleware(['auth:sanctum'])->group(function () {
            Route::get('orders', 'OrderController@index');
            Route::get('orders/{id}', 'OrderController@show');
            Route::get('orders/{id}/reviews', 'OrderController@getReviews');
            Route::post('orders/{id}/review', 'OrderController@addReview');
        });
        
        // User authentication and profile
        Route::group(['prefix' => 'auth'], function () {
            Route::post('register', 'AuthController@register');
            Route::post('login', 'AuthController@login');
            Route::post('facebook-login', 'AuthController@facebookLogin');
            Route::post('google-login', 'AuthController@googleLogin');
            Route::post('apple-login', 'AuthController@appleLogin');
            Route::post('firebase-otp', 'AuthController@firebaseOtpLogin');
            Route::post('forgot-password', 'AuthController@forgotPassword');
        });
        
        Route::middleware(['auth:sanctum'])->group(function () {
            // User profile
            Route::get('profile', 'UserController@profile');
            Route::put('profile', 'UserController@updateProfile');
            Route::get('profile/points', 'UserController@getPoints');
            
            // Firebase token for push notifications
            Route::post('firebase-token', 'UserController@updateFirebaseToken');
            
            // Notifications
            Route::get('notifications', 'NotificationController@index');
            Route::post('notifications/{id}/read', 'NotificationController@markAsRead');
        });
        
        // Checkout
        Route::group(['prefix' => 'checkout'], function () {
            Route::post('create', 'CheckoutController@create');
            Route::post('apply-coupon', 'CheckoutController@applyCoupon');
            Route::post('remove-coupon', 'CheckoutController@removeCoupon');
            Route::post('use-points', 'CheckoutController@usePoints');
        });
        
        // Blog/Comments
        Route::middleware(['auth:sanctum'])->group(function () {
            Route::post('blog/{id}/comment', 'BlogController@addComment');
        });

});
