@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        إضافة صفحة تعريف جديدة
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('revo-woocommerce.intro-pages.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" role="alert">
                        <i class="ti ti-info-circle me-2"></i>
                        <strong>معلومة:</strong> صفحات التعريف تظهر للمستخدمين الجدد عند فتح التطبيق لأول مرة.
                    </div>

                    {!! Form::open(['route' => 'revo-woocommerce.intro-pages.store', 'method' => 'POST', 'class' => 'needs-validation', 'novalidate' => true]) !!}
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label for="title" class="form-label">
                                    <i class="ti ti-heading me-1"></i>
                                    عنوان الصفحة
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="{{ old('title') }}" 
                                       placeholder="مثال: مرحباً بك في تطبيقنا" required>
                                <div class="form-text">العنوان الذي سيظهر في صفحة التعريف</div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="order" class="form-label">
                                    <i class="ti ti-sort-ascending me-1"></i>
                                    الترتيب
                                </label>
                                <input type="number" class="form-control" id="order" name="order" 
                                       value="{{ old('order', 1) }}" 
                                       placeholder="1" min="0">
                                <div class="form-text">ترتيب ظهور الصفحة</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="description" class="form-label">
                            <i class="ti ti-file-text me-1"></i>
                            وصف الصفحة
                            <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="4" 
                                  placeholder="أدخل وصف مفصل لصفحة التعريف" required>{{ old('description') }}</textarea>
                        <div class="form-text">الوصف الذي سيظهر تحت العنوان</div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="image" class="form-label">
                            <i class="ti ti-photo me-1"></i>
                            صورة صفحة التعريف
                            <span class="text-danger">*</span>
                        </label>
                        {!! Form::mediaImage('image', old('image'), ['required' => true]) !!}
                        <div class="form-text">اختر الصورة التي ستظهر في صفحة التعريف (يفضل 400x300 بكسل)</div>
                    </div>
                    
                    <!-- معاينة الصورة -->
                    <div class="form-group mb-4" id="image-preview-container" style="display: none;">
                        <label class="form-label">معاينة الصورة:</label>
                        <div class="border rounded p-3 text-center">
                            <img id="image-preview" src="" alt="معاينة الصورة" 
                                 class="img-fluid rounded" style="max-width: 300px; max-height: 200px;">
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <button type="submit" class="btn btn-success btn-lg me-3">
                                        <i class="ti ti-plus me-2"></i>
                                        إضافة صفحة التعريف
                                    </button>
                                    <a href="{{ route('revo-woocommerce.intro-pages.index') }}" class="btn btn-secondary btn-lg">
                                        <i class="ti ti-x me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    $(document).ready(function() {
        // Form validation
        $('.needs-validation').on('submit', function(e) {
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            $(this).addClass('was-validated');
        });
        
        // Image preview
        $('#image').on('input', function() {
            const imageUrl = $(this).val();
            if (imageUrl) {
                $('#image-preview').attr('src', imageUrl);
                $('#image-preview-container').show();
            } else {
                $('#image-preview-container').hide();
            }
        });
        
        // Success message
        @if(session('success'))
            Botble.showSuccess('{{ session('success') }}');
        @endif
        
        // Error messages
        @if($errors->any())
            @foreach($errors->all() as $error)
                Botble.showError('{{ $error }}');
            @endforeach
        @endif
    });
</script>
@endpush
