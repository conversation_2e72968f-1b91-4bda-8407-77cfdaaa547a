import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../features/home/<USER>/product_model.dart';

class UserStats {
  final int totalOrders;
  final double totalSpent;
  final int favoriteProducts;
  final int reviewsWritten;
  final int cartItems;
  final DateTime lastActivity;
  final Map<String, int> categoryPreferences;
  final List<String> recentSearches;
  final double averageOrderValue;
  final int loyaltyPoints;

  UserStats({
    this.totalOrders = 0,
    this.totalSpent = 0.0,
    this.favoriteProducts = 0,
    this.reviewsWritten = 0,
    this.cartItems = 0,
    required this.lastActivity,
    this.categoryPreferences = const {},
    this.recentSearches = const [],
    this.averageOrderValue = 0.0,
    this.loyaltyPoints = 0,
  });

  Map<String, dynamic> toJson() => {
    'totalOrders': totalOrders,
    'totalSpent': totalSpent,
    'favoriteProducts': favoriteProducts,
    'reviewsWritten': reviewsWritten,
    'cartItems': cartItems,
    'lastActivity': lastActivity.toIso8601String(),
    'categoryPreferences': categoryPreferences,
    'recentSearches': recentSearches,
    'averageOrderValue': averageOrderValue,
    'loyaltyPoints': loyaltyPoints,
  };

  factory UserStats.fromJson(Map<String, dynamic> json) => UserStats(
    totalOrders: json['totalOrders'] ?? 0,
    totalSpent: json['totalSpent']?.toDouble() ?? 0.0,
    favoriteProducts: json['favoriteProducts'] ?? 0,
    reviewsWritten: json['reviewsWritten'] ?? 0,
    cartItems: json['cartItems'] ?? 0,
    lastActivity: DateTime.parse(json['lastActivity'] ?? DateTime.now().toIso8601String()),
    categoryPreferences: Map<String, int>.from(json['categoryPreferences'] ?? {}),
    recentSearches: List<String>.from(json['recentSearches'] ?? []),
    averageOrderValue: json['averageOrderValue']?.toDouble() ?? 0.0,
    loyaltyPoints: json['loyaltyPoints'] ?? 0,
  );

  UserStats copyWith({
    int? totalOrders,
    double? totalSpent,
    int? favoriteProducts,
    int? reviewsWritten,
    int? cartItems,
    DateTime? lastActivity,
    Map<String, int>? categoryPreferences,
    List<String>? recentSearches,
    double? averageOrderValue,
    int? loyaltyPoints,
  }) {
    return UserStats(
      totalOrders: totalOrders ?? this.totalOrders,
      totalSpent: totalSpent ?? this.totalSpent,
      favoriteProducts: favoriteProducts ?? this.favoriteProducts,
      reviewsWritten: reviewsWritten ?? this.reviewsWritten,
      cartItems: cartItems ?? this.cartItems,
      lastActivity: lastActivity ?? this.lastActivity,
      categoryPreferences: categoryPreferences ?? this.categoryPreferences,
      recentSearches: recentSearches ?? this.recentSearches,
      averageOrderValue: averageOrderValue ?? this.averageOrderValue,
      loyaltyPoints: loyaltyPoints ?? this.loyaltyPoints,
    );
  }
}

class ActivityLog {
  final String id;
  final String type;
  final String description;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  ActivityLog({
    required this.id,
    required this.type,
    required this.description,
    required this.timestamp,
    this.metadata,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'description': description,
    'timestamp': timestamp.toIso8601String(),
    'metadata': metadata,
  };

  factory ActivityLog.fromJson(Map<String, dynamic> json) => ActivityLog(
    id: json['id'],
    type: json['type'],
    description: json['description'],
    timestamp: DateTime.parse(json['timestamp']),
    metadata: json['metadata'],
  );
}

class DashboardProvider extends ChangeNotifier {
  static const String _statsKey = 'user_stats';
  static const String _activityKey = 'activity_logs';
  
  UserStats _userStats = UserStats(lastActivity: DateTime.now());
  List<ActivityLog> _activityLogs = [];
  bool _isLoading = false;

  UserStats get userStats => _userStats;
  List<ActivityLog> get activityLogs => List.unmodifiable(_activityLogs);
  bool get isLoading => _isLoading;

  // تحميل البيانات
  Future<void> loadDashboardData() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _loadUserStats();
      await _loadActivityLogs();
    } catch (e) {
      debugPrint('Error loading dashboard data: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل إحصائيات المستخدم
  Future<void> _loadUserStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = prefs.getString(_statsKey);
      
      if (statsJson != null) {
        _userStats = UserStats.fromJson(json.decode(statsJson));
      }
    } catch (e) {
      debugPrint('Error loading user stats: $e');
    }
  }

  // حفظ إحصائيات المستخدم
  Future<void> _saveUserStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_statsKey, json.encode(_userStats.toJson()));
    } catch (e) {
      debugPrint('Error saving user stats: $e');
    }
  }

  // تحميل سجل الأنشطة
  Future<void> _loadActivityLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activityJson = prefs.getStringList(_activityKey) ?? [];
      
      _activityLogs = activityJson
          .map((json) => ActivityLog.fromJson(jsonDecode(json)))
          .toList();
      
      // ترتيب حسب التاريخ (الأحدث أولاً)
      _activityLogs.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    } catch (e) {
      debugPrint('Error loading activity logs: $e');
    }
  }

  // حفظ سجل الأنشطة
  Future<void> _saveActivityLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activityJson = _activityLogs
          .map((log) => json.encode(log.toJson()))
          .toList();
      
      await prefs.setStringList(_activityKey, activityJson);
    } catch (e) {
      debugPrint('Error saving activity logs: $e');
    }
  }

  // تحديث إحصائيات المستخدم
  Future<void> updateUserStats({
    int? totalOrders,
    double? totalSpent,
    int? favoriteProducts,
    int? reviewsWritten,
    int? cartItems,
    Map<String, int>? categoryPreferences,
    List<String>? recentSearches,
    int? loyaltyPoints,
  }) async {
    _userStats = _userStats.copyWith(
      totalOrders: totalOrders,
      totalSpent: totalSpent,
      favoriteProducts: favoriteProducts,
      reviewsWritten: reviewsWritten,
      cartItems: cartItems,
      lastActivity: DateTime.now(),
      categoryPreferences: categoryPreferences,
      recentSearches: recentSearches,
      loyaltyPoints: loyaltyPoints,
      averageOrderValue: totalOrders != null && totalOrders > 0 
          ? (totalSpent ?? _userStats.totalSpent) / totalOrders 
          : _userStats.averageOrderValue,
    );

    await _saveUserStats();
    notifyListeners();
  }

  // إضافة نشاط جديد
  Future<void> addActivity(String type, String description, {Map<String, dynamic>? metadata}) async {
    final activity = ActivityLog(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: type,
      description: description,
      timestamp: DateTime.now(),
      metadata: metadata,
    );

    _activityLogs.insert(0, activity);
    
    // الاحتفاظ بآخر 100 نشاط فقط
    if (_activityLogs.length > 100) {
      _activityLogs = _activityLogs.take(100).toList();
    }

    await _saveActivityLogs();
    notifyListeners();
  }

  // تحديث تفضيلات الفئات
  Future<void> updateCategoryPreference(String categoryId) async {
    final preferences = Map<String, int>.from(_userStats.categoryPreferences);
    preferences[categoryId] = (preferences[categoryId] ?? 0) + 1;
    
    await updateUserStats(categoryPreferences: preferences);
    await addActivity('category_view', 'تم عرض فئة المنتجات', 
        metadata: {'categoryId': categoryId});
  }

  // إضافة بحث حديث
  Future<void> addRecentSearch(String query) async {
    final searches = List<String>.from(_userStats.recentSearches);
    searches.remove(query); // إزالة إذا كان موجود
    searches.insert(0, query); // إضافة في المقدمة
    
    // الاحتفاظ بآخر 10 بحثات فقط
    if (searches.length > 10) {
      searches.removeRange(10, searches.length);
    }
    
    await updateUserStats(recentSearches: searches);
    await addActivity('search', 'تم البحث عن: $query', 
        metadata: {'query': query});
  }

  // إضافة منتج للمفضلة
  Future<void> addToFavorites(ProductModel product) async {
    await updateUserStats(favoriteProducts: _userStats.favoriteProducts + 1);
    await addActivity('favorite_add', 'تم إضافة ${product.name} للمفضلة',
        metadata: {'productId': product.id, 'productName': product.name});
  }

  // إزالة من المفضلة
  Future<void> removeFromFavorites(ProductModel product) async {
    await updateUserStats(favoriteProducts: _userStats.favoriteProducts - 1);
    await addActivity('favorite_remove', 'تم إزالة ${product.name} من المفضلة',
        metadata: {'productId': product.id, 'productName': product.name});
  }

  // إضافة للسلة
  Future<void> addToCart(ProductModel product, int quantity) async {
    await updateUserStats(cartItems: _userStats.cartItems + quantity);
    await addActivity('cart_add', 'تم إضافة ${product.name} للسلة (الكمية: $quantity)',
        metadata: {'productId': product.id, 'productName': product.name, 'quantity': quantity});
  }

  // إزالة من السلة
  Future<void> removeFromCart(ProductModel product, int quantity) async {
    await updateUserStats(cartItems: _userStats.cartItems - quantity);
    await addActivity('cart_remove', 'تم إزالة ${product.name} من السلة',
        metadata: {'productId': product.id, 'productName': product.name, 'quantity': quantity});
  }

  // إضافة تقييم
  Future<void> addReview(ProductModel product, double rating) async {
    await updateUserStats(reviewsWritten: _userStats.reviewsWritten + 1);
    await addActivity('review_add', 'تم تقييم ${product.name} بـ $rating نجوم',
        metadata: {'productId': product.id, 'productName': product.name, 'rating': rating});
  }

  // إتمام طلب
  Future<void> completeOrder(double orderValue, int itemsCount) async {
    await updateUserStats(
      totalOrders: _userStats.totalOrders + 1,
      totalSpent: _userStats.totalSpent + orderValue,
      cartItems: _userStats.cartItems - itemsCount,
      loyaltyPoints: _userStats.loyaltyPoints + (orderValue / 10).round(), // نقطة لكل 10 ريال
    );
    
    await addActivity('order_complete', 'تم إتمام طلب بقيمة ${orderValue.toStringAsFixed(0)} ريال',
        metadata: {'orderValue': orderValue, 'itemsCount': itemsCount});
  }

  // الحصول على الفئة المفضلة
  String? getFavoriteCategory() {
    if (_userStats.categoryPreferences.isEmpty) return null;
    
    return _userStats.categoryPreferences.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  // الحصول على إحصائيات الأنشطة
  Map<String, int> getActivityStats() {
    final stats = <String, int>{};
    
    for (var activity in _activityLogs) {
      stats[activity.type] = (stats[activity.type] ?? 0) + 1;
    }
    
    return stats;
  }

  // الحصول على أنشطة اليوم
  List<ActivityLog> getTodayActivities() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    
    return _activityLogs
        .where((activity) => activity.timestamp.isAfter(startOfDay))
        .toList();
  }

  // الحصول على أنشطة هذا الأسبوع
  List<ActivityLog> getWeekActivities() {
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 7));
    
    return _activityLogs
        .where((activity) => activity.timestamp.isAfter(weekAgo))
        .toList();
  }

  // مسح سجل الأنشطة
  Future<void> clearActivityLogs() async {
    _activityLogs.clear();
    await _saveActivityLogs();
    notifyListeners();
  }

  // إعادة تعيين الإحصائيات
  Future<void> resetStats() async {
    _userStats = UserStats(lastActivity: DateTime.now());
    _activityLogs.clear();
    
    await _saveUserStats();
    await _saveActivityLogs();
    notifyListeners();
  }

  // إنشاء بيانات تجريبية
  Future<void> generateSampleData() async {
    // إحصائيات تجريبية
    await updateUserStats(
      totalOrders: 15,
      totalSpent: 12500.0,
      favoriteProducts: 8,
      reviewsWritten: 12,
      cartItems: 3,
      loyaltyPoints: 1250,
      categoryPreferences: {
        'engines': 5,
        'brakes': 3,
        'tires': 4,
        'oils': 2,
        'filters': 1,
      },
      recentSearches: [
        'بروجكتر دعامية',
        'فرامل أمامية',
        'زيت محرك',
        'إطارات',
        'فلتر هواء',
      ],
    );

    // أنشطة تجريبية
    final sampleActivities = [
      {'type': 'search', 'description': 'تم البحث عن: بروجكتر دعامية'},
      {'type': 'favorite_add', 'description': 'تم إضافة بروجكتر دعامية يسار للمفضلة'},
      {'type': 'cart_add', 'description': 'تم إضافة فرامل أمامية للسلة (الكمية: 2)'},
      {'type': 'review_add', 'description': 'تم تقييم زيت محرك بـ 5.0 نجوم'},
      {'type': 'order_complete', 'description': 'تم إتمام طلب بقيمة 850 ريال'},
      {'type': 'category_view', 'description': 'تم عرض فئة قطع المحرك'},
    ];

    for (var activity in sampleActivities) {
      await addActivity(
        activity['type'] as String,
        activity['description'] as String,
      );
      
      // تأخير صغير لمحاكاة الواقع
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }
}
