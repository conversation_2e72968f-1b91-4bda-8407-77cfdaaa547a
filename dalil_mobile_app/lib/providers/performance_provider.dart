import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class CacheItem<T> {
  final T data;
  final DateTime cachedAt;
  final Duration expiry;

  CacheItem({
    required this.data,
    required this.cachedAt,
    required this.expiry,
  });

  bool get isExpired => DateTime.now().difference(cachedAt) > expiry;

  Map<String, dynamic> toJson() => {
    'data': data,
    'cachedAt': cachedAt.toIso8601String(),
    'expiry': expiry.inMilliseconds,
  };

  factory CacheItem.fromJson(Map<String, dynamic> json, T Function(dynamic) fromJsonT) => CacheItem(
    data: fromJsonT(json['data']),
    cachedAt: DateTime.parse(json['cachedAt']),
    expiry: Duration(milliseconds: json['expiry']),
  );
}

class PerformanceMetrics {
  final Map<String, int> apiCallCounts;
  final Map<String, Duration> apiResponseTimes;
  final Map<String, int> cacheHits;
  final Map<String, int> cacheMisses;
  final int totalMemoryUsage;
  final DateTime lastUpdated;

  PerformanceMetrics({
    this.apiCallCounts = const {},
    this.apiResponseTimes = const {},
    this.cacheHits = const {},
    this.cacheMisses = const {},
    this.totalMemoryUsage = 0,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() => {
    'apiCallCounts': apiCallCounts,
    'apiResponseTimes': apiResponseTimes.map((k, v) => MapEntry(k, v.inMilliseconds)),
    'cacheHits': cacheHits,
    'cacheMisses': cacheMisses,
    'totalMemoryUsage': totalMemoryUsage,
    'lastUpdated': lastUpdated.toIso8601String(),
  };

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) => PerformanceMetrics(
    apiCallCounts: Map<String, int>.from(json['apiCallCounts'] ?? {}),
    apiResponseTimes: (json['apiResponseTimes'] as Map<String, dynamic>? ?? {})
        .map((k, v) => MapEntry(k, Duration(milliseconds: v))),
    cacheHits: Map<String, int>.from(json['cacheHits'] ?? {}),
    cacheMisses: Map<String, int>.from(json['cacheMisses'] ?? {}),
    totalMemoryUsage: json['totalMemoryUsage'] ?? 0,
    lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
  );
}

class PerformanceProvider extends ChangeNotifier {
  static const String _cacheKey = 'performance_cache';
  static const String _metricsKey = 'performance_metrics';
  
  final Map<String, CacheItem> _cache = {};
  PerformanceMetrics _metrics = PerformanceMetrics(lastUpdated: DateTime.now());
  bool _isOptimizationEnabled = true;
  int _maxCacheSize = 100;
  Duration _defaultCacheExpiry = const Duration(minutes: 30);

  PerformanceMetrics get metrics => _metrics;
  bool get isOptimizationEnabled => _isOptimizationEnabled;
  int get cacheSize => _cache.length;
  double get cacheHitRate {
    final totalHits = _metrics.cacheHits.values.fold(0, (sum, hits) => sum + hits);
    final totalMisses = _metrics.cacheMisses.values.fold(0, (sum, misses) => sum + misses);
    final total = totalHits + totalMisses;
    return total > 0 ? totalHits / total : 0.0;
  }

  // تحميل البيانات المحفوظة
  Future<void> loadPerformanceData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تحميل الكاش
      final cacheJson = prefs.getString(_cacheKey);
      if (cacheJson != null) {
        final cacheData = json.decode(cacheJson) as Map<String, dynamic>;
        _cache.clear();
        cacheData.forEach((key, value) {
          try {
            _cache[key] = CacheItem.fromJson(value, (data) => data);
          } catch (e) {
            debugPrint('Error loading cache item $key: $e');
          }
        });
      }
      
      // تحميل المقاييس
      final metricsJson = prefs.getString(_metricsKey);
      if (metricsJson != null) {
        _metrics = PerformanceMetrics.fromJson(json.decode(metricsJson));
      }
      
      // تنظيف الكاش المنتهي الصلاحية
      _cleanExpiredCache();
      
    } catch (e) {
      debugPrint('Error loading performance data: $e');
    }
  }

  // حفظ البيانات
  Future<void> _savePerformanceData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // حفظ الكاش
      final cacheData = <String, dynamic>{};
      _cache.forEach((key, value) {
        cacheData[key] = value.toJson();
      });
      await prefs.setString(_cacheKey, json.encode(cacheData));
      
      // حفظ المقاييس
      await prefs.setString(_metricsKey, json.encode(_metrics.toJson()));
      
    } catch (e) {
      debugPrint('Error saving performance data: $e');
    }
  }

  // إضافة عنصر للكاش
  Future<void> cacheData<T>(String key, T data, {Duration? expiry}) async {
    if (!_isOptimizationEnabled) return;
    
    final cacheItem = CacheItem<T>(
      data: data,
      cachedAt: DateTime.now(),
      expiry: expiry ?? _defaultCacheExpiry,
    );
    
    _cache[key] = cacheItem;
    
    // تنظيف الكاش إذا تجاوز الحد الأقصى
    if (_cache.length > _maxCacheSize) {
      _cleanOldestCache();
    }
    
    await _savePerformanceData();
    notifyListeners();
  }

  // الحصول على بيانات من الكاش
  T? getCachedData<T>(String key) {
    if (!_isOptimizationEnabled) return null;
    
    final cacheItem = _cache[key];
    if (cacheItem == null) {
      _recordCacheMiss(key);
      return null;
    }
    
    if (cacheItem.isExpired) {
      _cache.remove(key);
      _recordCacheMiss(key);
      return null;
    }
    
    _recordCacheHit(key);
    return cacheItem.data as T?;
  }

  // تسجيل استدعاء API
  void recordApiCall(String endpoint, Duration responseTime) {
    final apiCalls = Map<String, int>.from(_metrics.apiCallCounts);
    final responseTimes = Map<String, Duration>.from(_metrics.apiResponseTimes);
    
    apiCalls[endpoint] = (apiCalls[endpoint] ?? 0) + 1;
    responseTimes[endpoint] = responseTime;
    
    _metrics = PerformanceMetrics(
      apiCallCounts: apiCalls,
      apiResponseTimes: responseTimes,
      cacheHits: _metrics.cacheHits,
      cacheMisses: _metrics.cacheMisses,
      totalMemoryUsage: _metrics.totalMemoryUsage,
      lastUpdated: DateTime.now(),
    );
    
    _savePerformanceData();
    notifyListeners();
  }

  // تسجيل إصابة الكاش
  void _recordCacheHit(String key) {
    final cacheHits = Map<String, int>.from(_metrics.cacheHits);
    cacheHits[key] = (cacheHits[key] ?? 0) + 1;
    
    _metrics = PerformanceMetrics(
      apiCallCounts: _metrics.apiCallCounts,
      apiResponseTimes: _metrics.apiResponseTimes,
      cacheHits: cacheHits,
      cacheMisses: _metrics.cacheMisses,
      totalMemoryUsage: _metrics.totalMemoryUsage,
      lastUpdated: DateTime.now(),
    );
  }

  // تسجيل فقدان الكاش
  void _recordCacheMiss(String key) {
    final cacheMisses = Map<String, int>.from(_metrics.cacheMisses);
    cacheMisses[key] = (cacheMisses[key] ?? 0) + 1;
    
    _metrics = PerformanceMetrics(
      apiCallCounts: _metrics.apiCallCounts,
      apiResponseTimes: _metrics.apiResponseTimes,
      cacheHits: _metrics.cacheHits,
      cacheMisses: cacheMisses,
      totalMemoryUsage: _metrics.totalMemoryUsage,
      lastUpdated: DateTime.now(),
    );
  }

  // تنظيف الكاش المنتهي الصلاحية
  void _cleanExpiredCache() {
    final expiredKeys = <String>[];
    
    _cache.forEach((key, value) {
      if (value.isExpired) {
        expiredKeys.add(key);
      }
    });
    
    for (final key in expiredKeys) {
      _cache.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      _savePerformanceData();
      notifyListeners();
    }
  }

  // تنظيف أقدم عناصر الكاش
  void _cleanOldestCache() {
    if (_cache.isEmpty) return;
    
    final sortedEntries = _cache.entries.toList()
      ..sort((a, b) => a.value.cachedAt.compareTo(b.value.cachedAt));
    
    // إزالة أقدم 20% من العناصر
    final removeCount = (_cache.length * 0.2).ceil();
    for (int i = 0; i < removeCount && i < sortedEntries.length; i++) {
      _cache.remove(sortedEntries[i].key);
    }
  }

  // مسح الكاش
  Future<void> clearCache() async {
    _cache.clear();
    await _savePerformanceData();
    notifyListeners();
  }

  // مسح مقاييس الأداء
  Future<void> clearMetrics() async {
    _metrics = PerformanceMetrics(lastUpdated: DateTime.now());
    await _savePerformanceData();
    notifyListeners();
  }

  // تفعيل/إلغاء تفعيل التحسين
  Future<void> setOptimizationEnabled(bool enabled) async {
    _isOptimizationEnabled = enabled;
    if (!enabled) {
      await clearCache();
    }
    await _savePerformanceData();
    notifyListeners();
  }

  // تعيين حجم الكاش الأقصى
  Future<void> setMaxCacheSize(int size) async {
    _maxCacheSize = size;
    if (_cache.length > size) {
      _cleanOldestCache();
    }
    await _savePerformanceData();
    notifyListeners();
  }

  // تعيين مدة انتهاء الكاش الافتراضية
  void setDefaultCacheExpiry(Duration expiry) {
    _defaultCacheExpiry = expiry;
  }

  // الحصول على إحصائيات الأداء
  Map<String, dynamic> getPerformanceStats() {
    final totalApiCalls = _metrics.apiCallCounts.values.fold(0, (sum, calls) => sum + calls);
    final averageResponseTime = _metrics.apiResponseTimes.values.isEmpty
        ? Duration.zero
        : Duration(
            milliseconds: _metrics.apiResponseTimes.values
                .map((d) => d.inMilliseconds)
                .reduce((a, b) => a + b) ~/
                _metrics.apiResponseTimes.length,
          );
    
    return {
      'totalApiCalls': totalApiCalls,
      'averageResponseTime': averageResponseTime.inMilliseconds,
      'cacheSize': _cache.length,
      'cacheHitRate': (cacheHitRate * 100).toStringAsFixed(1),
      'memoryUsage': _metrics.totalMemoryUsage,
      'lastUpdated': _metrics.lastUpdated,
    };
  }

  // الحصول على أبطأ APIs
  List<MapEntry<String, Duration>> getSlowestApis([int limit = 5]) {
    final sortedApis = _metrics.apiResponseTimes.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedApis.take(limit).toList();
  }

  // الحصول على أكثر APIs استخداماً
  List<MapEntry<String, int>> getMostUsedApis([int limit = 5]) {
    final sortedApis = _metrics.apiCallCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedApis.take(limit).toList();
  }

  // تحسين الأداء التلقائي
  Future<void> optimizePerformance() async {
    // تنظيف الكاش المنتهي الصلاحية
    _cleanExpiredCache();
    
    // تقليل حجم الكاش إذا كان كبيراً
    if (_cache.length > _maxCacheSize * 0.8) {
      _cleanOldestCache();
    }
    
    // تحديث إعدادات الكاش بناءً على الاستخدام
    if (cacheHitRate < 0.3) {
      // معدل إصابة منخفض، زيادة مدة الكاش
      _defaultCacheExpiry = Duration(
        milliseconds: (_defaultCacheExpiry.inMilliseconds * 1.5).round(),
      );
    } else if (cacheHitRate > 0.8) {
      // معدل إصابة عالي، يمكن تقليل مدة الكاش
      _defaultCacheExpiry = Duration(
        milliseconds: (_defaultCacheExpiry.inMilliseconds * 0.8).round(),
      );
    }
    
    await _savePerformanceData();
    notifyListeners();
  }

  // إنشاء تقرير الأداء
  String generatePerformanceReport() {
    final stats = getPerformanceStats();
    final slowestApis = getSlowestApis(3);
    final mostUsedApis = getMostUsedApis(3);
    
    final buffer = StringBuffer();
    buffer.writeln('تقرير الأداء');
    buffer.writeln('=' * 30);
    buffer.writeln('إجمالي استدعاءات API: ${stats['totalApiCalls']}');
    buffer.writeln('متوسط وقت الاستجابة: ${stats['averageResponseTime']} مللي ثانية');
    buffer.writeln('حجم الكاش: ${stats['cacheSize']} عنصر');
    buffer.writeln('معدل إصابة الكاش: ${stats['cacheHitRate']}%');
    buffer.writeln();
    
    if (slowestApis.isNotEmpty) {
      buffer.writeln('أبطأ APIs:');
      for (var api in slowestApis) {
        buffer.writeln('  ${api.key}: ${api.value.inMilliseconds} مللي ثانية');
      }
      buffer.writeln();
    }
    
    if (mostUsedApis.isNotEmpty) {
      buffer.writeln('أكثر APIs استخداماً:');
      for (var api in mostUsedApis) {
        buffer.writeln('  ${api.key}: ${api.value} مرة');
      }
    }
    
    return buffer.toString();
  }
}
