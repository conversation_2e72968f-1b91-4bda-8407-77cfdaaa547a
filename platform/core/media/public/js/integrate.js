(()=>{"use strict";var t={27:(t,e,n)=>{n.d(e,{K:()=>c});var i=n(14637),o=n(70418);function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function a(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,s(i.key),i)}}function s(t){var e=function(t,e){if("object"!=r(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==r(e)?e:e+""}var c=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return e=t,r=[{key:"initContext",value:function(){jQuery().contextMenu&&($.contextMenu({selector:'.js-context-menu[data-context="file"]',build:function(){return{items:t._fileContextMenu()}}}),$.contextMenu({selector:'.js-context-menu[data-context="folder"]',build:function(){return{items:t._folderContextMenu()}}}))}},{key:"_fileContextMenu",value:function(){var t={preview:{name:"Preview",icon:function(t,e,n,i){return e.html('<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>\n                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>\n                    </svg> '.concat(i.name)),"context-menu-icon-updated"},callback:function(){i.d.handlePreview()}}};o.M.each(o.M.getConfigs().actions_list,(function(e,n){o.M.each(e,(function(e){t[e.action]={name:e.name,icon:function(t,i,r,a){return i.html("".concat(e.icon," ").concat(o.M.trans("actions_list.".concat(n,".").concat(e.action))||a.name)),"context-menu-icon-updated media-action-".concat(e.action)},callback:function(){$('.js-files-action[data-action="'.concat(e.action,'"]')).trigger("click")}}}))}));var e=[];switch(o.M.getRequestParams().view_in){case"all_media":e=["remove_favorite","delete","restore"];break;case"recent":e=["remove_favorite","delete","restore","make_copy"];break;case"favorites":e=["favorite","delete","restore","make_copy"];break;case"trash":t={preview:t.preview,rename:t.rename,download:t.download,delete:t.delete,restore:t.restore}}o.M.each(e,(function(e){t[e]=void 0})),o.M.getSelectedFolder().length>0&&(t.preview=void 0,t.crop=void 0,t.copy_link=void 0,t.copy_indirect_link=void 0,t.share=void 0,t.alt_text=void 0,o.M.hasPermission("folders.create")||(t.make_copy=void 0),o.M.hasPermission("folders.edit")||(t.rename=void 0),o.M.hasPermission("folders.trash")||(t.trash=void 0,t.restore=void 0),o.M.hasPermission("folders.destroy")||(t.delete=void 0),o.M.hasPermission("folders.favorite")||(t.favorite=void 0,t.remove_favorite=void 0));var n=o.M.getSelectedFiles();return n.length>0&&(o.M.hasPermission("files.create")||(t.make_copy=void 0),o.M.hasPermission("files.edit")||(t.rename=void 0),o.M.hasPermission("files.trash")||(t.trash=void 0,t.restore=void 0),o.M.hasPermission("files.destroy")||(t.delete=void 0),o.M.hasPermission("files.favorite")||(t.favorite=void 0,t.remove_favorite=void 0),n.length>1&&(t.crop=void 0),t.properties=void 0),o.M.arrayFilter(n,(function(t){return t.preview_url})).length||(t.preview=void 0),o.M.arrayFilter(n,(function(t){return"image"===t.type})).length||(t.crop=void 0,t.alt_text=void 0),o.M.arrayFilter(n,(function(t){return t.full_url})).length||(t.copy_link=void 0),t}},{key:"_folderContextMenu",value:function(){var e=t._fileContextMenu();return e.preview=void 0,e.copy_link=void 0,e}},{key:"destroyContext",value:function(){jQuery().contextMenu&&$.contextMenu("destroy")}}],(n=null)&&a(e.prototype,n),r&&a(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r}()},14637:(t,e,n)=>{n.d(e,{d:()=>Jt});const i="undefined"!=typeof window&&void 0!==window.document,o=i?window:{},r=!!i&&"ontouchstart"in o.document.documentElement,a=!!i&&"PointerEvent"in o,s="cropper",c=`${s}-canvas`,l=`${s}-crosshair`,h=`${s}-grid`,d=`${s}-handle`,u=`${s}-image`,p=`${s}-selection`,f=`${s}-shade`,m=`${s}-viewer`,v="select",g="move",y="scale",w="rotate",b="transform",k="none",M="n-resize",_="e-resize",C="s-resize",x="w-resize",S="ne-resize",A="nw-resize",E="se-resize",j="sw-resize",P=a?"pointerdown":r?"touchstart":"mousedown",T=a?"pointermove":r?"touchmove":"mousemove",O=a?"pointerup pointercancel":r?"touchend touchcancel":"mouseup",z="error",R="keydown",I="load",D="wheel",L="action",N="actionend",F="actionstart",Y="change",B="transform";function H(t){return"string"==typeof t}const X=Number.isNaN||o.isNaN;function W(t){return"number"==typeof t&&!X(t)}function q(t){return W(t)&&t>0&&t<1/0}function U(t){return"object"==typeof t&&null!==t}const{hasOwnProperty:K}=Object.prototype;function G(t){if(!U(t))return!1;try{const{constructor:e}=t,{prototype:n}=e;return e&&n&&K.call(n,"isPrototypeOf")}catch(t){return!1}}function V(t){return"function"==typeof t}function J(t){return"object"==typeof t&&null!==t&&1===t.nodeType}const Q=/([a-z\d])([A-Z])/g;function Z(t){return String(t).replace(Q,"$1-$2").toLowerCase()}const tt=/-[A-z\d]/g;function et(t){return t.replace(tt,(t=>t.slice(1).toUpperCase()))}const nt=/\s\s*/;function it(t,e,n,i){e.trim().split(nt).forEach((e=>{t.removeEventListener(e,n,i)}))}function ot(t,e,n,i){e.trim().split(nt).forEach((e=>{t.addEventListener(e,n,i)}))}function rt(t,e,n,i){ot(t,e,n,Object.assign(Object.assign({},i),{once:!0}))}const at={bubbles:!0,cancelable:!0,composed:!0};const st=Promise.resolve();function ct(t){const{documentElement:e}=t.ownerDocument,n=t.getBoundingClientRect();return{left:n.left+(o.pageXOffset-e.clientLeft),top:n.top+(o.pageYOffset-e.clientTop)}}const lt=/deg|g?rad|turn$/i;function ht(t){const e=parseFloat(t)||0;if(0!==e){const[n="rad"]=String(t).match(lt)||[];switch(n.toLowerCase()){case"deg":return e/360*(2*Math.PI);case"grad":return e/400*(2*Math.PI);case"turn":return e*(2*Math.PI)}}return e}const dt="contain";function ut(t,e=dt){const{aspectRatio:n}=t;let{width:i,height:o}=t;const r=q(i),a=q(o);if(r&&a){const t=o*n;e===dt&&t>i||"cover"===e&&t<i?o=i/n:i=o*n}else r?o=i/n:a&&(i=o*n);return{width:i,height:o}}function pt(t,...e){if(0===e.length)return t;const[n,i,o,r,a,s]=t,[c,l,h,d,u,p]=e[0];return pt(t=[n*c+o*l,i*c+r*l,n*h+o*d,i*h+r*d,n*u+o*p+a,i*u+r*p+s],...e.slice(1))}const ft=/left|top|width|height/i,mt="open",vt=new WeakMap,gt=new WeakMap,yt=new Map,wt=o.document&&Array.isArray(o.document.adoptedStyleSheets)&&"replaceSync"in o.CSSStyleSheet.prototype;class bt extends HTMLElement{get $sharedStyle(){return(this.themeColor?`:host{--theme-color: ${this.themeColor};}`:"")+":host([hidden]){display:none!important}"}constructor(){var t,e;super(),this.shadowRootMode=mt,this.slottable=!0;const n=null===(e=null===(t=Object.getPrototypeOf(this))||void 0===t?void 0:t.constructor)||void 0===e?void 0:e.$name;n&&yt.set(n,this.tagName.toLowerCase())}static get observedAttributes(){return["shadow-root-mode","slottable","theme-color"]}attributeChangedCallback(t,e,n){if(Object.is(n,e))return;const i=et(t);let o=n;switch(typeof this[i]){case"boolean":o=null!==n&&"false"!==n;break;case"number":o=Number(n)}switch(this[i]=o,t){case"theme-color":{const t=gt.get(this),e=this.$sharedStyle;t&&e&&(wt?t.replaceSync(e):t.textContent=e);break}}}$propertyChangedCallback(t,e,n){if(!Object.is(n,e))switch(t=Z(t),typeof n){case"boolean":!0===n?this.hasAttribute(t)||this.setAttribute(t,""):this.removeAttribute(t);break;case"number":n=X(n)?"":String(n);default:n?this.getAttribute(t)!==n&&this.setAttribute(t,n):this.removeAttribute(t)}}connectedCallback(){Object.getPrototypeOf(this).constructor.observedAttributes.forEach((t=>{const e=et(t);let n=this[e];(function(t){return void 0===t})(n)||this.$propertyChangedCallback(e,void 0,n),Object.defineProperty(this,e,{enumerable:!0,configurable:!0,get:()=>n,set(t){const i=n;n=t,this.$propertyChangedCallback(e,i,t)}})}));const t=this.attachShadow({mode:this.shadowRootMode||mt});if(this.shadowRoot||vt.set(this,t),gt.set(this,this.$addStyles(this.$sharedStyle)),this.$style&&this.$addStyles(this.$style),this.$template){const e=document.createElement("template");e.innerHTML=this.$template,t.appendChild(e.content)}if(this.slottable){const e=document.createElement("slot");t.appendChild(e)}}disconnectedCallback(){gt.has(this)&&gt.delete(this),vt.has(this)&&vt.delete(this)}$getTagNameOf(t){var e;return null!==(e=yt.get(t))&&void 0!==e?e:t}$setStyles(t){return Object.keys(t).forEach((e=>{let n=t[e];W(n)&&(n=0!==n&&ft.test(e)?`${n}px`:String(n)),this.style[e]=n})),this}$getShadowRoot(){return this.shadowRoot||vt.get(this)}$addStyles(t){let e;const n=this.$getShadowRoot();return wt?(e=new CSSStyleSheet,e.replaceSync(t),n.adoptedStyleSheets=n.adoptedStyleSheets.concat(e)):(e=document.createElement("style"),e.textContent=t,n.appendChild(e)),e}$emit(t,e,n){return function(t,e,n,i){return t.dispatchEvent(new CustomEvent(e,Object.assign(Object.assign(Object.assign({},at),{detail:n}),i)))}(this,t,e,n)}$nextTick(t){return function(t,e){return e?st.then(t?e.bind(t):e):st}(this,t)}static $define(t,e){U(t)&&(e=t,t=""),t||(t=this.$name||this.name),t=Z(t),i&&o.customElements&&!o.customElements.get(t)&&customElements.define(t,this,e)}}bt.$version="2.0.0";class $t extends bt{constructor(){super(...arguments),this.$onPointerDown=null,this.$onPointerMove=null,this.$onPointerUp=null,this.$onWheel=null,this.$wheeling=!1,this.$pointers=new Map,this.$style=':host{display:block;min-height:100px;min-width:200px;overflow:hidden;position:relative;touch-action:none;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}:host([background]){background-color:#fff;background-image:repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc),repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc);background-image:repeating-conic-gradient(#ccc 0 25%,#fff 0 50%);background-position:0 0,.5rem .5rem;background-size:1rem 1rem}:host([disabled]){pointer-events:none}:host([disabled]):after{bottom:0;content:"";cursor:not-allowed;display:block;left:0;pointer-events:none;position:absolute;right:0;top:0}',this.$action=k,this.background=!1,this.disabled=!1,this.scaleStep=.1,this.themeColor="#39f"}static get observedAttributes(){return super.observedAttributes.concat(["background","disabled","scale-step"])}connectedCallback(){super.connectedCallback(),this.disabled||this.$bind()}disconnectedCallback(){this.disabled||this.$unbind(),super.disconnectedCallback()}$propertyChangedCallback(t,e,n){if(!Object.is(n,e)&&(super.$propertyChangedCallback(t,e,n),"disabled"===t))n?this.$unbind():this.$bind()}$bind(){this.$onPointerDown||(this.$onPointerDown=this.$handlePointerDown.bind(this),ot(this,P,this.$onPointerDown)),this.$onPointerMove||(this.$onPointerMove=this.$handlePointerMove.bind(this),ot(this.ownerDocument,T,this.$onPointerMove)),this.$onPointerUp||(this.$onPointerUp=this.$handlePointerUp.bind(this),ot(this.ownerDocument,O,this.$onPointerUp)),this.$onWheel||(this.$onWheel=this.$handleWheel.bind(this),ot(this,D,this.$onWheel,{passive:!1,capture:!0}))}$unbind(){this.$onPointerDown&&(it(this,P,this.$onPointerDown),this.$onPointerDown=null),this.$onPointerMove&&(it(this.ownerDocument,T,this.$onPointerMove),this.$onPointerMove=null),this.$onPointerUp&&(it(this.ownerDocument,O,this.$onPointerUp),this.$onPointerUp=null),this.$onWheel&&(it(this,D,this.$onWheel,{capture:!0}),this.$onWheel=null)}$handlePointerDown(t){const{buttons:e,button:n,type:i}=t;if(this.disabled||("pointerdown"===i&&"mouse"===t.pointerType||"mousedown"===i)&&(W(e)&&1!==e||W(n)&&0!==n||t.ctrlKey))return;const{$pointers:o}=this;let r="";if(t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t,pageX:e,pageY:n})=>{o.set(t,{startX:e,startY:n,endX:e,endY:n})}));else{const{pointerId:e=0,pageX:n,pageY:i}=t;o.set(e,{startX:n,startY:i,endX:n,endY:i})}o.size>1?r=b:J(t.target)&&(r=t.target.action||t.target.getAttribute("action")||""),!1!==this.$emit(F,{action:r,relatedEvent:t})&&(t.preventDefault(),this.$action=r,this.style.willChange="transform")}$handlePointerMove(t){const{$action:e,$pointers:n}=this;if(this.disabled||e===k||0===n.size)return;if(!1===this.$emit("actionmove",{action:e,relatedEvent:t}))return;if(t.preventDefault(),t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t,pageX:e,pageY:i})=>{const o=n.get(t);o&&Object.assign(o,{endX:e,endY:i})}));else{const{pointerId:e=0,pageX:i,pageY:o}=t,r=n.get(e);r&&Object.assign(r,{endX:i,endY:o})}const i={action:e,relatedEvent:t};if(e===b){const e=new Map(n);let o=0,r=0,a=0,s=0,c=t.pageX,l=t.pageY;n.forEach(((t,n)=>{e.delete(n),e.forEach((e=>{let n=e.startX-t.startX,i=e.startY-t.startY,h=e.endX-t.endX,d=e.endY-t.endY,u=0,p=0,f=0,m=0;if(0===n?i<0?f=2*Math.PI:i>0&&(f=Math.PI):n>0?f=Math.PI/2+Math.atan(i/n):n<0&&(f=1.5*Math.PI+Math.atan(i/n)),0===h?d<0?m=2*Math.PI:d>0&&(m=Math.PI):h>0?m=Math.PI/2+Math.atan(d/h):h<0&&(m=1.5*Math.PI+Math.atan(d/h)),m>0||f>0){const n=m-f,i=Math.abs(n);i>o&&(o=i,a=n,c=(t.startX+e.startX)/2,l=(t.startY+e.startY)/2)}if(n=Math.abs(n),i=Math.abs(i),h=Math.abs(h),d=Math.abs(d),n>0&&i>0?u=Math.sqrt(n*n+i*i):n>0?u=n:i>0&&(u=i),h>0&&d>0?p=Math.sqrt(h*h+d*d):h>0?p=h:d>0&&(p=d),u>0&&p>0){const n=(p-u)/u,i=Math.abs(n);i>r&&(r=i,s=n,c=(t.startX+e.startX)/2,l=(t.startY+e.startY)/2)}}))}));const h=o>0,d=r>0;h&&d?(i.rotate=a,i.scale=s,i.centerX=c,i.centerY=l):h?(i.action=w,i.rotate=a,i.centerX=c,i.centerY=l):d?(i.action=y,i.scale=s,i.centerX=c,i.centerY=l):i.action=k}else{const[t]=Array.from(n.values());Object.assign(i,t)}n.forEach((t=>{t.startX=t.endX,t.startY=t.endY})),i.action!==k&&this.$emit(L,i,{cancelable:!1})}$handlePointerUp(t){const{$action:e,$pointers:n}=this;if(!this.disabled&&e!==k&&!1!==this.$emit(N,{action:e,relatedEvent:t})){if(t.preventDefault(),t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t})=>{n.delete(t)}));else{const{pointerId:e=0}=t;n.delete(e)}0===n.size&&(this.style.willChange="",this.$action=k)}}$handleWheel(t){if(this.disabled)return;if(t.preventDefault(),this.$wheeling)return;this.$wheeling=!0,setTimeout((()=>{this.$wheeling=!1}),50);const e=(t.deltaY>0?-1:1)*this.scaleStep;this.$emit(L,{action:y,scale:e,relatedEvent:t},{cancelable:!1})}$setAction(t){return H(t)&&(this.$action=t),this}$toCanvas(t){return new Promise(((e,n)=>{if(!this.isConnected)return void n(new Error("The current element is not connected to the DOM."));const i=document.createElement("canvas");let o=this.offsetWidth,r=this.offsetHeight,a=1;G(t)&&(q(t.width)||q(t.height))&&(({width:o,height:r}=ut({aspectRatio:o/r,width:t.width,height:t.height})),a=o/this.offsetWidth),i.width=o,i.height=r;const s=this.querySelector(this.$getTagNameOf(u));s?s.$ready().then((n=>{const c=i.getContext("2d");if(c){const[e,l,h,d,u,p]=s.$getTransform();let f=u,m=p,v=n.naturalWidth,g=n.naturalHeight;1!==a&&(f*=a,m*=a,v*=a,g*=a);const y=v/2,w=g/2;c.fillStyle="transparent",c.fillRect(0,0,o,r),G(t)&&V(t.beforeDraw)&&t.beforeDraw.call(this,c,i),c.save(),c.translate(y,w),c.transform(e,l,h,d,f,m),c.translate(-y,-w),c.drawImage(n,0,0,v,g),c.restore()}e(i)})).catch(n):e(i)}))}}$t.$name=c,$t.$version="2.0.0";const kt=new WeakMap,Mt=["alt","crossorigin","decoding","importance","loading","referrerpolicy","sizes","src","srcset"];class _t extends bt{constructor(){super(...arguments),this.$matrix=[1,0,0,1,0,0],this.$onLoad=null,this.$onCanvasAction=null,this.$onCanvasActionEnd=null,this.$onCanvasActionStart=null,this.$actionStartTarget=null,this.$style=":host{display:inline-block}img{display:block;height:100%;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;width:100%}",this.$image=new Image,this.initialCenterSize="contain",this.rotatable=!1,this.scalable=!1,this.skewable=!1,this.slottable=!1,this.translatable=!1}set $canvas(t){kt.set(this,t)}get $canvas(){return kt.get(this)}static get observedAttributes(){return super.observedAttributes.concat(Mt,["initial-center-size","rotatable","scalable","skewable","translatable"])}attributeChangedCallback(t,e,n){Object.is(n,e)||(super.attributeChangedCallback(t,e,n),Mt.includes(t)&&this.$image.setAttribute(t,n))}$propertyChangedCallback(t,e,n){if(!Object.is(n,e)&&(super.$propertyChangedCallback(t,e,n),"initialCenterSize"===t))this.$nextTick((()=>{this.$center(n)}))}connectedCallback(){super.connectedCallback();const{$image:t}=this,e=this.closest(this.$getTagNameOf(c));e&&(this.$canvas=e,this.$setStyles({display:"block",position:"absolute"}),this.$onCanvasActionStart=t=>{var e,n;this.$actionStartTarget=null===(n=null===(e=t.detail)||void 0===e?void 0:e.relatedEvent)||void 0===n?void 0:n.target},this.$onCanvasActionEnd=()=>{this.$actionStartTarget=null},this.$onCanvasAction=this.$handleAction.bind(this),ot(e,F,this.$onCanvasActionStart),ot(e,N,this.$onCanvasActionEnd),ot(e,L,this.$onCanvasAction)),this.$onLoad=this.$handleLoad.bind(this),ot(t,I,this.$onLoad),this.$getShadowRoot().appendChild(t)}disconnectedCallback(){const{$image:t,$canvas:e}=this;e&&(this.$onCanvasActionStart&&(it(e,F,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(it(e,N,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasAction&&(it(e,L,this.$onCanvasAction),this.$onCanvasAction=null)),t&&this.$onLoad&&(it(t,I,this.$onLoad),this.$onLoad=null),this.$getShadowRoot().removeChild(t),super.disconnectedCallback()}$handleLoad(){const{$image:t}=this;this.$setStyles({width:t.naturalWidth,height:t.naturalHeight}),this.$canvas&&this.$center(this.initialCenterSize)}$handleAction(t){if(this.hidden||!(this.rotatable||this.scalable||this.translatable))return;const{$canvas:e}=this,{detail:n}=t;if(n){const{relatedEvent:t}=n;let{action:i}=n;switch(i!==b||this.rotatable&&this.scalable||(i=this.rotatable?w:this.scalable?y:k),i){case g:if(this.translatable){let i=null;t&&(i=t.target.closest(this.$getTagNameOf(p))),i||(i=e.querySelector(this.$getTagNameOf(p))),i&&i.multiple&&!i.active&&(i=e.querySelector(`${this.$getTagNameOf(p)}[active]`)),i&&!i.hidden&&i.movable&&!i.dynamic&&this.$actionStartTarget&&i.contains(this.$actionStartTarget)||this.$move(n.endX-n.startX,n.endY-n.startY)}break;case w:if(this.rotatable)if(t){const{x:e,y:i}=this.getBoundingClientRect();this.$rotate(n.rotate,t.clientX-e,t.clientY-i)}else this.$rotate(n.rotate);break;case y:if(this.scalable)if(t){const e=t.target.closest(this.$getTagNameOf(p));if(!e||!e.zoomable||e.zoomable&&e.dynamic){const{x:e,y:i}=this.getBoundingClientRect();this.$zoom(n.scale,t.clientX-e,t.clientY-i)}}else this.$zoom(n.scale);break;case b:if(this.rotatable&&this.scalable){const{rotate:e}=n;let{scale:i}=n;i<0?i=1/(1-i):i+=1;const o=Math.cos(e),r=Math.sin(e),[a,s,c,l]=[o*i,r*i,-r*i,o*i];if(t){const e=this.getBoundingClientRect(),n=t.clientX-e.x,i=t.clientY-e.y,[o,r,h,d]=this.$matrix,u=n-e.width/2,p=i-e.height/2,f=(u*d-h*p)/(o*d-h*r),m=(p*o-r*u)/(o*d-h*r);this.$transform(a,s,c,l,f*(1-a)+m*c,m*(1-l)+f*s)}else this.$transform(a,s,c,l,0,0)}}}}$ready(t){const{$image:e}=this,n=new Promise(((t,n)=>{const i=new Error("Failed to load the image source");if(e.complete)e.naturalWidth>0&&e.naturalHeight>0?t(e):n(i);else{const o=()=>{it(e,z,r),t(e)},r=()=>{it(e,I,o),n(i)};rt(e,I,o),rt(e,z,r)}}));return V(t)&&n.then((e=>(t(e),e))),n}$center(t){const{parentElement:e}=this;if(!e)return this;const n=e.getBoundingClientRect(),i=n.width,o=n.height,{x:r,y:a,width:s,height:c}=this.getBoundingClientRect(),l=r+s/2,h=a+c/2,d=n.x+i/2,u=n.y+o/2;if(this.$move(d-l,u-h),t&&(s!==i||c!==o)){const e=i/s,n=o/c;switch(t){case"cover":this.$scale(Math.max(e,n));break;case"contain":this.$scale(Math.min(e,n))}}return this}$move(t,e=t){if(this.translatable&&W(t)&&W(e)){const[n,i,o,r]=this.$matrix,a=(t*r-o*e)/(n*r-o*i),s=(e*n-i*t)/(n*r-o*i);this.$translate(a,s)}return this}$moveTo(t,e=t){if(this.translatable&&W(t)&&W(e)){const[n,i,o,r]=this.$matrix,a=(t*r-o*e)/(n*r-o*i),s=(e*n-i*t)/(n*r-o*i);this.$setTransform(n,i,o,r,a,s)}return this}$rotate(t,e,n){if(this.rotatable){const i=ht(t),o=Math.cos(i),r=Math.sin(i),[a,s,c,l]=[o,r,-r,o];if(W(e)&&W(n)){const[t,i,o,r]=this.$matrix,{width:h,height:d}=this.getBoundingClientRect(),u=e-h/2,p=n-d/2,f=(u*r-o*p)/(t*r-o*i),m=(p*t-i*u)/(t*r-o*i);this.$transform(a,s,c,l,f*(1-a)-m*c,m*(1-l)-f*s)}else this.$transform(a,s,c,l,0,0)}return this}$zoom(t,e,n){if(!this.scalable||0===t)return this;if(t<0?t=1/(1-t):t+=1,W(e)&&W(n)){const[i,o,r,a]=this.$matrix,{width:s,height:c}=this.getBoundingClientRect(),l=e-s/2,h=n-c/2,d=(l*a-r*h)/(i*a-r*o),u=(h*i-o*l)/(i*a-r*o);this.$transform(t,0,0,t,d*(1-t),u*(1-t))}else this.$scale(t);return this}$scale(t,e=t){return this.scalable&&this.$transform(t,0,0,e,0,0),this}$skew(t,e=0){if(this.skewable){const n=ht(t),i=ht(e);this.$transform(1,Math.tan(i),Math.tan(n),1,0,0)}return this}$translate(t,e=t){return this.translatable&&W(t)&&W(e)&&this.$transform(1,0,0,1,t,e),this}$transform(t,e,n,i,o,r){return W(t)&&W(e)&&W(n)&&W(i)&&W(o)&&W(r)?this.$setTransform(pt(this.$matrix,[t,e,n,i,o,r])):this}$setTransform(t,e,n,i,o,r){if((this.rotatable||this.scalable||this.skewable||this.translatable)&&(Array.isArray(t)&&([t,e,n,i,o,r]=t),W(t)&&W(e)&&W(n)&&W(i)&&W(o)&&W(r))){const a=[...this.$matrix],s=[t,e,n,i,o,r];if(!1===this.$emit(B,{matrix:s,oldMatrix:a}))return this;this.$matrix=s,this.style.transform=`matrix(${s.join(", ")})`}return this}$getTransform(){return this.$matrix.slice()}$resetTransform(){return this.$setTransform([1,0,0,1,0,0])}}_t.$name=u,_t.$version="2.0.0";const Ct=new WeakMap;class xt extends bt{constructor(){super(...arguments),this.$onCanvasChange=null,this.$onCanvasActionEnd=null,this.$onCanvasActionStart=null,this.$style=":host{display:block;height:0;left:0;outline:var(--theme-color) solid 1px;position:relative;top:0;width:0}:host([transparent]){outline-color:transparent}",this.x=0,this.y=0,this.width=0,this.height=0,this.slottable=!1,this.themeColor="rgba(0, 0, 0, 0.65)"}set $canvas(t){Ct.set(this,t)}get $canvas(){return Ct.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["height","width","x","y"])}connectedCallback(){super.connectedCallback();const t=this.closest(this.$getTagNameOf(c));if(t){this.$canvas=t,this.style.position="absolute";const e=t.querySelector(this.$getTagNameOf(p));e&&(this.$onCanvasActionStart=t=>{e.hidden&&t.detail.action===v&&(this.hidden=!1)},this.$onCanvasActionEnd=t=>{e.hidden&&t.detail.action===v&&(this.hidden=!0)},this.$onCanvasChange=t=>{const{x:n,y:i,width:o,height:r}=t.detail;this.$change(n,i,o,r),(e.hidden||0===n&&0===i&&0===o&&0===r)&&(this.hidden=!0)},ot(t,F,this.$onCanvasActionStart),ot(t,N,this.$onCanvasActionEnd),ot(t,Y,this.$onCanvasChange))}this.$render()}disconnectedCallback(){const{$canvas:t}=this;t&&(this.$onCanvasActionStart&&(it(t,F,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(it(t,N,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasChange&&(it(t,Y,this.$onCanvasChange),this.$onCanvasChange=null)),super.disconnectedCallback()}$change(t,e,n=this.width,i=this.height){return W(t)&&W(e)&&W(n)&&W(i)&&(t!==this.x||e!==this.y||n!==this.width||i!==this.height)?(this.hidden&&(this.hidden=!1),this.x=t,this.y=e,this.width=n,this.height=i,this.$render()):this}$reset(){return this.$change(0,0,0,0)}$render(){return this.$setStyles({transform:`translate(${this.x}px, ${this.y}px)`,width:this.width,height:this.height,outlineWidth:o.innerWidth})}}xt.$name=f,xt.$version="2.0.0";class St extends bt{constructor(){super(...arguments),this.$onCanvasCropEnd=null,this.$onCanvasCropStart=null,this.$style=':host{background-color:var(--theme-color);display:block}:host([action=move]),:host([action=select]){height:100%;left:0;position:absolute;top:0;width:100%}:host([action=move]){cursor:move}:host([action=select]){cursor:crosshair}:host([action$=-resize]){background-color:transparent;height:15px;position:absolute;width:15px}:host([action$=-resize]):after{background-color:var(--theme-color);content:"";display:block;height:5px;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:5px}:host([action=n-resize]),:host([action=s-resize]){cursor:ns-resize;left:50%;transform:translateX(-50%);width:100%}:host([action=n-resize]){top:-8px}:host([action=s-resize]){bottom:-8px}:host([action=e-resize]),:host([action=w-resize]){cursor:ew-resize;height:100%;top:50%;transform:translateY(-50%)}:host([action=e-resize]){right:-8px}:host([action=w-resize]){left:-8px}:host([action=ne-resize]){cursor:nesw-resize;right:-8px;top:-8px}:host([action=nw-resize]){cursor:nwse-resize;left:-8px;top:-8px}:host([action=se-resize]){bottom:-8px;cursor:nwse-resize;right:-8px}:host([action=se-resize]):after{height:15px;width:15px}@media (pointer:coarse){:host([action=se-resize]):after{height:10px;width:10px}}@media (pointer:fine){:host([action=se-resize]):after{height:5px;width:5px}}:host([action=sw-resize]){bottom:-8px;cursor:nesw-resize;left:-8px}:host([plain]){background-color:transparent}',this.action=k,this.plain=!1,this.slottable=!1,this.themeColor="rgba(51, 153, 255, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["action","plain"])}}St.$name=d,St.$version="2.0.0";const At=new WeakMap;class Et extends bt{constructor(){super(...arguments),this.$onCanvasAction=null,this.$onCanvasActionStart=null,this.$onCanvasActionEnd=null,this.$onDocumentKeyDown=null,this.$action="",this.$actionStartTarget=null,this.$changing=!1,this.$style=':host{display:block;left:0;position:relative;right:0}:host([outlined]){outline:1px solid var(--theme-color)}:host([multiple]){outline:1px dashed hsla(0,0%,100%,.5)}:host([multiple]):after{bottom:0;content:"";cursor:pointer;display:block;left:0;position:absolute;right:0;top:0}:host([multiple][active]){outline-color:var(--theme-color);z-index:1}:host([multiple])>*{visibility:hidden}:host([multiple][active])>*{visibility:visible}:host([multiple][active]):after{display:none}',this.$initialSelection={x:0,y:0,width:0,height:0},this.x=0,this.y=0,this.width=0,this.height=0,this.aspectRatio=NaN,this.initialAspectRatio=NaN,this.initialCoverage=NaN,this.active=!1,this.linked=!1,this.dynamic=!1,this.movable=!1,this.resizable=!1,this.zoomable=!1,this.multiple=!1,this.keyboard=!1,this.outlined=!1,this.precise=!1}set $canvas(t){At.set(this,t)}get $canvas(){return At.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["active","aspect-ratio","dynamic","height","initial-aspect-ratio","initial-coverage","keyboard","linked","movable","multiple","outlined","precise","resizable","width","x","y","zoomable"])}$propertyChangedCallback(t,e,n){if(!Object.is(n,e))switch(super.$propertyChangedCallback(t,e,n),t){case"x":case"y":case"width":case"height":this.$changing||this.$nextTick((()=>{this.$change(this.x,this.y,this.width,this.height,this.aspectRatio,!0)}));break;case"aspectRatio":case"initialAspectRatio":this.$nextTick((()=>{this.$initSelection()}));break;case"initialCoverage":this.$nextTick((()=>{q(n)&&n<=1&&this.$initSelection(!0,!0)}));break;case"keyboard":this.$nextTick((()=>{this.$canvas&&(n?this.$onDocumentKeyDown||(this.$onDocumentKeyDown=this.$handleKeyDown.bind(this),ot(this.ownerDocument,R,this.$onDocumentKeyDown)):this.$onDocumentKeyDown&&(it(this.ownerDocument,R,this.$onDocumentKeyDown),this.$onDocumentKeyDown=null))}));break;case"multiple":this.$nextTick((()=>{if(this.$canvas){const t=this.$getSelections();n?(t.forEach((t=>{t.active=!1})),this.active=!0,this.$emit(Y,{x:this.x,y:this.y,width:this.width,height:this.height})):(this.active=!1,t.slice(1).forEach((t=>{this.$removeSelection(t)})))}}));break;case"precise":this.$nextTick((()=>{this.$change(this.x,this.y)}));break;case"linked":n&&(this.dynamic=!0)}}connectedCallback(){super.connectedCallback();const t=this.closest(this.$getTagNameOf(c));t?(this.$canvas=t,this.$setStyles({position:"absolute",transform:`translate(${this.x}px, ${this.y}px)`}),this.hidden||this.$render(),this.$initSelection(!0),this.$onCanvasActionStart=this.$handleActionStart.bind(this),this.$onCanvasActionEnd=this.$handleActionEnd.bind(this),this.$onCanvasAction=this.$handleAction.bind(this),ot(t,F,this.$onCanvasActionStart),ot(t,N,this.$onCanvasActionEnd),ot(t,L,this.$onCanvasAction)):this.$render()}disconnectedCallback(){const{$canvas:t}=this;t&&(this.$onCanvasActionStart&&(it(t,F,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(it(t,N,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasAction&&(it(t,L,this.$onCanvasAction),this.$onCanvasAction=null)),super.disconnectedCallback()}$getSelections(){let t=[];return this.parentElement&&(t=Array.from(this.parentElement.querySelectorAll(this.$getTagNameOf(p)))),t}$initSelection(t=!1,e=!1){const{initialCoverage:n,parentElement:i}=this;if(q(n)&&i){const o=this.aspectRatio||this.initialAspectRatio;let r=(e?0:this.width)||i.offsetWidth*n,a=(e?0:this.height)||i.offsetHeight*n;q(o)&&({width:r,height:a}=ut({aspectRatio:o,width:r,height:a})),this.$change(this.x,this.y,r,a),t&&this.$center(),this.$initialSelection={x:this.x,y:this.y,width:this.width,height:this.height}}}$createSelection(){const t=this.cloneNode(!0);return this.hasAttribute("id")&&t.removeAttribute("id"),t.initialCoverage=NaN,this.active=!1,this.parentElement&&this.parentElement.insertBefore(t,this.nextSibling),t}$removeSelection(t=this){if(this.parentElement){const e=this.$getSelections();if(e.length>1){const n=e.indexOf(t),i=e[n+1]||e[n-1];i&&(t.active=!1,this.parentElement.removeChild(t),i.active=!0,i.$emit(Y,{x:i.x,y:i.y,width:i.width,height:i.height}))}else this.$clear()}}$handleActionStart(t){var e,n;const i=null===(n=null===(e=t.detail)||void 0===e?void 0:e.relatedEvent)||void 0===n?void 0:n.target;this.$action="",this.$actionStartTarget=i,!this.hidden&&this.multiple&&!this.active&&i===this&&this.parentElement&&(this.$getSelections().forEach((t=>{t.active=!1})),this.active=!0,this.$emit(Y,{x:this.x,y:this.y,width:this.width,height:this.height}))}$handleAction(t){const{currentTarget:e,detail:n}=t;if(!e||!n)return;const{relatedEvent:i}=n;let{action:o}=n;if(!o&&this.multiple&&(o=this.$action||(null==i?void 0:i.target.action),this.$action=o),!o||this.hidden&&o!==v||this.multiple&&!this.active&&o!==y)return;const r=n.endX-n.startX,a=n.endY-n.startY,{width:s,height:c}=this;let{aspectRatio:l}=this;switch(!q(l)&&i.shiftKey&&(l=q(s)&&q(c)?s/c:1),o){case v:if(0!==r&&0!==a){const{$canvas:t}=this,i=ct(e);(this.multiple&&!this.hidden?this.$createSelection():this).$change(n.startX-i.left,n.startY-i.top,Math.abs(r),Math.abs(a),l),r<0?a<0?o=A:a>0&&(o=j):r>0&&(a<0?o=S:a>0&&(o=E)),t&&(t.$action=o)}break;case g:this.movable&&(this.dynamic||this.$actionStartTarget&&this.contains(this.$actionStartTarget))&&this.$move(r,a);break;case y:if(i&&this.zoomable&&(this.dynamic||this.contains(i.target))){const t=ct(e);this.$zoom(n.scale,i.pageX-t.left,i.pageY-t.top)}break;default:this.$resize(o,r,a,l)}}$handleActionEnd(){this.$action="",this.$actionStartTarget=null}$handleKeyDown(t){if(this.hidden||!this.keyboard||this.multiple&&!this.active||t.defaultPrevented)return;const{activeElement:e}=document;if(!e||!["INPUT","TEXTAREA"].includes(e.tagName)&&!["true","plaintext-only"].includes(e.contentEditable))switch(t.key){case"Backspace":t.metaKey&&(t.preventDefault(),this.$removeSelection());break;case"Delete":t.preventDefault(),this.$removeSelection();break;case"ArrowLeft":t.preventDefault(),this.$move(-1,0);break;case"ArrowRight":t.preventDefault(),this.$move(1,0);break;case"ArrowUp":t.preventDefault(),this.$move(0,-1);break;case"ArrowDown":t.preventDefault(),this.$move(0,1);break;case"+":t.preventDefault(),this.$zoom(.1);break;case"-":t.preventDefault(),this.$zoom(-.1)}}$center(){const{parentElement:t}=this;if(!t)return this;const e=(t.offsetWidth-this.width)/2,n=(t.offsetHeight-this.height)/2;return this.$change(e,n)}$move(t,e=t){return this.$moveTo(this.x+t,this.y+e)}$moveTo(t,e=t){return this.movable?this.$change(t,e):this}$resize(t,e=0,n=0,i=this.aspectRatio){if(!this.resizable)return this;const o=q(i),{$canvas:r}=this;let{x:a,y:s,width:c,height:l}=this;switch(t){case M:s+=n,l-=n,l<0&&(t=C,l=-l,s-=l),o&&(a+=(e=n*i)/2,c-=e,c<0&&(c=-c,a-=c));break;case _:c+=e,c<0&&(t=x,c=-c,a-=c),o&&(s-=(n=e/i)/2,l+=n,l<0&&(l=-l,s-=l));break;case C:l+=n,l<0&&(t=M,l=-l,s-=l),o&&(a-=(e=n*i)/2,c+=e,c<0&&(c=-c,a-=c));break;case x:a+=e,c-=e,c<0&&(t=_,c=-c,a-=c),o&&(s+=(n=e/i)/2,l-=n,l<0&&(l=-l,s-=l));break;case S:o&&(n=-e/i),s+=n,l-=n,c+=e,c<0&&l<0?(t=j,c=-c,l=-l,a-=c,s-=l):c<0?(t=A,c=-c,a-=c):l<0&&(t=E,l=-l,s-=l);break;case A:o&&(n=e/i),a+=e,s+=n,c-=e,l-=n,c<0&&l<0?(t=E,c=-c,l=-l,a-=c,s-=l):c<0?(t=S,c=-c,a-=c):l<0&&(t=j,l=-l,s-=l);break;case E:o&&(n=e/i),c+=e,l+=n,c<0&&l<0?(t=A,c=-c,l=-l,a-=c,s-=l):c<0?(t=j,c=-c,a-=c):l<0&&(t=S,l=-l,s-=l);break;case j:o&&(n=-e/i),a+=e,c-=e,l+=n,c<0&&l<0?(t=S,c=-c,l=-l,a-=c,s-=l):c<0?(t=E,c=-c,a-=c):l<0&&(t=A,l=-l,s-=l)}return r&&r.$setAction(t),this.$change(a,s,c,l)}$zoom(t,e,n){if(!this.zoomable||0===t)return this;t<0?t=1/(1-t):t+=1;const{width:i,height:o}=this,r=i*t,a=o*t;let s=this.x,c=this.y;return W(e)&&W(n)?(s-=(r-i)*((e-this.x)/i),c-=(a-o)*((n-this.y)/o)):(s-=(r-i)/2,c-=(a-o)/2),this.$change(s,c,r,a)}$change(t,e,n=this.width,i=this.height,o=this.aspectRatio,r=!1){return this.$changing||!W(t)||!W(e)||!W(n)||!W(i)||n<0||i<0?this:(q(o)&&({width:n,height:i}=ut({aspectRatio:o,width:n,height:i},"cover")),this.precise||(t=Math.round(t),e=Math.round(e),n=Math.round(n),i=Math.round(i)),t===this.x&&e===this.y&&n===this.width&&i===this.height&&Object.is(o,this.aspectRatio)&&!r?this:(this.hidden&&(this.hidden=!1),!1===this.$emit(Y,{x:t,y:e,width:n,height:i})?this:(this.$changing=!0,this.x=t,this.y=e,this.width=n,this.height=i,this.$changing=!1,this.$render())))}$reset(){const{x:t,y:e,width:n,height:i}=this.$initialSelection;return this.$change(t,e,n,i)}$clear(){return this.$change(0,0,0,0,NaN,!0),this.hidden=!0,this}$render(){return this.$setStyles({transform:`translate(${this.x}px, ${this.y}px)`,width:this.width,height:this.height})}$toCanvas(t){return new Promise(((e,n)=>{if(!this.isConnected)return void n(new Error("The current element is not connected to the DOM."));const i=document.createElement("canvas");let{width:o,height:r}=this,a=1;if(G(t)&&(q(t.width)||q(t.height))&&(({width:o,height:r}=ut({aspectRatio:o/r,width:t.width,height:t.height})),a=o/this.width),i.width=o,i.height=r,!this.$canvas)return void e(i);const s=this.$canvas.querySelector(this.$getTagNameOf(u));s?s.$ready().then((n=>{const c=i.getContext("2d");if(c){const[e,l,h,d,u,p]=s.$getTransform(),f=-this.x,m=-this.y,v=(f*d-h*m)/(e*d-h*l),g=(m*e-l*f)/(e*d-h*l);let y=e*v+h*g+u,w=l*v+d*g+p,b=n.naturalWidth,$=n.naturalHeight;1!==a&&(y*=a,w*=a,b*=a,$*=a);const k=b/2,M=$/2;c.fillStyle="transparent",c.fillRect(0,0,o,r),G(t)&&V(t.beforeDraw)&&t.beforeDraw.call(this,c,i),c.save(),c.translate(k,M),c.transform(e,l,h,d,y,w),c.translate(-k,-M),c.drawImage(n,0,0,b,$),c.restore()}e(i)})).catch(n):e(i)}))}}Et.$name=p,Et.$version="2.0.0";class jt extends bt{constructor(){super(...arguments),this.$style=":host{display:flex;flex-direction:column;position:relative;touch-action:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}:host([bordered]){border:1px dashed var(--theme-color)}:host([covered]){bottom:0;left:0;position:absolute;right:0;top:0}:host>span{display:flex;flex:1}:host>span+span{border-top:1px dashed var(--theme-color)}:host>span>span{flex:1}:host>span>span+span{border-left:1px dashed var(--theme-color)}",this.bordered=!1,this.columns=3,this.covered=!1,this.rows=3,this.slottable=!1,this.themeColor="rgba(238, 238, 238, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["bordered","columns","covered","rows"])}$propertyChangedCallback(t,e,n){Object.is(n,e)||(super.$propertyChangedCallback(t,e,n),"rows"!==t&&"columns"!==t||this.$nextTick((()=>{this.$render()})))}connectedCallback(){super.connectedCallback(),this.$render()}$render(){const t=this.$getShadowRoot(),e=document.createDocumentFragment();for(let t=0;t<this.rows;t+=1){const t=document.createElement("span");t.setAttribute("role","row");for(let e=0;e<this.columns;e+=1){const e=document.createElement("span");e.setAttribute("role","gridcell"),t.appendChild(e)}e.appendChild(t)}t&&(t.innerHTML="",t.appendChild(e))}}jt.$name=h,jt.$version="2.0.0";class Pt extends bt{constructor(){super(...arguments),this.$style=':host{display:inline-block;height:1em;position:relative;touch-action:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;vertical-align:middle;width:1em}:host:after,:host:before{background-color:var(--theme-color);content:"";display:block;position:absolute}:host:before{height:1px;left:0;top:50%;transform:translateY(-50%);width:100%}:host:after{height:100%;left:50%;top:0;transform:translateX(-50%);width:1px}:host([centered]){left:50%;position:absolute;top:50%;transform:translate(-50%,-50%)}',this.centered=!1,this.slottable=!1,this.themeColor="rgba(238, 238, 238, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["centered"])}}Pt.$name=l,Pt.$version="2.0.0";const Tt=new WeakMap,Ot=new WeakMap,zt=new WeakMap,Rt=new WeakMap,It="vertical";class Dt extends bt{constructor(){super(...arguments),this.$onSelectionChange=null,this.$onSourceImageLoad=null,this.$onSourceImageTransform=null,this.$scale=1,this.$style=":host{display:block;height:100%;overflow:hidden;position:relative;width:100%}",this.resize=It,this.selection="",this.slottable=!1}set $image(t){Ot.set(this,t)}get $image(){return Ot.get(this)}set $sourceImage(t){Rt.set(this,t)}get $sourceImage(){return Rt.get(this)}set $canvas(t){Tt.set(this,t)}get $canvas(){return Tt.get(this)}set $selection(t){zt.set(this,t)}get $selection(){return zt.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["resize","selection"])}connectedCallback(){super.connectedCallback();let t=null;if(t=this.selection?this.ownerDocument.querySelector(this.selection):this.closest(this.$getTagNameOf(p)),J(t)){this.$selection=t,this.$onSelectionChange=this.$handleSelectionChange.bind(this),ot(t,Y,this.$onSelectionChange);const e=t.closest(this.$getTagNameOf(c));if(e){this.$canvas=e;const t=e.querySelector(this.$getTagNameOf(u));t&&(this.$sourceImage=t,this.$image=t.cloneNode(!0),this.$getShadowRoot().appendChild(this.$image),this.$onSourceImageLoad=this.$handleSourceImageLoad.bind(this),this.$onSourceImageTransform=this.$handleSourceImageTransform.bind(this),ot(t.$image,I,this.$onSourceImageLoad),ot(t,B,this.$onSourceImageTransform))}this.$render()}}disconnectedCallback(){const{$selection:t,$sourceImage:e}=this;t&&this.$onSelectionChange&&(it(t,Y,this.$onSelectionChange),this.$onSelectionChange=null),e&&this.$onSourceImageLoad&&(it(e.$image,I,this.$onSourceImageLoad),this.$onSourceImageLoad=null),e&&this.$onSourceImageTransform&&(it(e,B,this.$onSourceImageTransform),this.$onSourceImageTransform=null),super.disconnectedCallback()}$handleSelectionChange(t){this.$render(t.detail)}$handleSourceImageLoad(){const{$image:t,$sourceImage:e}=this,n=t.getAttribute("src"),i=e.getAttribute("src");i&&i!==n&&(t.setAttribute("src",i),t.$ready((()=>{setTimeout((()=>{this.$render()}),50)})))}$handleSourceImageTransform(t){this.$render(void 0,t.detail.matrix)}$render(t,e){const{$canvas:n,$selection:i}=this;t||i.hidden||(t=i),(!t||0===t.x&&0===t.y&&0===t.width&&0===t.height)&&(t={x:0,y:0,width:n.offsetWidth,height:n.offsetHeight});const{x:o,y:r,width:a,height:s}=t,c={},{clientWidth:l,clientHeight:h}=this;let d=l,u=h,p=NaN;switch(this.resize){case"both":p=1,d=a,u=s,c.width=a,c.height=s;break;case"horizontal":p=s>0?h/s:0,d=a*p,c.width=d;break;case It:p=a>0?l/a:0,u=s*p,c.height=u;break;default:l>0?p=a>0?l/a:0:h>0&&(p=s>0?h/s:0)}this.$scale=p,this.$setStyles(c),this.$sourceImage&&this.$transformImageByOffset(null!=e?e:this.$sourceImage.$getTransform(),-o,-r)}$transformImageByOffset(t,e,n){const{$image:i,$scale:o,$sourceImage:r}=this;if(r&&i&&o>=0){const[r,a,s,c,l,h]=t,d=(e*c-s*n)/(r*c-s*a),u=(n*r-a*e)/(r*c-s*a),p=r*d+s*u+l,f=a*d+c*u+h;i.$ready((t=>{this.$setStyles.call(i,{width:t.naturalWidth*o,height:t.naturalHeight*o})})),i.$setTransform(r,a,s,c,p*o,f*o)}}}Dt.$name=m,Dt.$version="2.0.0";const Lt=/^img|canvas$/,Nt=/<(\/?(?:script|style)[^>]*)>/gi,Ft={template:'<cropper-canvas background><cropper-image rotatable scalable skewable translatable></cropper-image><cropper-shade hidden></cropper-shade><cropper-handle action="select" plain></cropper-handle><cropper-selection initial-coverage="0.5" movable resizable><cropper-grid role="grid" bordered covered></cropper-grid><cropper-crosshair centered></cropper-crosshair><cropper-handle action="move" theme-color="rgba(255, 255, 255, 0.35)"></cropper-handle><cropper-handle action="n-resize"></cropper-handle><cropper-handle action="e-resize"></cropper-handle><cropper-handle action="s-resize"></cropper-handle><cropper-handle action="w-resize"></cropper-handle><cropper-handle action="ne-resize"></cropper-handle><cropper-handle action="nw-resize"></cropper-handle><cropper-handle action="se-resize"></cropper-handle><cropper-handle action="sw-resize"></cropper-handle></cropper-selection></cropper-canvas>'};$t.$define(),Pt.$define(),jt.$define(),St.$define(),_t.$define(),Et.$define(),xt.$define(),Dt.$define();class Yt{constructor(t,e){if(this.options=Ft,H(t)&&(t=document.querySelector(t)),!J(t)||!Lt.test(t.localName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,e=Object.assign(Object.assign({},Ft),e),this.options=e;const{ownerDocument:n}=t;let{container:i}=e;if(i&&(H(i)&&(i=n.querySelector(i)),!J(i)))throw new Error("The `container` option must be an element or a valid selector.");J(i)||(i=t.parentElement?t.parentElement:n.body),this.container=i;const o=t.localName;let r="";"img"===o?({src:r}=t):"canvas"===o&&window.HTMLCanvasElement&&(r=t.toDataURL());const{template:a}=e;if(a&&H(a)){const e=document.createElement("template"),n=document.createDocumentFragment();e.innerHTML=a.replace(Nt,"&lt;$1&gt;"),n.appendChild(e.content),Array.from(n.querySelectorAll(u)).forEach((e=>{e.setAttribute("src",r),e.setAttribute("alt",t.alt||"The image to crop")})),t.parentElement?(t.style.display="none",i.insertBefore(n,t.nextSibling)):i.appendChild(n)}}getCropperCanvas(){return this.container.querySelector(c)}getCropperImage(){return this.container.querySelector(u)}getCropperSelection(){return this.container.querySelector(p)}getCropperSelections(){return this.container.querySelectorAll(p)}}Yt.version="2.0.0";var Bt=n(46294),Ht=n(70418),Xt=n(91994);function Wt(t){return Wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wt(t)}function qt(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */qt=function(){return e};var t,e={},n=Object.prototype,i=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",s=r.asyncIterator||"@@asyncIterator",c=r.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function h(t,e,n,i){var r=e&&e.prototype instanceof g?e:g,a=Object.create(r.prototype),s=new j(i||[]);return o(a,"_invoke",{value:x(t,n,s)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var u="suspendedStart",p="suspendedYield",f="executing",m="completed",v={};function g(){}function y(){}function w(){}var b={};l(b,a,(function(){return this}));var $=Object.getPrototypeOf,k=$&&$($(P([])));k&&k!==n&&i.call(k,a)&&(b=k);var M=w.prototype=g.prototype=Object.create(b);function _(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function n(o,r,a,s){var c=d(t[o],t,r);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==Wt(h)&&i.call(h,"__await")?e.resolve(h.__await).then((function(t){n("next",t,a,s)}),(function(t){n("throw",t,a,s)})):e.resolve(h).then((function(t){l.value=t,a(l)}),(function(t){return n("throw",t,a,s)}))}s(c.arg)}var r;o(this,"_invoke",{value:function(t,i){function o(){return new e((function(e,o){n(t,i,e,o)}))}return r=r?r.then(o,o):o()}})}function x(e,n,i){var o=u;return function(r,a){if(o===f)throw Error("Generator is already running");if(o===m){if("throw"===r)throw a;return{value:t,done:!0}}for(i.method=r,i.arg=a;;){var s=i.delegate;if(s){var c=S(s,i);if(c){if(c===v)continue;return c}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(o===u)throw o=m,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);o=f;var l=d(e,n,i);if("normal"===l.type){if(o=i.done?m:p,l.arg===v)continue;return{value:l.arg,done:i.done}}"throw"===l.type&&(o=m,i.method="throw",i.arg=l.arg)}}}function S(e,n){var i=n.method,o=e.iterator[i];if(o===t)return n.delegate=null,"throw"===i&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==i&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+i+"' method")),v;var r=d(o,e.iterator,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,v;var a=r.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function P(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function n(){for(;++o<e.length;)if(i.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return r.next=r}}throw new TypeError(Wt(e)+" is not iterable")}return y.prototype=w,o(M,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:y,configurable:!0}),y.displayName=l(w,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,l(t,c,"GeneratorFunction")),t.prototype=Object.create(M),t},e.awrap=function(t){return{__await:t}},_(C.prototype),l(C.prototype,s,(function(){return this})),e.AsyncIterator=C,e.async=function(t,n,i,o,r){void 0===r&&(r=Promise);var a=new C(h(t,n,i,o),r);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(M),l(M,c,"Generator"),l(M,a,(function(){return this})),l(M,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var i in e)n.push(i);return n.reverse(),function t(){for(;n.length;){var i=n.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},e.values=P,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var n in this)"t"===n.charAt(0)&&i.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(i,o){return s.type="throw",s.arg=e,n.next=i,o&&(n.method="next",n.arg=t),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=i.call(a,"catchLoc"),l=i.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var r=o;break}}r&&("break"===t||"continue"===t)&&r.tryLoc<=e&&e<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=t,a.arg=e,r?(this.method="next",this.next=r.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var i=n.completion;if("throw"===i.type){var o=i.arg;E(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,i){return this.delegate={iterator:P(e),resultName:n,nextLoc:i},"next"===this.method&&(this.arg=t),v}},e}function Ut(t,e,n,i,o,r,a){try{var s=t[r](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(i,o)}function Kt(t){return function(){var e=this,n=arguments;return new Promise((function(i,o){var r=t.apply(e,n);function a(t){Ut(r,i,o,a,s,"next",t)}function s(t){Ut(r,i,o,a,s,"throw",t)}a(void 0)}))}}function Gt(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,Vt(i.key),i)}}function Vt(t){var e=function(t,e){if("object"!=Wt(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=Wt(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Wt(e)?e:e+""}var Jt=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return e=t,n=null,i=[{key:"handleDropdown",value:function(){var e=Ht.M.size(Ht.M.getSelectedItems());t.renderActions(),e>0?$(".rv-dropdown-actions > .dropdown-toggle").removeClass("disabled").prop("disabled",!1):$(".rv-dropdown-actions > .dropdown-toggle").addClass("disabled").prop("disabled",!0)}},{key:"handlePreview",value:function(){var t=[];Ht.M.each(Ht.M.getSelectedFiles(),(function(e){if(e.preview_url){if("document"===e.type){var n=document.createElement("iframe");n.src=e.preview_url,n.allowFullscreen=!0,n.style.width="100vh",n.style.height="100vh",t.push(n)}else t.push(e.preview_url);Bt.y.push(e.id)}})),Ht.M.size(t)>0?(Botble.lightbox(t),Ht.M.storeRecentItems()):this.handleGlobalAction("download")}},{key:"renderCropImage",value:function(){var t,e=$("#rv_media_crop_image").html(),n=$("#modal_crop_image .crop-image").empty(),i=Ht.M.getSelectedItems()[0],o=$("#modal_crop_image .form-crop"),r=e.replace(/__src__/gi,i.full_url);n.append(r);var a=n.find("img")[0],s={minContainerWidth:500,minContainerHeight:550,dragMode:"move",crop:function(e){t=e.detail,o.find('input[name="image_id"]').val(i.id),o.find('input[name="crop_data"]').val(JSON.stringify(t)),l(t.height),h(t.width)}},c=new Yt(a,s);o.find("#aspectRatio").on("click",(function(){c.destroy(),$(this).is(":checked")?s.aspectRatio=t.width/t.height:s.aspectRatio=null,c=new Yt(a,s)})),o.find("#dataHeight").on("change",(function(){t.height=parseFloat($(this).val()),c.setData(t),l(t.height)})),o.find("#dataWidth").on("change",(function(){t.width=parseFloat($(this).val()),c.setData(t),h(t.width)}));var l=function(t){o.find("#dataHeight").val(parseInt(t))},h=function(t){o.find("#dataWidth").val(parseInt(t))}}},{key:"handleCopyLink",value:(r=Kt(qt().mark((function t(){var e;return qt().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e="",Ht.M.each(Ht.M.getSelectedFiles(),(function(t){Ht.M.isEmpty(e)||(e+="\n"),e+=t.full_url})),t.next=4,Botble.copyToClipboard(e);case 4:Xt.b.showMessage("success",Ht.M.trans("clipboard.success"),Ht.M.trans("message.success_header"));case 5:case"end":return t.stop()}}),t)}))),function(){return r.apply(this,arguments)})},{key:"handleCopyIndirectLink",value:(o=Kt(qt().mark((function t(){var e;return qt().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e="",Ht.M.each(Ht.M.getSelectedFiles(),(function(t){Ht.M.isEmpty(e)||(e+="\n"),e+=t.indirect_url})),t.next=4,Botble.copyToClipboard(e);case 4:Xt.b.showMessage("success",Ht.M.trans("clipboard.success"),Ht.M.trans("message.success_header"));case 5:case"end":return t.stop()}}),t)}))),function(){return o.apply(this,arguments)})},{key:"handleShare",value:function(){$("#modal_share_items").modal("show").find("form.form-alt-text").data("action",type)}},{key:"handleGlobalAction",value:function(e,n){var i=[];switch(Ht.M.each(Ht.M.getSelectedItems(),(function(t){i.push({is_folder:t.is_folder,id:t.id,full_url:t.full_url})})),e){case"rename":$("#modal_rename_items").modal("show").find("form.form-rename").data("action",e);break;case"copy_link":t.handleCopyLink().then((function(){}));break;case"copy_indirect_link":t.handleCopyIndirectLink().then((function(){}));break;case"share":$("#modal_share_items").modal("show");break;case"preview":t.handlePreview();break;case"alt_text":$("#modal_alt_text_items").modal("show").find("form.form-alt-text").data("action",e);break;case"crop":$("#modal_crop_image").modal("show").find("form.rv-form").data("action",e);break;case"trash":$("#modal_trash_items").modal("show").find("form.form-delete-items").data("action",e);break;case"delete":$("#modal_delete_items").modal("show").find("form.form-delete-items").data("action",e);break;case"empty_trash":$("#modal_empty_trash").modal("show").find("form.form-empty-trash").data("action",e);break;case"download":var o=[];Ht.M.each(Ht.M.getSelectedItems(),(function(t){Ht.M.inArray(Ht.M.getConfigs().denied_download,t.mime_type)||o.push({id:t.id,is_folder:t.is_folder})})),o.length?t.handleDownload(o):Xt.b.showMessage("error",Ht.M.trans("download.error"),Ht.M.trans("message.error_header"));break;case"properties":$("#modal-properties").modal("show");break;default:t.processAction({selected:i,action:e},n)}}},{key:"processAction",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;Ht.M.showAjaxLoading(),$httpClient.make().post(RV_MEDIA_URL.global_actions,t).then((function(t){var n=t.data;Ht.M.resetPagination(),Xt.b.showMessage("success",n.message,Ht.M.trans("message.success_header")),e&&e(n)})).catch((function(t){var n=t.response;return e&&e(n.data)})).finally((function(){return Ht.M.hideAjaxLoading()}))}},{key:"renderRenameItems",value:function(){var t=$("#rv_media_rename_item").html(),e=$("#modal_rename_items .rename-items").empty();Ht.M.each(Ht.M.getSelectedItems(),(function(n){var i=t.replace(/__icon__/gi,n.icon||'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n                </svg>').replace(/__placeholder__/gi,"Input file name").replace(/__value__/gi,n.name),o=$(i);o.data("id",n.id.toString()),o.data("is_folder",n.is_folder),o.data("name",n.name);var r=o.find('input[name="rename_physical_file"]');r.closest(".form-check").find("span").text(n.is_folder?r.data("folder-label"):r.data("file-label")),o.find('input[name="rename_physical_file"]').on("change",(function(){o.data("rename_physical_file",$(this).is(":checked"))})),e.append(o),Botble.initFieldCollapse()}))}},{key:"renderAltTextItems",value:function(){var t=$("#rv_media_alt_text_item").html(),e=$("#modal_alt_text_items .alt-text-items").empty();Ht.M.each(Ht.M.getSelectedItems(),(function(n){var i=t.replace(/__icon__/gi,n.icon||'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n                </svg>').replace(/__placeholder__/gi,"Input file alt").replace(/__value__/gi,null===n.alt?"":n.alt),o=$(i);o.data("id",n.id),o.data("alt",n.alt),e.append(o)}))}},{key:"renderShareItems",value:function(){var t=$('#modal_share_items [data-bb-value="share-result"]'),e=$('#modal_share_items select[data-bb-value="share-type"]').val();t.val("");var n=[];Ht.M.each(Ht.M.getSelectedItems(),(function(t){switch(e){case"html":n.push("image"===t.type?'<img src="'.concat(t.full_url,'" alt="').concat(t.alt,'" />'):'<a href="'.concat(t.full_url,'" target="_blank">').concat(t.alt,"</a>"));break;case"markdown":n.push(("image"===t.type?"!":"")+"[".concat(t.alt,"](").concat(t.full_url,")"));break;case"indirect_url":n.push(t.indirect_url);break;default:n.push(t.full_url)}})),t.val(n.join("\n"))}},{key:"renderActions",value:function(){var t=Ht.M.getSelectedFolder().length>0,e=$("#rv_action_item").html(),n=0,i=$(".rv-dropdown-actions .dropdown-menu");i.empty();var o=$.extend({},!0,Ht.M.getConfigs().actions_list);if(t){var r=["preview","crop","alt_text","copy_link","copy_direct_link","share"];o.basic=Ht.M.arrayReject(o.basic,(function(t){return r.includes(t.action)})),Ht.M.hasPermission("folders.create")||(o.file=Ht.M.arrayReject(o.file,(function(t){return"make_copy"===t.action}))),Ht.M.hasPermission("folders.edit")||(o.file=Ht.M.arrayReject(o.file,(function(t){return Ht.M.inArray(["rename"],t.action)})),o.user=Ht.M.arrayReject(o.user,(function(t){return Ht.M.inArray(["rename"],t.action)}))),Ht.M.hasPermission("folders.trash")||(o.other=Ht.M.arrayReject(o.other,(function(t){return Ht.M.inArray(["trash","restore"],t.action)}))),Ht.M.hasPermission("folders.destroy")||(o.other=Ht.M.arrayReject(o.other,(function(t){return Ht.M.inArray(["delete"],t.action)}))),Ht.M.hasPermission("folders.favorite")||(o.other=Ht.M.arrayReject(o.other,(function(t){return Ht.M.inArray(["favorite","remove_favorite"],t.action)})))}var a=Ht.M.getSelectedFiles();Ht.M.arrayFilter(a,(function(t){return t.preview_url})).length||(o.basic=Ht.M.arrayReject(o.basic,(function(t){return"preview"===t.action}))),Ht.M.arrayFilter(a,(function(t){return"image"===t.type})).length||(o.basic=Ht.M.arrayReject(o.basic,(function(t){return"crop"===t.action})),o.file=Ht.M.arrayReject(o.file,(function(t){return"alt_text"===t.action}))),a.length>0&&(Ht.M.hasPermission("files.create")||(o.file=Ht.M.arrayReject(o.file,(function(t){return"make_copy"===t.action}))),Ht.M.hasPermission("files.edit")||(o.file=Ht.M.arrayReject(o.file,(function(t){return Ht.M.inArray(["rename"],t.action)}))),Ht.M.hasPermission("files.trash")||(o.other=Ht.M.arrayReject(o.other,(function(t){return Ht.M.inArray(["trash","restore"],t.action)}))),Ht.M.hasPermission("files.destroy")||(o.other=Ht.M.arrayReject(o.other,(function(t){return Ht.M.inArray(["delete"],t.action)}))),Ht.M.hasPermission("files.favorite")||(o.other=Ht.M.arrayReject(o.other,(function(t){return Ht.M.inArray(["favorite","remove_favorite"],t.action)}))),a.length>1&&(o.basic=Ht.M.arrayReject(o.basic,(function(t){return"crop"===t.action})))),(!Ht.M.hasPermission("folders.edit")||a.length>0)&&(o.other=Ht.M.arrayReject(o.other,(function(t){return Ht.M.inArray(["properties"],t.action)}))),Ht.M.each(o,(function(t,o){Ht.M.each(t,(function(t,r){var a=!1;switch(Ht.M.getRequestParams().view_in){case"all_media":Ht.M.inArray(["remove_favorite","delete","restore"],t.action)&&(a=!0);break;case"recent":Ht.M.inArray(["remove_favorite","delete","restore","make_copy"],t.action)&&(a=!0);break;case"favorites":Ht.M.inArray(["favorite","delete","restore","make_copy"],t.action)&&(a=!0);break;case"trash":Ht.M.inArray(["preview","delete","restore","rename","download"],t.action)||(a=!0)}if(!a){var s=e.replace(/__action__/gi,t.action||"").replace('<i class="__icon__ dropdown-item-icon dropdown-item-icon"></i>','<span class="icon-tabler-wrapper dropdown-item-icon">__icon__</span>').replace("__icon__",'<span class="icon-tabler-wrapper dropdown-item-icon">__icon__</span>').replace("__icon__",t.icon||"").replace(/__name__/gi,Ht.M.trans("actions_list.".concat(o,".").concat(t.action))||t.name);t.icon&&(s=s.replace("media-icon","media-icon dropdown-item-icon")),!r&&n&&(s='<li role="separator" class="divider"></li>'.concat(s)),i.append(s)}})),t.length>0&&n++}))}},{key:"handleDownload",value:function(t){var e=$(".media-download-popup");e.show(),$httpClient.make().withResponseType("blob").post(RV_MEDIA_URL.download,{selected:t}).then((function(t){var e=(t.headers["content-disposition"]||"").split("filename=")[1].split(";")[0],n=URL.createObjectURL(t.data),i=document.createElement("a");i.href=n,i.download=e,document.body.appendChild(i),i.click(),i.remove(),URL.revokeObjectURL(n)})).finally((function(){e.hide(),clearTimeout(null)}))}}],n&&Gt(e.prototype,n),i&&Gt(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,i,o,r}()},46294:(t,e,n)=>{n.d(e,{T:()=>i,y:()=>r});var i=$.parseJSON(localStorage.getItem("MediaConfig"))||{},o={app_key:RV_MEDIA_CONFIG.random_hash?RV_MEDIA_CONFIG.random_hash:"21d06709fe1d3abdf0e35ddda89c4b282",request_params:{view_type:"tiles",filter:"everything",view_in:"all_media",sort_by:"created_at-desc",folder_id:0},hide_details_pane:!1,icons:{folder:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n            <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n        </svg>'},actions_list:{basic:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>\n                    <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>\n                </svg>',name:"Preview",action:"preview",order:0,class:"rv-action-preview"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M8 5v10a1 1 0 0 0 1 1h10"></path>\n                    <path d="M5 8h10a1 1 0 0 1 1 1v10"></path>\n                </svg>',name:"Crop",action:"crop",order:1,class:"rv-action-crop"}],file:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"></path>\n                    <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"></path>\n                    <path d="M16 5l3 3"></path>\n                </svg>',name:"Rename",action:"rename",order:0,class:"rv-action-rename"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"></path>\n                    <path d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"></path>\n                </svg>',name:"Make a copy",action:"make_copy",order:1,class:"rv-action-make-copy"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M15 8h.01"></path>\n                    <path d="M11 20h-4a3 3 0 0 1 -3 -3v-10a3 3 0 0 1 3 -3h10a3 3 0 0 1 3 3v4"></path>\n                    <path d="M4 15l4 -4c.928 -.893 2.072 -.893 3 0l3 3"></path>\n                    <path d="M14 14l1 -1c.31 -.298 .644 -.497 .987 -.596"></path>\n                    <path d="M18.42 15.61a2.1 2.1 0 0 1 2.97 2.97l-3.39 3.42h-3v-3l3.42 -3.39z"></path>\n                </svg>',name:"Alt text",action:"alt_text",order:2,class:"rv-action-alt-text"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M9 15l6 -6"></path>\n                    <path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464"></path>\n                    <path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463"></path>\n                </svg>',name:"Copy link",action:"copy_link",order:3,class:"rv-action-copy-link"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M9 15l6 -6"></path>\n                    <path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464"></path>\n                    <path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463"></path>\n                </svg>',name:"Copy indirect link",action:"copy_indirect_link",order:4,class:"rv-action-copy-indirect-link"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n                  <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                  <path d="M6 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>\n                  <path d="M18 6m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>\n                  <path d="M18 18m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>\n                  <path d="M8.7 10.7l6.6 -3.4"></path>\n                  <path d="M8.7 13.3l6.6 3.4"></path>\n                </svg>',name:"Share",action:"share",order:5,class:"rv-action-share"}],user:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z"></path>\n                </svg>',name:"Favorite",action:"favorite",order:2,class:"rv-action-favorite"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z"></path>\n                </svg>',name:"Remove favorite",action:"remove_favorite",order:3,class:"rv-action-favorite"}],other:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>\n                    <path d="M7 11l5 5l5 -5"></path>\n                    <path d="M12 4l0 12"></path>\n                </svg>',name:"Download",action:"download",order:0,class:"rv-action-download"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M4 7l16 0"></path>\n                    <path d="M10 11l0 6"></path>\n                    <path d="M14 11l0 6"></path>\n                    <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"></path>\n                    <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"></path>\n                </svg>',name:"Move to trash",action:"trash",order:1,class:"rv-action-trash"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M19 20h-10.5l-4.21 -4.3a1 1 0 0 1 0 -1.41l10 -10a1 1 0 0 1 1.41 0l5 5a1 1 0 0 1 0 1.41l-9.2 9.3"></path>\n                    <path d="M18 13.3l-6.3 -6.3"></path>\n                </svg>',name:"Delete permanently",action:"delete",order:2,class:"rv-action-delete"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M9 11l-4 4l4 4m-4 -4h11a4 4 0 0 0 0 -8h-1"></path>\n                </svg>',name:"Restore",action:"restore",order:3,class:"rv-action-restore"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-palette" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 21a9 9 0 0 1 0 -18c4.97 0 9 3.582 9 8c0 1.06 -.474 2.078 -1.318 2.828c-.844 .75 -1.989 1.172 -3.182 1.172h-2.5a2 2 0 0 0 -1 3.75a1.3 1.3 0 0 1 -1 2.25" /><path d="M8.5 10.5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" /><path d="M12.5 7.5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" /><path d="M16.5 10.5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" /></svg>',name:"Properties",action:"properties",order:4,class:"rv-action-properties"}]}};i.app_key&&i.app_key===o.app_key||(i=o),i.request_params.search="";var r=$.parseJSON(localStorage.getItem("RecentItems"))||[]},70418:(t,e,n)=>{n.d(e,{M:()=>h});var i=n(46294);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach((function(e){s(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function s(t,e,n){return(e=l(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function c(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,l(i.key),i)}}function l(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}var h=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return e=t,o=[{key:"getUrlParam",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;e||(e=window.location.search);var n=new RegExp("(?:[?&]|&)"+t+"=([^&]+)","i"),i=e.match(n);return i&&i.length>1?i[1]:null}},{key:"asset",value:function(t){if("//"===t.substring(0,2)||"http://"===t.substring(0,7)||"https://"===t.substring(0,8))return t;var e="/"!==RV_MEDIA_URL.base_url.substr(-1,1)?RV_MEDIA_URL.base_url+"/":RV_MEDIA_URL.base_url;return"/"===t.substring(0,1)?e+t.substring(1):e+t}},{key:"showAjaxLoading",value:function(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".rv-media-main")).addClass("on-loading").append($("#rv_media_loading").html())}},{key:"hideAjaxLoading",value:function(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".rv-media-main")).removeClass("on-loading").find(".loading-spinner").remove()}},{key:"isOnAjaxLoading",value:function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".rv-media-items")).hasClass("on-loading")}},{key:"jsonEncode",value:function(t){return void 0===t&&(t=null),JSON.stringify(t)}},{key:"jsonDecode",value:function(t,e){if(!t)return e;if("string"==typeof t){var n;try{n=$.parseJSON(t)}catch(t){n=e}return n}return t}},{key:"getRequestParams",value:function(){return window.rvMedia.options&&"modal"===window.rvMedia.options.open_in?a(a({},i.T.request_params),window.rvMedia.options):i.T.request_params}},{key:"setSelectedFile",value:function(t){void 0!==window.rvMedia.options?window.rvMedia.options.selected_file_id=t:i.T.request_params.selected_file_id=t}},{key:"getConfigs",value:function(){return i.T}},{key:"storeConfig",value:function(){localStorage.setItem("MediaConfig",t.jsonEncode(i.T))}},{key:"storeRecentItems",value:function(){localStorage.setItem("RecentItems",t.jsonEncode(i.y))}},{key:"addToRecent",value:function(e){e instanceof Array?t.each(e,(function(t){i.y.push(t)})):(i.y.push(e),this.storeRecentItems())}},{key:"getItems",value:function(){var t=[];return $(".js-media-list-title").each((function(e,n){var i=$(n),o=i.data()||{};o.index_key=i.index(),t.push(o)})),t}},{key:"getSelectedItems",value:function(){var t=[];return $(".js-media-list-title input[type=checkbox]:checked").each((function(e,n){var i=$(n).closest(".js-media-list-title"),o=i.data()||{};o.index_key=i.index(),t.push(o)})),t}},{key:"getSelectedFiles",value:function(){var t=[];return $(".js-media-list-title[data-context=file] input[type=checkbox]:checked").each((function(e,n){var i=$(n).closest(".js-media-list-title"),o=i.data()||{};o.index_key=i.index(),t.push(o)})),t}},{key:"getSelectedFolder",value:function(){var t=[];return $(".js-media-list-title[data-context=folder] input[type=checkbox]:checked").each((function(e,n){var i=$(n).closest(".js-media-list-title"),o=i.data()||{};o.index_key=i.index(),t.push(o)})),t}},{key:"isUseInModal",value:function(){return window.rvMedia&&window.rvMedia.options&&"modal"===window.rvMedia.options.open_in}},{key:"resetPagination",value:function(){RV_MEDIA_CONFIG.pagination={paged:1,posts_per_page:40,in_process_get_media:!1,has_more:!0}}},{key:"trans",value:function(t){return _.get(RV_MEDIA_CONFIG.translations,t,t)}},{key:"config",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return _.get(RV_MEDIA_CONFIG,t,e)}},{key:"hasPermission",value:function(e){return t.inArray(t.config("permissions",[]),e)}},{key:"inArray",value:function(t,e){return _.includes(t,e)}},{key:"each",value:function(t,e){return _.each(t,e)}},{key:"forEach",value:function(t,e){return _.forEach(t,e)}},{key:"arrayReject",value:function(t,e){return _.reject(t,e)}},{key:"arrayFilter",value:function(t,e){return _.filter(t,e)}},{key:"arrayFirst",value:function(t){return _.first(t)}},{key:"isArray",value:function(t){return _.isArray(t)}},{key:"isEmpty",value:function(t){return _.isEmpty(t)}},{key:"size",value:function(t){return _.size(t)}}],(n=null)&&c(e.prototype,n),o&&c(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,o}()},91994:(t,e,n)=>{function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,r(i.key),i)}}function r(t){var e=function(t,e){if("object"!=i(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!=i(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==i(e)?e:e+""}n.d(e,{b:()=>a});var a=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)},n=[{key:"showMessage",value:function(t,e){Botble.showNotice(t,e)}}],(e=null)&&o(t.prototype,e),n&&o(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,n}()}},e={};function n(i){var o=e[i];if(void 0!==o)return o.exports;var r=e[i]={exports:{}};return t[i](r,r.exports,n),r.exports}n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var i=n(70418),o=n(46294),r=n(27);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function c(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,h(i.key),i)}}function l(t,e,n){return e&&c(t.prototype,e),n&&c(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function h(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=a(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}var d=function(){return l((function t(){s(this,t)}),null,[{key:"editorSelectFile",value:function(t){var e=i.M.getUrlParam("CKEditor")||i.M.getUrlParam("CKEditorFuncNum");if(window.opener&&e){var n=i.M.arrayFirst(t);window.opener.CKEDITOR.tools.callFunction(i.M.getUrlParam("CKEditorFuncNum"),n.full_url),window.opener&&window.close()}}}])}(),u=l((function t(e,n){s(this,t);var a=window.RvMediaCustomCallback||null;if("function"!=typeof a){window.rvMedia=window.rvMedia||{};var c=$("body");n=$.extend(!0,{multiple:!0,type:"*",onSelectFiles:function(t,e){}},n);var l=function(t){t.preventDefault();var e=$(t.currentTarget);$("#rv_media_modal").modal("show"),window.rvMedia.options=n,window.rvMedia.options.open_in="modal",window.rvMedia.$el=e,o.T.request_params.filter="everything",i.M.storeConfig();var a=window.rvMedia.$el.data("rv-media");void 0!==a&&a.length>0&&(a=a[0],window.rvMedia.options=$.extend(!0,window.rvMedia.options,a||{}),void 0!==a.selected_file_id?window.rvMedia.options.is_popup=!0:void 0!==window.rvMedia.options.is_popup&&(window.rvMedia.options.is_popup=void 0)),0===$("#rv_media_body .rv-media-container").length?$("#rv_media_body").load(RV_MEDIA_URL.popup,(function(t){t.error&&alert(t.message),$("#rv_media_body").removeClass("media-modal-loading").closest(".modal-content").removeClass("bb-loading"),$(document).find(".rv-media-container .js-change-action[data-type=refresh]").trigger("click"),"everything"!==i.M.getRequestParams().filter&&$(".rv-media-actions .btn.js-rv-media-change-filter-group.js-filter-by-type").hide(),r.K.destroyContext(),r.K.initContext()})):$(document).find(".rv-media-container .js-change-action[data-type=refresh]").trigger("click")};"string"==typeof e?c.off("click",e).on("click",e,l):e.off("click").on("click",l)}else a(e,n)}));window.RvMediaStandAlone=u,$(".js-insert-to-editor").off("click").on("click",(function(t){t.preventDefault();var e=i.M.getSelectedFiles();i.M.size(e)>0&&d.editorSelectFile(e)})),$.fn.rvMedia=function(t){var e=$(this);o.T.request_params.filter="everything",$(document).find(".js-insert-to-editor").prop("disabled","trash"===o.T.request_params.view_in),i.M.storeConfig();var n=window.RvMediaCustomCallback||null;"function"!=typeof n?new u(e,t):n(e,t)},document.dispatchEvent(new CustomEvent("core-media-loaded"))})();