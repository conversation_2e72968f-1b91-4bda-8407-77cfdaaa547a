<?php

namespace Bo<PERSON>ble\Ecommerce\Services\Products;

use Bo<PERSON>ble\Ecommerce\Models\Product;
use Botble\Ecommerce\Facades\Discount as DiscountFacade;
use Botble\Ecommerce\Enums\DiscountTypeOptionEnum;
use Closure;

class ProductDiscountPriceService extends ProductPriceHandlerService
{
    public function handle(Product $product, Closure $next)
    {
        $finalPrice = $product->getFinalPrice();

        // الحصول على الخصم بناءً على السعر الحالي وليس السعر الأساسي
        $promotion = DiscountFacade::getFacadeRoot()
            ->promotionForProduct([$product->getKey(), $product->original_product->id ?? null]);

        if (!$promotion) {
            return $next($product);
        }

        // حساب الخصم على السعر الحالي وليس السعر الأساسي
        $discountedPrice = $finalPrice;
        switch ($promotion->type_option) {
            case DiscountTypeOptionEnum::SAME_PRICE:
                $discountedPrice = $promotion->value;
                break;
            case DiscountTypeOptionEnum::AMOUNT:
                $discountedPrice = $finalPrice - $promotion->value;
                if ($discountedPrice < 0) {
                    $discountedPrice = 0;
                }
                break;
            case DiscountTypeOptionEnum::PERCENTAGE:
                $discountedPrice = $finalPrice - ($finalPrice * $promotion->value / 100);
                if ($discountedPrice < 0) {
                    $discountedPrice = 0;
                }
                break;
        }

        if ($discountedPrice < $finalPrice) {
            $product->setFinalPrice($discountedPrice);
        }

        return $next($product);
    }
}
