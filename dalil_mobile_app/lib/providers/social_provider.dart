import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../features/home/<USER>/product_model.dart';

class ShareData {
  final String id;
  final String type;
  final String title;
  final String description;
  final String? imageUrl;
  final Map<String, dynamic>? metadata;
  final DateTime sharedAt;

  ShareData({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    this.imageUrl,
    this.metadata,
    required this.sharedAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'title': title,
    'description': description,
    'imageUrl': imageUrl,
    'metadata': metadata,
    'sharedAt': sharedAt.toIso8601String(),
  };

  factory ShareData.fromJson(Map<String, dynamic> json) => ShareData(
    id: json['id'],
    type: json['type'],
    title: json['title'],
    description: json['description'],
    imageUrl: json['imageUrl'],
    metadata: json['metadata'],
    sharedAt: DateTime.parse(json['sharedAt']),
  );
}

class ReferralData {
  final String code;
  final int usageCount;
  final double totalEarnings;
  final DateTime createdAt;
  final List<String> usedBy;

  ReferralData({
    required this.code,
    this.usageCount = 0,
    this.totalEarnings = 0.0,
    required this.createdAt,
    this.usedBy = const [],
  });

  Map<String, dynamic> toJson() => {
    'code': code,
    'usageCount': usageCount,
    'totalEarnings': totalEarnings,
    'createdAt': createdAt.toIso8601String(),
    'usedBy': usedBy,
  };

  factory ReferralData.fromJson(Map<String, dynamic> json) => ReferralData(
    code: json['code'],
    usageCount: json['usageCount'] ?? 0,
    totalEarnings: json['totalEarnings']?.toDouble() ?? 0.0,
    createdAt: DateTime.parse(json['createdAt']),
    usedBy: List<String>.from(json['usedBy'] ?? []),
  );
}

class SocialProvider extends ChangeNotifier {
  static const String _shareHistoryKey = 'share_history';
  static const String _referralKey = 'referral_data';
  
  final List<ShareData> _shareHistory = [];
  ReferralData? _referralData;
  bool _isLoading = false;

  List<ShareData> get shareHistory => List.unmodifiable(_shareHistory);
  ReferralData? get referralData => _referralData;
  bool get isLoading => _isLoading;
  bool get hasReferralCode => _referralData != null;

  // تحميل البيانات
  Future<void> loadSocialData() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _loadShareHistory();
      await _loadReferralData();
    } catch (e) {
      debugPrint('Error loading social data: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل تاريخ المشاركة
  Future<void> _loadShareHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getStringList(_shareHistoryKey) ?? [];
      
      _shareHistory.clear();
      _shareHistory.addAll(
        historyJson.map((json) => ShareData.fromJson(jsonDecode(json))),
      );
      
      // ترتيب حسب التاريخ (الأحدث أولاً)
      _shareHistory.sort((a, b) => b.sharedAt.compareTo(a.sharedAt));
    } catch (e) {
      debugPrint('Error loading share history: $e');
    }
  }

  // حفظ تاريخ المشاركة
  Future<void> _saveShareHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = _shareHistory
          .map((share) => json.encode(share.toJson()))
          .toList();
      
      await prefs.setStringList(_shareHistoryKey, historyJson);
    } catch (e) {
      debugPrint('Error saving share history: $e');
    }
  }

  // تحميل بيانات الإحالة
  Future<void> _loadReferralData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final referralJson = prefs.getString(_referralKey);
      
      if (referralJson != null) {
        _referralData = ReferralData.fromJson(json.decode(referralJson));
      }
    } catch (e) {
      debugPrint('Error loading referral data: $e');
    }
  }

  // حفظ بيانات الإحالة
  Future<void> _saveReferralData() async {
    try {
      if (_referralData != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_referralKey, json.encode(_referralData!.toJson()));
      }
    } catch (e) {
      debugPrint('Error saving referral data: $e');
    }
  }

  // مشاركة منتج
  Future<bool> shareProduct(ProductModel product, String platform) async {
    try {
      final shareData = ShareData(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: 'product',
        title: product.name,
        description: 'اكتشف هذا المنتج الرائع في تطبيق دليل لقطع غيار السيارات',
        imageUrl: product.imageUrl.isNotEmpty ? product.imageUrl : null,
        metadata: {
          'productId': product.id,
          'price': product.price,
          'platform': platform,
        },
        sharedAt: DateTime.now(),
      );

      await _addToShareHistory(shareData);
      await _performShare(shareData, platform);
      
      return true;
    } catch (e) {
      debugPrint('Error sharing product: $e');
      return false;
    }
  }

  // مشاركة التطبيق
  Future<bool> shareApp(String platform) async {
    try {
      final shareData = ShareData(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: 'app',
        title: 'تطبيق دليل لقطع غيار السيارات',
        description: 'اكتشف أفضل تطبيق لقطع غيار السيارات مع أسعار منافسة وجودة عالية',
        metadata: {
          'platform': platform,
          'referralCode': _referralData?.code,
        },
        sharedAt: DateTime.now(),
      );

      await _addToShareHistory(shareData);
      await _performShare(shareData, platform);
      
      return true;
    } catch (e) {
      debugPrint('Error sharing app: $e');
      return false;
    }
  }

  // مشاركة عرض خاص
  Future<bool> shareOffer(String title, String description, String platform) async {
    try {
      final shareData = ShareData(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: 'offer',
        title: title,
        description: description,
        metadata: {
          'platform': platform,
        },
        sharedAt: DateTime.now(),
      );

      await _addToShareHistory(shareData);
      await _performShare(shareData, platform);
      
      return true;
    } catch (e) {
      debugPrint('Error sharing offer: $e');
      return false;
    }
  }

  // إضافة للتاريخ
  Future<void> _addToShareHistory(ShareData shareData) async {
    _shareHistory.insert(0, shareData);
    
    // الاحتفاظ بآخر 50 مشاركة فقط
    if (_shareHistory.length > 50) {
      _shareHistory.removeRange(50, _shareHistory.length);
    }
    
    await _saveShareHistory();
    notifyListeners();
  }

  // تنفيذ المشاركة
  Future<void> _performShare(ShareData shareData, String platform) async {
    String shareText = _buildShareText(shareData);
    
    switch (platform) {
      case 'copy':
        await _copyToClipboard(shareText);
        break;
      case 'whatsapp':
        await _shareToWhatsApp(shareText);
        break;
      case 'telegram':
        await _shareToTelegram(shareText);
        break;
      case 'twitter':
        await _shareToTwitter(shareText);
        break;
      case 'facebook':
        await _shareToFacebook(shareText);
        break;
      default:
        await _copyToClipboard(shareText);
    }
  }

  // بناء نص المشاركة
  String _buildShareText(ShareData shareData) {
    String text = '${shareData.title}\n\n${shareData.description}';
    
    if (shareData.type == 'product' && shareData.metadata?['price'] != null) {
      text += '\n\nالسعر: ${shareData.metadata!['price']} ريال';
    }
    
    if (_referralData != null) {
      text += '\n\nاستخدم كود الإحالة: ${_referralData!.code} للحصول على خصم خاص!';
    }
    
    text += '\n\nحمل التطبيق الآن: https://dalil-app.com';
    
    return text;
  }

  // نسخ للحافظة
  Future<void> _copyToClipboard(String text) async {
    await Clipboard.setData(ClipboardData(text: text));
  }

  // مشاركة على واتساب
  Future<void> _shareToWhatsApp(String text) async {
    // يمكن استخدام url_launcher أو share plugin
    await _copyToClipboard(text);
  }

  // مشاركة على تيليجرام
  Future<void> _shareToTelegram(String text) async {
    await _copyToClipboard(text);
  }

  // مشاركة على تويتر
  Future<void> _shareToTwitter(String text) async {
    await _copyToClipboard(text);
  }

  // مشاركة على فيسبوك
  Future<void> _shareToFacebook(String text) async {
    await _copyToClipboard(text);
  }

  // إنشاء كود إحالة
  Future<String> generateReferralCode() async {
    if (_referralData != null) {
      return _referralData!.code;
    }
    
    // إنشاء كود عشوائي
    final code = _generateRandomCode();
    
    _referralData = ReferralData(
      code: code,
      createdAt: DateTime.now(),
    );
    
    await _saveReferralData();
    notifyListeners();
    
    return code;
  }

  // توليد كود عشوائي
  String _generateRandomCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    String code = '';
    
    for (int i = 0; i < 6; i++) {
      code += chars[(random + i) % chars.length];
    }
    
    return 'DALIL$code';
  }

  // استخدام كود إحالة
  Future<bool> useReferralCode(String code, String userId) async {
    if (_referralData?.code == code) {
      final updatedUsedBy = List<String>.from(_referralData!.usedBy);
      
      if (!updatedUsedBy.contains(userId)) {
        updatedUsedBy.add(userId);
        
        _referralData = ReferralData(
          code: _referralData!.code,
          usageCount: _referralData!.usageCount + 1,
          totalEarnings: _referralData!.totalEarnings + 10.0, // 10 ريال لكل إحالة
          createdAt: _referralData!.createdAt,
          usedBy: updatedUsedBy,
        );
        
        await _saveReferralData();
        notifyListeners();
        
        return true;
      }
    }
    
    return false;
  }

  // الحصول على إحصائيات المشاركة
  Map<String, dynamic> getShareStats() {
    final platformCounts = <String, int>{};
    final typeCounts = <String, int>{};
    
    for (var share in _shareHistory) {
      final platform = share.metadata?['platform'] as String? ?? 'unknown';
      platformCounts[platform] = (platformCounts[platform] ?? 0) + 1;
      typeCounts[share.type] = (typeCounts[share.type] ?? 0) + 1;
    }
    
    return {
      'totalShares': _shareHistory.length,
      'platformCounts': platformCounts,
      'typeCounts': typeCounts,
      'referralUsage': _referralData?.usageCount ?? 0,
      'referralEarnings': _referralData?.totalEarnings ?? 0.0,
    };
  }

  // الحصول على المشاركات الأخيرة
  List<ShareData> getRecentShares([int limit = 10]) {
    return _shareHistory.take(limit).toList();
  }

  // الحصول على أكثر المنصات استخداماً
  List<MapEntry<String, int>> getMostUsedPlatforms([int limit = 5]) {
    final platformCounts = <String, int>{};
    
    for (var share in _shareHistory) {
      final platform = share.metadata?['platform'] as String? ?? 'unknown';
      platformCounts[platform] = (platformCounts[platform] ?? 0) + 1;
    }
    
    final sortedPlatforms = platformCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedPlatforms.take(limit).toList();
  }

  // مسح تاريخ المشاركة
  Future<void> clearShareHistory() async {
    _shareHistory.clear();
    await _saveShareHistory();
    notifyListeners();
  }

  // حذف مشاركة معينة
  Future<void> deleteShare(String shareId) async {
    _shareHistory.removeWhere((share) => share.id == shareId);
    await _saveShareHistory();
    notifyListeners();
  }

  // إنشاء بيانات تجريبية
  Future<void> generateSampleData() async {
    // إنشاء كود إحالة تجريبي
    await generateReferralCode();
    
    // إضافة مشاركات تجريبية
    final sampleShares = [
      {
        'type': 'product',
        'title': 'بروجكتر دعامية يسار',
        'description': 'قطعة غيار أصلية بجودة عالية',
        'platform': 'whatsapp',
      },
      {
        'type': 'app',
        'title': 'تطبيق دليل',
        'description': 'أفضل تطبيق لقطع غيار السيارات',
        'platform': 'telegram',
      },
      {
        'type': 'offer',
        'title': 'خصم 20%',
        'description': 'خصم خاص على جميع المنتجات',
        'platform': 'twitter',
      },
    ];

    for (var shareData in sampleShares) {
      final share = ShareData(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: shareData['type'] as String,
        title: shareData['title'] as String,
        description: shareData['description'] as String,
        metadata: {'platform': shareData['platform']},
        sharedAt: DateTime.now().subtract(
          Duration(days: sampleShares.indexOf(shareData)),
        ),
      );
      
      await _addToShareHistory(share);
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }
}
