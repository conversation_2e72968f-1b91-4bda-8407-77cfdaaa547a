<?php

namespace Botble\RevoWoocommerce\Http\Controllers\Api;

use Botble\Base\Http\Controllers\BaseController;
use Illuminate\Http\JsonResponse;
use Bo<PERSON>ble\Setting\Facades\Setting;

class HomeController extends BaseController
{
    public function slider(): JsonResponse
    {
        // Get sliders from settings
        $slidersJson = Setting::get('revo_home_sliders', '[]');
        $sliders = json_decode($slidersJson, true) ?: [];

        // No default data - return empty array if no sliders exist
        
        // Filter only active sliders and sort by order
        $activeSliders = array_filter($sliders, function($slider) {
            return $slider['status'] ?? true;
        });
        
        usort($activeSliders, function($a, $b) {
            return ($a['order'] ?? 0) - ($b['order'] ?? 0);
        });
        
        return response()->json([
            'success' => true,
            'data' => array_values($activeSliders),
        ]);
    }

    public function categories(): JsonResponse
    {
        // Get categories order from settings
        $categoriesJson = Setting::get('revo_categories_order', '[]');
        $customOrder = json_decode($categoriesJson, true) ?: [];
        
        // This would normally fetch from ec_product_categories table
        // For now, return placeholder data
        $categories = [
            [
                'id' => 1,
                'name' => 'الإلكترونيات',
                'image' => 'https://via.placeholder.com/150x150/007bff/ffffff?text=Electronics',
                'products_count' => 25,
                'order' => $customOrder[1] ?? 1,
                'visible_in_app' => Setting::get('revo_category_visible_1', true),
            ],
            [
                'id' => 2,
                'name' => 'الملابس',
                'image' => 'https://via.placeholder.com/150x150/28a745/ffffff?text=Clothes',
                'products_count' => 45,
                'order' => $customOrder[2] ?? 2,
                'visible_in_app' => Setting::get('revo_category_visible_2', true),
            ],
            [
                'id' => 3,
                'name' => 'المنزل والحديقة',
                'image' => 'https://via.placeholder.com/150x150/ffc107/ffffff?text=Home',
                'products_count' => 18,
                'order' => $customOrder[3] ?? 3,
                'visible_in_app' => Setting::get('revo_category_visible_3', false),
            ],
        ];
        
        // Filter visible categories and sort by order
        $visibleCategories = array_filter($categories, function($category) {
            return $category['visible_in_app'];
        });
        
        usort($visibleCategories, function($a, $b) {
            return $a['order'] - $b['order'];
        });
        
        return response()->json([
            'success' => true,
            'data' => array_values($visibleCategories),
        ]);
    }

    public function miniBanner(): JsonResponse
    {
        // Get mini banners from settings
        $bannersJson = Setting::get('revo_mini_banners', '[]');
        $banners = json_decode($bannersJson, true) ?: [];

        // No default data - return empty array if no banners exist
        
        // Filter active banners and group by position
        $activeBanners = array_filter($banners, function($banner) {
            return $banner['status'] ?? true;
        });
        
        $groupedBanners = [];
        foreach ($activeBanners as $banner) {
            $position = $banner['position'] ?? 'top';
            if (!isset($groupedBanners[$position])) {
                $groupedBanners[$position] = [];
            }
            $groupedBanners[$position][] = $banner;
        }
        
        // Sort each position group by order
        foreach ($groupedBanners as $position => $banners) {
            usort($groupedBanners[$position], function($a, $b) {
                return ($a['order'] ?? 0) - ($b['order'] ?? 0);
            });
        }
        
        return response()->json([
            'success' => true,
            'data' => $groupedBanners,
        ]);
    }

    public function flashSale(): JsonResponse
    {
        // Get flash sales from settings
        $salesJson = Setting::get('revo_flash_sales', '[]');
        $sales = json_decode($salesJson, true) ?: [];
        
        // Filter active sales (current time between start and end date)
        $now = now();
        $activeSales = array_filter($sales, function($sale) use ($now) {
            $startDate = $sale['start_date'] ?? null;
            $endDate = $sale['end_date'] ?? null;
            
            if (!$startDate || !$endDate) {
                return false;
            }
            
            return $now >= $startDate && $now <= $endDate;
        });
        
        return response()->json([
            'success' => true,
            'data' => array_values($activeSales),
        ]);
    }

    public function additionalProducts(): JsonResponse
    {
        // This would normally fetch featured/recommended products
        // For now, return placeholder response
        return response()->json([
            'success' => true,
            'data' => [
                'featured_products' => [],
                'recommended_products' => [],
                'recent_products' => [],
            ],
        ]);
    }
}
