<?php

namespace Bo<PERSON>ble\RevoWoocommerce;

use Bo<PERSON>ble\PluginManagement\Abstracts\PluginOperationAbstract;
use Illuminate\Support\Facades\Schema;

class Plugin extends PluginOperationAbstract
{
    public static function activate(): void
    {
        // Create upload directory for revo files
        $uploadDir = wp_upload_dir();
        $revoDir = $uploadDir['basedir'] . '/revo';
        
        if (!is_dir($revoDir)) {
            wp_mkdir_p($revoDir);
        }
        
        // Run any activation tasks
        self::runActivationTasks();
    }

    public static function activated(): void
    {
        // Tasks to run after plugin is activated
        // This is called after the plugin is successfully activated
    }

    public static function deactivate(): void
    {
        // Tasks to run when plugin is deactivated
        // Clean up temporary data if needed
    }

    public static function deactivated(): void
    {
        // Tasks to run after plugin is deactivated
    }

    public static function remove(): void
    {
        // Remove plugin data when plugin is deleted
        // Drop database tables if any
        
        // Remove upload directory
        $uploadDir = wp_upload_dir();
        $revoDir = $uploadDir['basedir'] . '/revo';
        
        if (is_dir($revoDir)) {
            self::removeDirectory($revoDir);
        }
        
        // Remove any custom database tables
        self::removeCustomTables();
    }

    protected static function runActivationTasks(): void
    {
        // Create any necessary database tables
        // Set default configuration values
        // Run any migration scripts
    }

    protected static function removeCustomTables(): void
    {
        // Remove any custom tables created by this plugin
        // Example:
        // Schema::dropIfExists('revo_settings');
        // Schema::dropIfExists('revo_notifications');
    }

    protected static function removeDirectory(string $dir): bool
    {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            is_dir($path) ? self::removeDirectory($path) : unlink($path);
        }
        
        return rmdir($dir);
    }
}
