class User {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final String? avatar;
  final String accountType;
  final bool isWholesaleCustomer;
  final bool emailVerified;
  final bool phoneVerified;
  final Map<String, dynamic>? profile;
  final DateTime? createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.avatar,
    this.accountType = 'customer',
    this.isWholesaleCustomer = false,
    this.emailVerified = false,
    this.phoneVerified = false,
    this.profile,
    this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      avatar: json['avatar'],
      accountType: json['account_type'] ?? 'customer',
      isWholesaleCustomer: json['is_wholesale_customer'] ?? false,
      emailVerified: json['email_verified'] ?? false,
      phoneVerified: json['phone_verified'] ?? false,
      profile: json['profile'],
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'account_type': accountType,
      'is_wholesale_customer': isWholesaleCustomer,
      'email_verified': emailVerified,
      'phone_verified': phoneVerified,
      'profile': profile,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  bool get isCustomer => accountType == 'customer';
  bool get isWholesale => isWholesaleCustomer;
  bool get isVerified => emailVerified && phoneVerified;
  String get displayName => name.isNotEmpty ? name : email;

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class LoginResponse {
  final User user;
  final String token;
  final String tokenType;
  final DateTime? expiresAt;

  LoginResponse({
    required this.user,
    required this.token,
    this.tokenType = 'Bearer',
    this.expiresAt,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      user: User.fromJson(json['user']),
      token: json['token'] ?? json['access_token'] ?? '',
      tokenType: json['token_type'] ?? 'Bearer',
      expiresAt: json['expires_at'] != null 
          ? DateTime.tryParse(json['expires_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'token': token,
      'token_type': tokenType,
      'expires_at': expiresAt?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'LoginResponse(user: ${user.name}, token: ${token.substring(0, 20)}...)';
  }
}
