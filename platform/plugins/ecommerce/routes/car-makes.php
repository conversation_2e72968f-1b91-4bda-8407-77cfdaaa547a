<?php

use <PERSON><PERSON><PERSON>\Base\Facades\BaseHelper;
use Botble\Ecommerce\Http\Controllers\CarMakeController;
use Botble\Ecommerce\Models\CarMake;
use Botble\Ecommerce\Models\CarModel;
use Botble\Ecommerce\Models\CarYear;
use Illuminate\Support\Facades\Route;

Route::middleware(['web', 'core', 'auth'])->group(function () {
    Route::prefix(BaseHelper::getAdminPrefix() . '/ecommerce')->group(function () {
        Route::resource('car-makes', CarMakeController::class, [
            'names' => [
                'index' => 'car-makes.index',
                'create' => 'car-makes.create',
                'store' => 'car-makes.store',
                'edit' => 'car-makes.edit',
                'update' => 'car-makes.update',
                'destroy' => 'car-makes.destroy',
            ],
        ]);
        
        Route::delete('car-makes/items/destroy', [
            'as' => 'car-makes.deletes',
            'uses' => 'Botble\Ecommerce\Http\Controllers\CarMakeController@deletes',
            'permission' => 'car-makes.destroy',
        ]);
        
        // هنا يمكنك إضافة مسارات لموديلات السيارات وسنواتها لاحقًا
    });
}); 