<?php

namespace Botble\Ecommerce\Http\Controllers\API;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Ecommerce\Models\CarMake;
use Botble\Ecommerce\Models\CarModel;
use Botble\Ecommerce\Models\CarYear;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CarController extends BaseController
{
    /**
     * الحصول على موديلات سيارة معينة
     */
    public function getModels(Request $request): JsonResponse
    {
        $makeId = $request->input('make_id');
        
        if (!$makeId) {
            return response()->json([]);
        }
        
        $models = CarModel::wherePublished()
            ->where('make_id', $makeId)
            ->select(['id', 'name'])
            ->orderBy('name')
            ->get();
            
        return response()->json($models);
    }
    
    /**
     * الحصول على سنوات الصنع لموديل معين
     */
    public function getYears(Request $request): JsonResponse
    {
        $modelId = $request->input('model_id');
        
        if (!$modelId) {
            return response()->json([]);
        }
        
        $years = CarYear::where('model_id', $modelId)
            ->select(['id', 'year'])
            ->orderBy('year', 'desc')
            ->get();
            
        return response()->json($years);
    }
} 