import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class ReviewModel {
  final String id;
  final int productId;
  final String userName;
  final String userAvatar;
  final double rating;
  final String comment;
  final DateTime createdAt;
  final List<String> images;
  final bool isVerifiedPurchase;
  final int helpfulCount;
  final bool isHelpful;

  ReviewModel({
    required this.id,
    required this.productId,
    required this.userName,
    this.userAvatar = '',
    required this.rating,
    required this.comment,
    required this.createdAt,
    this.images = const [],
    this.isVerifiedPurchase = false,
    this.helpfulCount = 0,
    this.isHelpful = false,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'productId': productId,
    'userName': userName,
    'userAvatar': userAvatar,
    'rating': rating,
    'comment': comment,
    'createdAt': createdAt.toIso8601String(),
    'images': images,
    'isVerifiedPurchase': isVerifiedPurchase,
    'helpfulCount': helpfulCount,
    'isHelpful': isHelpful,
  };

  factory ReviewModel.fromJson(Map<String, dynamic> json) => ReviewModel(
    id: json['id'],
    productId: json['productId'],
    userName: json['userName'],
    userAvatar: json['userAvatar'] ?? '',
    rating: json['rating'].toDouble(),
    comment: json['comment'],
    createdAt: DateTime.parse(json['createdAt']),
    images: List<String>.from(json['images'] ?? []),
    isVerifiedPurchase: json['isVerifiedPurchase'] ?? false,
    helpfulCount: json['helpfulCount'] ?? 0,
    isHelpful: json['isHelpful'] ?? false,
  );

  ReviewModel copyWith({
    String? id,
    int? productId,
    String? userName,
    String? userAvatar,
    double? rating,
    String? comment,
    DateTime? createdAt,
    List<String>? images,
    bool? isVerifiedPurchase,
    int? helpfulCount,
    bool? isHelpful,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      createdAt: createdAt ?? this.createdAt,
      images: images ?? this.images,
      isVerifiedPurchase: isVerifiedPurchase ?? this.isVerifiedPurchase,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      isHelpful: isHelpful ?? this.isHelpful,
    );
  }
}

class ReviewsProvider extends ChangeNotifier {
  static const String _reviewsKey = 'reviews';
  
  final Map<int, List<ReviewModel>> _productReviews = {};
  bool _isLoading = false;
  bool _isSubmitting = false;

  bool get isLoading => _isLoading;
  bool get isSubmitting => _isSubmitting;

  // الحصول على تقييمات منتج معين
  List<ReviewModel> getProductReviews(int productId) {
    return _productReviews[productId] ?? [];
  }

  // الحصول على إحصائيات التقييمات
  Map<String, dynamic> getReviewStats(int productId) {
    final reviews = getProductReviews(productId);
    
    if (reviews.isEmpty) {
      return {
        'totalReviews': 0,
        'averageRating': 0.0,
        'ratingDistribution': {5: 0, 4: 0, 3: 0, 2: 0, 1: 0},
        'verifiedPurchases': 0,
      };
    }

    final totalReviews = reviews.length;
    final averageRating = reviews.map((r) => r.rating).reduce((a, b) => a + b) / totalReviews;
    
    final ratingDistribution = <int, int>{5: 0, 4: 0, 3: 0, 2: 0, 1: 0};
    for (var review in reviews) {
      ratingDistribution[review.rating.round()] = 
          (ratingDistribution[review.rating.round()] ?? 0) + 1;
    }

    final verifiedPurchases = reviews.where((r) => r.isVerifiedPurchase).length;

    return {
      'totalReviews': totalReviews,
      'averageRating': averageRating,
      'ratingDistribution': ratingDistribution,
      'verifiedPurchases': verifiedPurchases,
    };
  }

  // تحميل التقييمات
  Future<void> loadReviews() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final reviewsJson = prefs.getString(_reviewsKey);
      
      if (reviewsJson != null) {
        final reviewsData = json.decode(reviewsJson) as Map<String, dynamic>;
        
        _productReviews.clear();
        reviewsData.forEach((productIdStr, reviewsList) {
          final productId = int.parse(productIdStr);
          final reviews = (reviewsList as List)
              .map((reviewJson) => ReviewModel.fromJson(reviewJson))
              .toList();
          _productReviews[productId] = reviews;
        });
      }
    } catch (e) {
      debugPrint('Error loading reviews: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // حفظ التقييمات
  Future<void> _saveReviews() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reviewsData = <String, dynamic>{};
      
      _productReviews.forEach((productId, reviews) {
        reviewsData[productId.toString()] = reviews.map((r) => r.toJson()).toList();
      });
      
      await prefs.setString(_reviewsKey, json.encode(reviewsData));
    } catch (e) {
      debugPrint('Error saving reviews: $e');
    }
  }

  // إضافة تقييم جديد
  Future<bool> addReview({
    required int productId,
    required String userName,
    required double rating,
    required String comment,
    List<String> images = const [],
    bool isVerifiedPurchase = false,
  }) async {
    _isSubmitting = true;
    notifyListeners();

    try {
      final review = ReviewModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        productId: productId,
        userName: userName,
        rating: rating,
        comment: comment,
        createdAt: DateTime.now(),
        images: images,
        isVerifiedPurchase: isVerifiedPurchase,
      );

      if (_productReviews[productId] == null) {
        _productReviews[productId] = [];
      }
      
      _productReviews[productId]!.insert(0, review);
      await _saveReviews();
      
      return true;
    } catch (e) {
      debugPrint('Error adding review: $e');
      return false;
    } finally {
      _isSubmitting = false;
      notifyListeners();
    }
  }

  // تحديث مفيد/غير مفيد
  Future<void> toggleHelpful(String reviewId, int productId) async {
    try {
      final reviews = _productReviews[productId];
      if (reviews == null) return;

      final reviewIndex = reviews.indexWhere((r) => r.id == reviewId);
      if (reviewIndex == -1) return;

      final review = reviews[reviewIndex];
      final newHelpfulCount = review.isHelpful 
          ? review.helpfulCount - 1 
          : review.helpfulCount + 1;

      reviews[reviewIndex] = review.copyWith(
        helpfulCount: newHelpfulCount,
        isHelpful: !review.isHelpful,
      );

      await _saveReviews();
      notifyListeners();
    } catch (e) {
      debugPrint('Error toggling helpful: $e');
    }
  }

  // ترتيب التقييمات
  List<ReviewModel> getSortedReviews(int productId, String sortBy) {
    final reviews = List<ReviewModel>.from(getProductReviews(productId));
    
    switch (sortBy) {
      case 'newest':
        reviews.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'oldest':
        reviews.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case 'highest_rating':
        reviews.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'lowest_rating':
        reviews.sort((a, b) => a.rating.compareTo(b.rating));
        break;
      case 'most_helpful':
        reviews.sort((a, b) => b.helpfulCount.compareTo(a.helpfulCount));
        break;
      case 'verified_first':
        reviews.sort((a, b) {
          if (a.isVerifiedPurchase && !b.isVerifiedPurchase) return -1;
          if (!a.isVerifiedPurchase && b.isVerifiedPurchase) return 1;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
    }
    
    return reviews;
  }

  // فلترة التقييمات
  List<ReviewModel> getFilteredReviews(int productId, {
    double? minRating,
    double? maxRating,
    bool? verifiedOnly,
    bool? withImages,
  }) {
    var reviews = getProductReviews(productId);
    
    if (minRating != null) {
      reviews = reviews.where((r) => r.rating >= minRating).toList();
    }
    
    if (maxRating != null) {
      reviews = reviews.where((r) => r.rating <= maxRating).toList();
    }
    
    if (verifiedOnly == true) {
      reviews = reviews.where((r) => r.isVerifiedPurchase).toList();
    }
    
    if (withImages == true) {
      reviews = reviews.where((r) => r.images.isNotEmpty).toList();
    }
    
    return reviews;
  }

  // البحث في التقييمات
  List<ReviewModel> searchReviews(int productId, String query) {
    if (query.isEmpty) return getProductReviews(productId);
    
    final lowerQuery = query.toLowerCase();
    return getProductReviews(productId)
        .where((review) => 
            review.comment.toLowerCase().contains(lowerQuery) ||
            review.userName.toLowerCase().contains(lowerQuery))
        .toList();
  }

  // الحصول على التقييمات الأحدث
  List<ReviewModel> getRecentReviews([int limit = 10]) {
    final allReviews = <ReviewModel>[];
    
    _productReviews.values.forEach((reviews) {
      allReviews.addAll(reviews);
    });
    
    allReviews.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return allReviews.take(limit).toList();
  }

  // إنشاء تقييمات تجريبية
  Future<void> createSampleReviews(int productId) async {
    final sampleReviews = [
      {
        'userName': 'أحمد محمد',
        'rating': 5.0,
        'comment': 'منتج ممتاز وجودة عالية، أنصح بشرائه بشدة. التوصيل كان سريع والتعامل احترافي.',
        'isVerifiedPurchase': true,
      },
      {
        'userName': 'فاطمة علي',
        'rating': 4.0,
        'comment': 'جيد جداً ولكن السعر مرتفع قليلاً. الجودة ممتازة والمنتج يستحق الشراء.',
        'isVerifiedPurchase': true,
      },
      {
        'userName': 'محمد السعيد',
        'rating': 3.0,
        'comment': 'منتج عادي، يؤدي الغرض ولكن ليس استثنائياً. التوصيل كان متأخر قليلاً.',
        'isVerifiedPurchase': false,
      },
      {
        'userName': 'نورا أحمد',
        'rating': 5.0,
        'comment': 'رائع! تماماً كما هو موصوف. سأشتري منتجات أخرى من نفس المتجر.',
        'isVerifiedPurchase': true,
      },
      {
        'userName': 'خالد عبدالله',
        'rating': 4.0,
        'comment': 'جودة جيدة وسعر مناسب. أنصح به للمبتدئين.',
        'isVerifiedPurchase': false,
      },
    ];

    for (var reviewData in sampleReviews) {
      await addReview(
        productId: productId,
        userName: reviewData['userName'] as String,
        rating: reviewData['rating'] as double,
        comment: reviewData['comment'] as String,
        isVerifiedPurchase: reviewData['isVerifiedPurchase'] as bool,
      );
      
      // تأخير صغير لمحاكاة الواقع
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  // مسح جميع التقييمات
  Future<void> clearAllReviews() async {
    _productReviews.clear();
    await _saveReviews();
    notifyListeners();
  }

  // مسح تقييمات منتج معين
  Future<void> clearProductReviews(int productId) async {
    _productReviews.remove(productId);
    await _saveReviews();
    notifyListeners();
  }
}
