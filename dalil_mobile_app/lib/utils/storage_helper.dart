import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

class StorageHelper {
  static SharedPreferences? _prefs;

  // تهيئة SharedPreferences
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // التأكد من التهيئة
  static Future<SharedPreferences> get _instance async {
    if (_prefs == null) {
      await init();
    }
    return _prefs!;
  }

  // === إدارة المصادقة ===

  // حفظ رمز المصادقة
  static Future<bool> saveToken(String token) async {
    final prefs = await _instance;
    return prefs.setString(AppConfig.tokenKey, token);
  }

  // الحصول على رمز المصادقة
  static Future<String?> getToken() async {
    final prefs = await _instance;
    return prefs.getString(AppConfig.tokenKey);
  }

  // حذف رمز المصادقة
  static Future<bool> removeToken() async {
    final prefs = await _instance;
    return prefs.remove(AppConfig.tokenKey);
  }

  // التحقق من وجود رمز المصادقة
  static Future<bool> hasToken() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // === إدارة بيانات المستخدم ===

  // حفظ بيانات المستخدم
  static Future<bool> saveUser(Map<String, dynamic> userData) async {
    final prefs = await _instance;
    final userJson = jsonEncode(userData);
    return prefs.setString(AppConfig.userKey, userJson);
  }

  // الحصول على بيانات المستخدم
  static Future<Map<String, dynamic>?> getUser() async {
    final prefs = await _instance;
    final userJson = prefs.getString(AppConfig.userKey);
    if (userJson != null) {
      try {
        return jsonDecode(userJson) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // حذف بيانات المستخدم
  static Future<bool> removeUser() async {
    final prefs = await _instance;
    return prefs.remove(AppConfig.userKey);
  }

  // === إدارة السلة ===

  // حفظ بيانات السلة
  static Future<bool> saveCart(List<Map<String, dynamic>> cartItems) async {
    final prefs = await _instance;
    final cartJson = jsonEncode(cartItems);
    return prefs.setString(AppConfig.cartKey, cartJson);
  }

  // الحصول على بيانات السلة
  static Future<List<Map<String, dynamic>>> getCart() async {
    final prefs = await _instance;
    final cartJson = prefs.getString(AppConfig.cartKey);
    if (cartJson != null) {
      try {
        final cartList = jsonDecode(cartJson) as List;
        return cartList.map((item) => item as Map<String, dynamic>).toList();
      } catch (e) {
        return [];
      }
    }
    return [];
  }

  // حذف السلة
  static Future<bool> clearCart() async {
    final prefs = await _instance;
    return prefs.remove(AppConfig.cartKey);
  }

  // === إدارة المفضلة ===

  // حفظ المفضلة
  static Future<bool> saveWishlist(List<int> productIds) async {
    final prefs = await _instance;
    final wishlistJson = jsonEncode(productIds);
    return prefs.setString(AppConfig.wishlistKey, wishlistJson);
  }

  // الحصول على المفضلة
  static Future<List<int>> getWishlist() async {
    final prefs = await _instance;
    final wishlistJson = prefs.getString(AppConfig.wishlistKey);
    if (wishlistJson != null) {
      try {
        final wishlistList = jsonDecode(wishlistJson) as List;
        return wishlistList.map((id) => id as int).toList();
      } catch (e) {
        return [];
      }
    }
    return [];
  }

  // إضافة منتج للمفضلة
  static Future<bool> addToWishlist(int productId) async {
    final wishlist = await getWishlist();
    if (!wishlist.contains(productId)) {
      wishlist.add(productId);
      return saveWishlist(wishlist);
    }
    return true;
  }

  // حذف منتج من المفضلة
  static Future<bool> removeFromWishlist(int productId) async {
    final wishlist = await getWishlist();
    wishlist.remove(productId);
    return saveWishlist(wishlist);
  }

  // التحقق من وجود منتج في المفضلة
  static Future<bool> isInWishlist(int productId) async {
    final wishlist = await getWishlist();
    return wishlist.contains(productId);
  }

  // === إدارة اللغة ===

  // حفظ اللغة المختارة
  static Future<bool> saveLanguage(String languageCode) async {
    final prefs = await _instance;
    return prefs.setString(AppConfig.languageKey, languageCode);
  }

  // الحصول على اللغة المختارة
  static Future<String> getLanguage() async {
    final prefs = await _instance;
    return prefs.getString(AppConfig.languageKey) ?? AppConfig.defaultLanguage;
  }

  // === إدارة الثيم ===

  // حفظ الثيم المختار
  static Future<bool> saveTheme(String theme) async {
    final prefs = await _instance;
    return prefs.setString(AppConfig.themeKey, theme);
  }

  // الحصول على الثيم المختار
  static Future<String> getTheme() async {
    final prefs = await _instance;
    return prefs.getString(AppConfig.themeKey) ?? 'light';
  }

  // === دوال عامة ===

  // حفظ قيمة نصية
  static Future<bool> setString(String key, String value) async {
    final prefs = await _instance;
    return prefs.setString(key, value);
  }

  // الحصول على قيمة نصية
  static Future<String?> getString(String key) async {
    final prefs = await _instance;
    return prefs.getString(key);
  }

  // حفظ قيمة رقمية صحيحة
  static Future<bool> setInt(String key, int value) async {
    final prefs = await _instance;
    return prefs.setInt(key, value);
  }

  // الحصول على قيمة رقمية صحيحة
  static Future<int?> getInt(String key) async {
    final prefs = await _instance;
    return prefs.getInt(key);
  }

  // حفظ قيمة رقمية عشرية
  static Future<bool> setDouble(String key, double value) async {
    final prefs = await _instance;
    return prefs.setDouble(key, value);
  }

  // الحصول على قيمة رقمية عشرية
  static Future<double?> getDouble(String key) async {
    final prefs = await _instance;
    return prefs.getDouble(key);
  }

  // حفظ قيمة منطقية
  static Future<bool> setBool(String key, bool value) async {
    final prefs = await _instance;
    return prefs.setBool(key, value);
  }

  // الحصول على قيمة منطقية
  static Future<bool> getBool(String key, {bool defaultValue = false}) async {
    final prefs = await _instance;
    return prefs.getBool(key) ?? defaultValue;
  }

  // حفظ قائمة نصية
  static Future<bool> setStringList(String key, List<String> value) async {
    final prefs = await _instance;
    return prefs.setStringList(key, value);
  }

  // الحصول على قائمة نصية
  static Future<List<String>> getStringList(String key) async {
    final prefs = await _instance;
    return prefs.getStringList(key) ?? [];
  }

  // حفظ كائن JSON
  static Future<bool> setJson(String key, Map<String, dynamic> value) async {
    final prefs = await _instance;
    final jsonString = jsonEncode(value);
    return prefs.setString(key, jsonString);
  }

  // الحصول على كائن JSON
  static Future<Map<String, dynamic>?> getJson(String key) async {
    final prefs = await _instance;
    final jsonString = prefs.getString(key);
    if (jsonString != null) {
      try {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // حذف مفتاح محدد
  static Future<bool> remove(String key) async {
    final prefs = await _instance;
    return prefs.remove(key);
  }

  // التحقق من وجود مفتاح
  static Future<bool> containsKey(String key) async {
    final prefs = await _instance;
    return prefs.containsKey(key);
  }

  // حذف جميع البيانات
  static Future<bool> clear() async {
    final prefs = await _instance;
    return prefs.clear();
  }

  // الحصول على جميع المفاتيح
  static Future<Set<String>> getKeys() async {
    final prefs = await _instance;
    return prefs.getKeys();
  }

  // === دوال تسجيل الخروج ===

  // تسجيل الخروج الكامل
  static Future<bool> logout() async {
    try {
      await removeToken();
      await removeUser();
      await clearCart();
      // يمكن الاحتفاظ بالمفضلة واللغة والثيم
      return true;
    } catch (e) {
      return false;
    }
  }

  // === دوال التصحيح ===

  // طباعة جميع البيانات المحفوظة (للتصحيح فقط)
  static Future<void> debugPrintAll() async {
    if (AppConfig.enableDebugMode) {
      final prefs = await _instance;
      final keys = prefs.getKeys();
      print('=== Storage Debug Info ===');
      for (final key in keys) {
        final value = prefs.get(key);
        print('$key: $value');
      }
      print('=========================');
    }
  }

  // الحصول على حجم البيانات المحفوظة تقريبياً
  static Future<int> getStorageSize() async {
    final prefs = await _instance;
    final keys = prefs.getKeys();
    int totalSize = 0;
    
    for (final key in keys) {
      final value = prefs.get(key);
      if (value is String) {
        totalSize += value.length;
      } else {
        totalSize += value.toString().length;
      }
    }
    
    return totalSize;
  }
}
