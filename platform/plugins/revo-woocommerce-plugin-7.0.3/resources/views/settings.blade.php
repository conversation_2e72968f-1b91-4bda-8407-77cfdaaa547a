@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-settings me-2"></i>
                        إعدادات RevoWOO
                    </h4>
                </div>
                <div class="card-body">
                    {!! Form::open(['route' => 'revo-woocommerce.settings.save', 'method' => 'POST', 'class' => 'needs-validation', 'novalidate' => true]) !!}

                    <div class="alert alert-info" role="alert">
                        <i class="ti ti-info-circle me-2"></i>
                        <strong>معلومة:</strong> هذه الإعدادات ستؤثر على تطبيق Flutter الخاص بك. تأكد من إدخال البيانات الصحيحة.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="app_title" class="form-label">
                                    <i class="ti ti-app-window me-1"></i>
                                    عنوان التطبيق
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="app_title" name="app_title"
                                       value="{{ old('app_title', $settings['app_title']) }}"
                                       placeholder="مثال: متجر دليلك" required>
                                <div class="form-text">اسم التطبيق الذي سيظهر للمستخدمين</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="app_logo" class="form-label">
                                    <i class="ti ti-photo me-1"></i>
                                    شعار التطبيق
                                </label>
                                <input type="url" class="form-control" id="app_logo" name="app_logo"
                                       value="{{ old('app_logo', $settings['app_logo']) }}"
                                       placeholder="https://example.com/logo.png">
                                <div class="form-text">رابط شعار التطبيق (يفضل PNG أو JPG)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="contact_info" class="form-label">
                                    <i class="ti ti-address-book me-1"></i>
                                    معلومات الاتصال
                                </label>
                                <textarea class="form-control" id="contact_info" name="contact_info" rows="4"
                                          placeholder="أدخل معلومات الاتصال مثل الهاتف والإيميل والعنوان">{{ old('contact_info', $settings['contact_info']) }}</textarea>
                                <div class="form-text">معلومات الاتصال التي ستظهر في التطبيق</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="about_url" class="form-label">
                                    <i class="ti ti-info-circle me-1"></i>
                                    رابط حول التطبيق
                                </label>
                                <input type="url" class="form-control" id="about_url" name="about_url"
                                       value="{{ old('about_url', $settings['about_url']) }}"
                                       placeholder="https://example.com/about">
                                <div class="form-text">رابط صفحة "حول التطبيق"</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="customer_support_url" class="form-label">
                                    <i class="ti ti-headset me-1"></i>
                                    رابط دعم العملاء
                                </label>
                                <input type="url" class="form-control" id="customer_support_url" name="customer_support_url"
                                       value="{{ old('customer_support_url', $settings['customer_support_url']) }}"
                                       placeholder="https://example.com/support">
                                <div class="form-text">رابط صفحة دعم العملاء</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="firebase_server_key" class="form-label">
                                    <i class="ti ti-brand-firebase me-1"></i>
                                    مفتاح خادم Firebase
                                </label>
                                <input type="password" class="form-control" id="firebase_server_key" name="firebase_server_key"
                                       value="{{ old('firebase_server_key', $settings['firebase_server_key']) }}"
                                       placeholder="أدخل مفتاح خادم Firebase">
                                <div class="form-text">مطلوب لإرسال الإشعارات Push</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="card-title mb-0">
                                        <i class="ti ti-brand-firebase me-2"></i>
                                        إعدادات Firebase
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check form-switch">
                                        <input type="checkbox" class="form-check-input" id="firebase_enabled" name="firebase_enabled" value="1"
                                               {{ old('firebase_enabled', $settings['firebase_enabled']) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="firebase_enabled">
                                            <strong>تفعيل Firebase</strong>
                                        </label>
                                    </div>
                                    <small class="text-muted">
                                        <i class="ti ti-info-circle me-1"></i>
                                        تفعيل هذا الخيار سيمكن إرسال الإشعارات Push للمستخدمين
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <button type="submit" class="btn btn-success btn-lg me-3">
                                        <i class="ti ti-device-floppy me-2"></i>
                                        حفظ الإعدادات
                                    </button>
                                    <a href="{{ route('revo-woocommerce.index') }}" class="btn btn-secondary btn-lg">
                                        <i class="ti ti-arrow-left me-2"></i>
                                        العودة للرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    {!! Form::close() !!}
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    $(document).ready(function() {
        // Form validation
        $('.needs-validation').on('submit', function(e) {
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            $(this).addClass('was-validated');
        });

        // Show/hide Firebase key field based on checkbox
        $('#firebase_enabled').on('change', function() {
            if ($(this).is(':checked')) {
                $('#firebase_server_key').closest('.form-group').show();
                Botble.showSuccess('تم تفعيل Firebase! تأكد من إدخال مفتاح الخادم الصحيح.');
            } else {
                $('#firebase_server_key').closest('.form-group').hide();
                Botble.showInfo('تم إلغاء تفعيل Firebase.');
            }
        });

        // Trigger change event on page load
        $('#firebase_enabled').trigger('change');

        // Success message
        @if(session('success'))
            Botble.showSuccess('{{ session('success') }}');
        @endif

        // Error messages
        @if($errors->any())
            @foreach($errors->all() as $error)
                Botble.showError('{{ $error }}');
            @endforeach
        @endif
    });
</script>
@endpush
