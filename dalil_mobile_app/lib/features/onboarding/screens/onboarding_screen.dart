import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/services/localization_service.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      icon: Icons.car_repair,
      title: 'مرحباً بك في دليل',
      subtitle: 'تطبيقك المتخصص في قطع غيار السيارات',
      description: 'اكتشف أسهل طريقة للعثور على قطع الغيار المناسبة لسيارتك',
      color: const Color(0xFF1976D2),
    ),
    OnboardingPage(
      icon: Icons.search,
      title: 'باحث قطع الغيار الذكي',
      subtitle: 'ابحث بسهولة ودقة',
      description: 'اختر نوع سيارتك وماركتها وموديلها للحصول على قطع الغيار المناسبة تماماً',
      color: const Color(0xFF03DAC6),
    ),
    OnboardingPage(
      icon: Icons.garage,
      title: 'احفظ سياراتك',
      subtitle: 'وصول سريع ومريح',
      description: 'احفظ معلومات سياراتك للوصول السريع وتجربة تسوق أفضل',
      color: const Color(0xFFFF6B35),
    ),
    OnboardingPage(
      icon: Icons.local_shipping,
      title: 'توصيل سريع وآمن',
      subtitle: 'قطع أصلية بجودة عالية',
      description: 'نضمن لك الحصول على قطع غيار أصلية مع خدمة توصيل سريعة وآمنة',
      color: const Color(0xFF4CAF50),
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // شريط علوي مع اختيار اللغة وتخطي
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // اختيار اللغة
                  PopupMenuButton<LocaleModel>(
                    icon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.language,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          LocalizationService.getCurrentLocale(context).nativeName,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    onSelected: (locale) async {
                      await LocalizationService.changeLanguage(context, locale);
                      setState(() {});
                    },
                    itemBuilder: (context) => LocalizationService.supportedLocales
                        .map((locale) => PopupMenuItem<LocaleModel>(
                              value: locale,
                              child: Row(
                                children: [
                                  Text(locale.flag),
                                  const SizedBox(width: 8),
                                  Text(locale.nativeName),
                                ],
                              ),
                            ))
                        .toList(),
                  ),
                  
                  // زر تخطي
                  TextButton(
                    onPressed: _completeOnboarding,
                    child: Text(
                      'تخطي',
                      style: TextStyle(
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // محتوى الصفحات
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index]);
                },
              ),
            ),
            
            // مؤشر الصفحات والأزرار
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // مؤشر الصفحات
                  SmoothPageIndicator(
                    controller: _pageController,
                    count: _pages.length,
                    effect: WormEffect(
                      dotColor: Colors.grey[300]!,
                      activeDotColor: _pages[_currentPage].color,
                      dotHeight: 8,
                      dotWidth: 8,
                      spacing: 16,
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // الأزرار
                  Row(
                    children: [
                      // زر السابق
                      if (_currentPage > 0)
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              _pageController.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            },
                            child: Text('common.previous'.tr()),
                          ),
                        ),
                      
                      if (_currentPage > 0) const SizedBox(width: 16),
                      
                      // زر التالي/البدء
                      Expanded(
                        flex: _currentPage == 0 ? 1 : 1,
                        child: ElevatedButton(
                          onPressed: () {
                            if (_currentPage == _pages.length - 1) {
                              _completeOnboarding();
                            } else {
                              _pageController.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _pages[_currentPage].color,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: Text(
                            _currentPage == _pages.length - 1
                                ? 'البدء'
                                : 'common.next'.tr(),
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // الأيقونة
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: page.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              page.icon,
              size: 60,
              color: page.color,
            ),
          ),
          
          const SizedBox(height: 40),
          
          // العنوان
          Text(
            page.title,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: page.color,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          // العنوان الفرعي
          Text(
            page.subtitle,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 24),
          
          // الوصف
          Text(
            page.description,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _completeOnboarding() async {
    await StorageService.saveSetting('onboarding_completed', true);
    context.go('/home');
  }
}

class OnboardingPage {
  final IconData icon;
  final String title;
  final String subtitle;
  final String description;
  final Color color;

  OnboardingPage({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.color,
  });
}
