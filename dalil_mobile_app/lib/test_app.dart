import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() {
  runApp(const VehiclePartsTestApp());
}

class VehiclePartsTestApp extends StatelessWidget {
  const VehiclePartsTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Vehicle Parts Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Arial',
      ),
      home: const VehiclePartsTestScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class VehiclePartsTestScreen extends StatefulWidget {
  const VehiclePartsTestScreen({super.key});

  @override
  State<VehiclePartsTestScreen> createState() => _VehiclePartsTestScreenState();
}

class _VehiclePartsTestScreenState extends State<VehiclePartsTestScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<dynamic> _categories = [];
  List<dynamic> _searchResults = [];
  bool _isLoading = false;
  String _status = 'جاهز للاختبار';

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
      _status = 'تحميل الفئات...';
    });

    try {
      final response = await http.get(
        Uri.parse('http://localhost:8000/api/v1/vehicle-parts/root-categories'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          _categories = data['data'] ?? [];
          _status = 'تم تحميل ${_categories.length} فئة';
        });
      } else {
        setState(() {
          _status = 'خطأ في تحميل الفئات: ${response.statusCode}';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'خطأ: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _searchParts() async {
    if (_searchController.text.isEmpty) return;

    setState(() {
      _isLoading = true;
      _status = 'البحث عن "${_searchController.text}"...';
    });

    try {
      final response = await http.post(
        Uri.parse('http://localhost:8000/api/v1/vehicle-parts/search'),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'keyword': _searchController.text,
          'per_page': 10,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final products = data['data']['products'] ?? [];
        setState(() {
          _searchResults = products;
          _status = 'وُجد ${products.length} منتج';
        });
      } else {
        setState(() {
          _status = 'خطأ في البحث: ${response.statusCode}';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'خطأ: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار Vehicle Parts APIs'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // حالة النظام
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Text(
                'الحالة: $_status',
                style: const TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),

            // البحث
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'ابحث عن قطع الغيار',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.search),
                    ),
                    onSubmitted: (_) => _searchParts(),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _searchParts,
                  child: const Text('بحث'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // الفئات
            if (_categories.isNotEmpty) ...[
              const Text(
                'الفئات المتاحة:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _categories.length,
                  itemBuilder: (context, index) {
                    final category = _categories[index];
                    return Container(
                      width: 120,
                      margin: const EdgeInsets.only(right: 8),
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.category, color: Colors.blue),
                              const SizedBox(height: 4),
                              Text(
                                category['name'] ?? '',
                                textAlign: TextAlign.center,
                                style: const TextStyle(fontSize: 12),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
            ],

            // نتائج البحث
            if (_searchResults.isNotEmpty) ...[
              const Text(
                'نتائج البحث:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: _searchResults.length,
                  itemBuilder: (context, index) {
                    final product = _searchResults[index];
                    return Card(
                      child: ListTile(
                        leading: const Icon(Icons.build, color: Colors.blue),
                        title: Text(product['name'] ?? ''),
                        subtitle: Text('${product['price'] ?? 0} ريال'),
                        trailing: const Icon(Icons.arrow_forward_ios),
                      ),
                    );
                  },
                ),
              ),
            ],

            // مؤشر التحميل
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
