<?php

use Botble\Gallery\Http\Controllers\API\GalleryController;
use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => ['api', 'throttle:api'],
    'prefix' => 'api/v1/galleries',
    'namespace' => 'Botble\Gallery\Http\Controllers\API',
], function (): void {
    // قائمة المعارض
    Route::get('/', [GalleryController::class, 'index']);
    
    // معرض محدد
    Route::get('/{id}', [GalleryController::class, 'show'])->wherePrimaryKey();
    
    // صور معرض محدد
    Route::get('/{id}/images', [GalleryController::class, 'getImages'])->wherePrimaryKey();
    
    // المعارض المميزة
    Route::get('/featured', [GalleryController::class, 'getFeatured']);
    
    // البحث في المعارض
    Route::get('/search', [GalleryController::class, 'search']);
});
