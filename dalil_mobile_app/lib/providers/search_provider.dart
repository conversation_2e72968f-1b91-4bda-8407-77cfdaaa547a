import 'package:flutter/material.dart';
import '../features/home/<USER>/product_model.dart';
import '../services/vehicle_parts_service.dart';
import '../services/api_service.dart';

class SearchFilter {
  final String? category;
  final double? minPrice;
  final double? maxPrice;
  final bool? inStock;
  final double? minRating;
  final String? brand;
  final String? sortBy;
  final bool ascending;

  SearchFilter({
    this.category,
    this.minPrice,
    this.maxPrice,
    this.inStock,
    this.minRating,
    this.brand,
    this.sortBy,
    this.ascending = true,
  });

  SearchFilter copyWith({
    String? category,
    double? minPrice,
    double? maxPrice,
    bool? inStock,
    double? minRating,
    String? brand,
    String? sortBy,
    bool? ascending,
  }) {
    return SearchFilter(
      category: category ?? this.category,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      inStock: inStock ?? this.inStock,
      minRating: minRating ?? this.minRating,
      brand: brand ?? this.brand,
      sortBy: sortBy ?? this.sortBy,
      ascending: ascending ?? this.ascending,
    );
  }

  bool get hasActiveFilters {
    return category != null ||
           minPrice != null ||
           maxPrice != null ||
           inStock != null ||
           minRating != null ||
           brand != null;
  }
}

class SearchProvider extends ChangeNotifier {
  final VehiclePartsService _vehiclePartsService = VehiclePartsService();
  
  // حالة البحث
  List<ProductModel> _searchResults = [];
  List<String> _searchHistory = [];
  List<String> _suggestions = [];
  SearchFilter _currentFilter = SearchFilter();
  
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  String _currentQuery = '';
  
  // Getters
  List<ProductModel> get searchResults => List.unmodifiable(_searchResults);
  List<String> get searchHistory => List.unmodifiable(_searchHistory);
  List<String> get suggestions => List.unmodifiable(_suggestions);
  SearchFilter get currentFilter => _currentFilter;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  String get currentQuery => _currentQuery;
  bool get hasResults => _searchResults.isNotEmpty;
  bool get hasActiveFilters => _currentFilter.hasActiveFilters;

  // البحث الرئيسي
  Future<void> search(String query, {SearchFilter? filter}) async {
    if (query.trim().isEmpty) {
      clearResults();
      return;
    }

    _setLoading(true);
    _setError(false, '');
    _currentQuery = query;
    
    if (filter != null) {
      _currentFilter = filter;
    }

    try {
      // إضافة إلى تاريخ البحث
      _addToSearchHistory(query);
      
      // البحث باستخدام API
      final response = await _vehiclePartsService.searchVehicleParts(
        keyword: query,
        page: 1,
        perPage: 50,
      );
      
      if (response.isSuccess && response.data != null) {
        // استخراج المنتجات من البيانات
        final data = response.data!;
        if (data['data'] != null && data['data'] is List) {
          _searchResults = (data['data'] as List)
              .map((item) => ProductModel.fromJson(item))
              .toList();
        } else {
          _searchResults = [];
        }

        // تطبيق الفلاتر المحلية
        _applyFilters();

        // ترتيب النتائج
        _sortResults();
      } else {
        _setError(true, response.error ?? 'فشل في البحث');
      }
    } catch (e) {
      _setError(true, 'حدث خطأ أثناء البحث: $e');
    } finally {
      _setLoading(false);
    }
  }

  // البحث السريع للاقتراحات
  Future<void> getSearchSuggestions(String query) async {
    if (query.trim().isEmpty) {
      _suggestions.clear();
      notifyListeners();
      return;
    }

    try {
      final response = await _vehiclePartsService.quickSearch(query, limit: 5);
      
      if (response.isSuccess && response.data != null) {
        // استخراج أسماء المنتجات كاقتراحات
        final data = response.data!;
        if (data['data'] != null && data['data'] is List) {
          _suggestions = (data['data'] as List)
              .map((item) => item['name'] as String)
              .take(5)
              .toList();
        } else {
          _suggestions = [];
        }
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error getting suggestions: $e');
    }
  }

  // تطبيق الفلاتر
  void _applyFilters() {
    if (!_currentFilter.hasActiveFilters) return;

    _searchResults = _searchResults.where((product) {
      // فلتر السعر
      if (_currentFilter.minPrice != null && 
          (product.price ?? 0) < _currentFilter.minPrice!) {
        return false;
      }
      
      if (_currentFilter.maxPrice != null && 
          (product.price ?? 0) > _currentFilter.maxPrice!) {
        return false;
      }
      
      // فلتر التوفر
      if (_currentFilter.inStock != null && 
          product.inStock != _currentFilter.inStock!) {
        return false;
      }
      
      // فلتر التقييم
      if (_currentFilter.minRating != null && 
          product.rating < _currentFilter.minRating!) {
        return false;
      }
      
      return true;
    }).toList();
  }

  // ترتيب النتائج
  void _sortResults() {
    if (_currentFilter.sortBy == null) return;

    _searchResults.sort((a, b) {
      int comparison = 0;
      
      switch (_currentFilter.sortBy) {
        case 'price':
          comparison = (a.price ?? 0).compareTo(b.price ?? 0);
          break;
        case 'rating':
          comparison = a.rating.compareTo(b.rating);
          break;
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'newest':
          comparison = (a.createdAt ?? DateTime.now())
              .compareTo(b.createdAt ?? DateTime.now());
          break;
        default:
          comparison = 0;
      }
      
      return _currentFilter.ascending ? comparison : -comparison;
    });
  }

  // تحديث الفلاتر
  void updateFilter(SearchFilter newFilter) {
    _currentFilter = newFilter;
    
    if (_searchResults.isNotEmpty) {
      // إعادة تطبيق الفلاتر على النتائج الحالية
      search(_currentQuery, filter: newFilter);
    }
  }

  // مسح الفلاتر
  void clearFilters() {
    _currentFilter = SearchFilter();
    
    if (_searchResults.isNotEmpty) {
      search(_currentQuery);
    }
  }

  // إضافة إلى تاريخ البحث
  void _addToSearchHistory(String query) {
    query = query.trim();
    if (query.isEmpty) return;
    
    // إزالة إذا كان موجود مسبقاً
    _searchHistory.remove(query);
    
    // إضافة في المقدمة
    _searchHistory.insert(0, query);
    
    // الاحتفاظ بآخر 20 بحث فقط
    if (_searchHistory.length > 20) {
      _searchHistory = _searchHistory.take(20).toList();
    }
    
    _saveSearchHistory();
  }

  // حفظ تاريخ البحث
  Future<void> _saveSearchHistory() async {
    try {
      // يمكن حفظ تاريخ البحث في SharedPreferences
      // await SharedPreferences...
    } catch (e) {
      debugPrint('Error saving search history: $e');
    }
  }

  // تحميل تاريخ البحث
  Future<void> loadSearchHistory() async {
    try {
      // يمكن تحميل تاريخ البحث من SharedPreferences
      // final prefs = await SharedPreferences.getInstance();
      // _searchHistory = prefs.getStringList('search_history') ?? [];
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading search history: $e');
    }
  }

  // حذف عنصر من تاريخ البحث
  void removeFromHistory(String query) {
    _searchHistory.remove(query);
    _saveSearchHistory();
    notifyListeners();
  }

  // مسح تاريخ البحث
  void clearSearchHistory() {
    _searchHistory.clear();
    _saveSearchHistory();
    notifyListeners();
  }

  // مسح النتائج
  void clearResults() {
    _searchResults.clear();
    _currentQuery = '';
    _suggestions.clear();
    _setError(false, '');
    notifyListeners();
  }

  // البحث في الفئات
  Future<void> searchInCategory(String categoryId, String query) async {
    _setLoading(true);
    _setError(false, '');

    try {
      final response = await _vehiclePartsService.searchVehicleParts(
        categoryIds: [int.parse(categoryId)],
        keyword: query,
        page: 1,
        perPage: 50,
      );
      
      if (response.isSuccess && response.data != null) {
        // استخراج المنتجات من البيانات
        final data = response.data!;
        if (data['data'] != null && data['data'] is List) {
          _searchResults = (data['data'] as List)
              .map((item) => ProductModel.fromJson(item))
              .toList();
        } else {
          _searchResults = [];
        }
        _applyFilters();
        _sortResults();
      } else {
        _setError(true, response.error ?? 'فشل في البحث');
      }
    } catch (e) {
      _setError(true, 'حدث خطأ أثناء البحث: $e');
    } finally {
      _setLoading(false);
    }
  }

  // الحصول على المنتجات المشابهة
  Future<List<ProductModel>> getSimilarProducts(ProductModel product) async {
    try {
      // البحث بناءً على الفئة أو الكلمات المفتاحية
      final keywords = product.name.split(' ').take(2).join(' ');
      
      final response = await _vehiclePartsService.searchVehicleParts(
        keyword: keywords,
        page: 1,
        perPage: 10,
      );
      
      if (response.isSuccess && response.data != null) {
        // استخراج المنتجات من البيانات
        final data = response.data!;
        if (data['data'] != null && data['data'] is List) {
          final products = (data['data'] as List)
              .map((item) => ProductModel.fromJson(item))
              .where((p) => p.id != product.id)
              .take(5)
              .toList();
          return products;
        }
      }
    } catch (e) {
      debugPrint('Error getting similar products: $e');
    }
    
    return [];
  }

  // تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // تحديث حالة الخطأ
  void _setError(bool hasError, String message) {
    _hasError = hasError;
    _errorMessage = message;
    notifyListeners();
  }

  // الحصول على إحصائيات البحث
  Map<String, dynamic> getSearchStats() {
    return {
      'totalResults': _searchResults.length,
      'averagePrice': _searchResults.isEmpty 
          ? 0 
          : _searchResults
              .map((p) => p.price ?? 0)
              .reduce((a, b) => a + b) / _searchResults.length,
      'inStockCount': _searchResults.where((p) => p.inStock).length,
      'averageRating': _searchResults.isEmpty 
          ? 0 
          : _searchResults
              .map((p) => p.rating)
              .reduce((a, b) => a + b) / _searchResults.length,
    };
  }
}
