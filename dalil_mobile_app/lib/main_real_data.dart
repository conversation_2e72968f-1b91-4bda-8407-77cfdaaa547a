import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'config/app_config.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image/cached_network_image.dart';

void main() {
  runApp(const DalilRealDataApp());
}

class DalilRealDataApp extends StatelessWidget {
  const DalilRealDataApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '${AppConfig.appName} - متجر قطع الغيار مع البيانات الحقيقية',
      debugShowCheckedModeBanner: false,

      // إعداد اتجاه النص للعربية
      locale: const Locale('ar'),
      supportedLocales: const [
        Locale('ar'), // العربية
        Locale('en'), // الإنجليزية
        Locale('ku'), // الكردية
      ],

      // إضافة دعم الترجمة
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      theme: ThemeData(
        primaryColor: Color(AppConfig.primaryColorValue),
        colorScheme: ColorScheme.fromSeed(
          seedColor: Color(AppConfig.primaryColorValue),
          brightness: Brightness.light,
        ),
        fontFamily: AppConfig.primaryFontFamily,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        appBarTheme: AppBarTheme(
          backgroundColor: Color(AppConfig.primaryColorValue),
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: Color(AppConfig.primaryColorValue),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
      initialRoute: '/home',
      routes: {
        '/home': (context) => const RealDataHomeScreen(),
        '/login': (context) => const LoginScreen(),
        '/register': (context) => const RegisterScreen(),
        '/product-detail': (context) {
          final product = ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
          return ProductDetailScreen(product: product);
        },
        '/category': (context) {
          final category = ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
          return CategoryScreen(category: category);
        },
      },
    );
  }
}

class RealDataHomeScreen extends StatefulWidget {
  const RealDataHomeScreen({super.key});

  @override
  State<RealDataHomeScreen> createState() => _RealDataHomeScreenState();
}

class _RealDataHomeScreenState extends State<RealDataHomeScreen> {
  String currentLanguage = 'العربية';
  int _selectedIndex = 0;
  int cartItemsCount = 3;

  // قوائم المنتجات المختلفة
  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> _featuredProducts = [];
  List<Map<String, dynamic>> _latestProducts = [];
  List<Map<String, dynamic>> _bestSellingProducts = [];
  List<Map<String, dynamic>> _discountedProducts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  Future<void> _loadAllData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل البيانات بشكل متوازي
      final results = await Future.wait([
        CategoryService.getProductCategories(),
        ProductService.getFeaturedProducts(limit: 15),
        ProductService.getLatestProducts(limit: 15),
        ProductService.getBestSellingProducts(limit: 15),
        ProductService.getDiscountedProducts(limit: 15),
      ]);

      setState(() {
        _categories = results[0];
        _featuredProducts = results[1];
        _latestProducts = results[2];
        _bestSellingProducts = results[3];
        _discountedProducts = results[4];
        _isLoading = false;
      });
    } catch (e) {
      print('خطأ في تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  // دالة الترجمة
  String tr(String key) {
    return translations[currentLanguage]?[key] ?? key;
  }

  final Map<String, Map<String, String>> translations = {
    'العربية': {
      'app_name': 'دليل',
      'search_hint': 'ابحث عن قطع الغيار...',
      'vehicle_parts_finder': 'باحث قطع الغيار الذكي',
      'categories': 'الفئات',
      'featured_products': 'المنتجات المميزة',
      'add_to_cart': 'إضافة للسلة',
      'in_stock': 'متوفر',
      'out_of_stock': 'غير متوفر',
      'home': 'الرئيسية',
      'categories_tab': 'الفئات',
      'cart': 'السلة',
      'profile': 'الحساب',
    },
    'English': {
      'app_name': 'Dalil',
      'search_hint': 'Search for auto parts...',
      'vehicle_parts_finder': 'Smart Vehicle Parts Finder',
      'categories': 'Categories',
      'featured_products': 'Featured Products',
      'add_to_cart': 'Add to Cart',
      'in_stock': 'In Stock',
      'out_of_stock': 'Out of Stock',
      'home': 'Home',
      'categories_tab': 'Categories',
      'cart': 'Cart',
      'profile': 'Profile',
    },
    'کوردی': {
      'app_name': 'دەلیل',
      'search_hint': 'گەڕان بۆ پارچەکانی ئۆتۆمبێل...',
      'vehicle_parts_finder': 'گەڕەری زیرەکی پارچەکانی ئۆتۆمبێل',
      'categories': 'جۆرەکان',
      'featured_products': 'بەرهەمە تایبەتەکان',
      'add_to_cart': 'زیادکردن بۆ سەبەتە',
      'in_stock': 'بەردەستە',
      'out_of_stock': 'بەردەست نییە',
      'home': 'سەرەکی',
      'categories_tab': 'جۆرەکان',
      'cart': 'سەبەتە',
      'profile': 'پرۆفایل',
    },
  };



  void _showAccountMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // معلومات المستخدم
            FutureBuilder<Map<String, dynamic>?>(
              future: AuthService.getCurrentUser(),
              builder: (context, snapshot) {
                final user = snapshot.data;
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Color(AppConfig.primaryColorValue),
                    child: Text(
                      user?['name']?.substring(0, 1).toUpperCase() ?? 'م',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(user?['name'] ?? 'المستخدم'),
                  subtitle: Text(user?['phone'] ?? ''),
                );
              },
            ),

            const Divider(),

            // خيارات القائمة
            ListTile(
              leading: const Icon(Icons.person),
              title: const Text('الملف الشخصي'),
              onTap: () {
                Navigator.pop(context);
                // الانتقال لصفحة الملف الشخصي
              },
            ),

            ListTile(
              leading: const Icon(Icons.shopping_bag),
              title: const Text('طلباتي'),
              onTap: () {
                Navigator.pop(context);
                // الانتقال لصفحة الطلبات
              },
            ),

            ListTile(
              leading: const Icon(Icons.favorite),
              title: const Text('المفضلة'),
              onTap: () {
                Navigator.pop(context);
                // الانتقال لصفحة المفضلة
              },
            ),

            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('الإعدادات'),
              onTap: () {
                Navigator.pop(context);
                // الانتقال لصفحة الإعدادات
              },
            ),

            const Divider(),

            ListTile(
              leading: const Icon(Icons.logout, color: Colors.red),
              title: const Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
              onTap: () async {
                Navigator.pop(context);
                await AuthService.logout();
                setState(() {
                  // تحديث الواجهة
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تسجيل الخروج بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        body: _selectedIndex == 0 ? _buildHomeContent() : _buildOtherContent(),
        bottomNavigationBar: _buildBottomNavBar(),
      ),
    );
  }

  Widget _buildHomeContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return CustomScrollView(
      slivers: [
        _buildAppBar(),
        _buildSearchBar(),
        _buildVehiclePartsFinderCard(),
        _buildCategoriesSection(),
        _buildLatestProductsSection(),
        _buildBestSellingProductsSection(),
        _buildDiscountedProductsSection(),
        _buildFeaturedProductsSection(),
      ],
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: const Color(0xFF1976D2),
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF1976D2),
                Color(0xFF1565C0),
                Color(0xFF0D47A1),
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // اللوجو
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.asset(
                        AppConfig.logoImage,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(
                            Icons.car_repair,
                            color: Color(0xFF1976D2),
                            size: 30,
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // اسم التطبيق
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          tr('app_name'),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Text(
                          'متجر قطع غيار السيارات - بيانات حقيقية',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // أيقونات الإجراءات
                  Row(
                    children: [
                      // اختيار اللغة
                      PopupMenuButton<String>(
                        icon: const Icon(Icons.language, color: Colors.white),
                        onSelected: (language) {
                          setState(() {
                            currentLanguage = language;
                          });
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'العربية',
                            child: Text('🇪🇬 العربية'),
                          ),
                          const PopupMenuItem(
                            value: 'English',
                            child: Text('🇺🇸 English'),
                          ),
                          const PopupMenuItem(
                            value: 'کوردی',
                            child: Text('🇮🇶 کوردی'),
                          ),
                        ],
                      ),
                      
                      // تسجيل الدخول/الحساب
                      FutureBuilder<bool>(
                        future: AuthService.isLoggedIn(),
                        builder: (context, snapshot) {
                          final isLoggedIn = snapshot.data ?? false;
                          return IconButton(
                            icon: Icon(
                              isLoggedIn ? Icons.account_circle : Icons.login,
                              color: Colors.white,
                            ),
                            onPressed: () {
                              if (isLoggedIn) {
                                // إظهار قائمة الحساب
                                _showAccountMenu(context);
                              } else {
                                // الانتقال لصفحة تسجيل الدخول
                                Navigator.pushNamed(context, '/login');
                              }
                            },
                          );
                        },
                      ),

                      // الإشعارات
                      IconButton(
                        icon: const Icon(Icons.notifications, color: Colors.white),
                        onPressed: () {},
                      ),
                      
                      // السلة
                      Stack(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.shopping_cart, color: Colors.white),
                            onPressed: () {
                              setState(() {
                                _selectedIndex = 2;
                              });
                            },
                          ),
                          if (cartItemsCount > 0)
                            Positioned(
                              right: 8,
                              top: 8,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 16,
                                  minHeight: 16,
                                ),
                                child: Text(
                                  '$cartItemsCount',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            decoration: InputDecoration(
              hintText: tr('search_hint'),
              prefixIcon: const Icon(Icons.search, color: Color(0xFF1976D2)),
              suffixIcon: IconButton(
                icon: const Icon(Icons.mic, color: Color(0xFF1976D2)),
                onPressed: () {},
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVehiclePartsFinderCard() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF4CAF50),
                Color(0xFF45A049),
                Color(0xFF388E3C),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withOpacity(0.3),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.search,
                      color: Colors.white,
                      size: 32,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      tr('vehicle_parts_finder'),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'ابحث عن قطع الغيار المناسبة لسيارتك بسهولة',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: const Color(0xFF4CAF50),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('ابدأ البحث'),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.directions_car,
                  color: Colors.white,
                  size: 40,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  tr('categories'),
                  style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _selectedIndex = 1;
                    });
                  },
                  icon: const Icon(Icons.arrow_forward),
                  label: const Text('عرض الكل'),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 130,
            child: FutureBuilder<List<Map<String, dynamic>>>(
              future: CategoryService.getProductCategories(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                final categories = snapshot.data ?? [];
                if (categories.isEmpty) {
                  return const Center(
                    child: Text('لا توجد فئات متاحة'),
                  );
                }

                return ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: categories.length,
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    final colors = [Colors.red, Colors.orange, Colors.blue, Colors.purple, Colors.green, Colors.amber];
                    final icons = [Icons.settings, Icons.disc_full, Icons.electrical_services, Icons.car_crash, Icons.filter_alt, Icons.opacity];

                    final color = colors[index % colors.length];
                    final icon = icons[index % icons.length];

                    return Container(
                      width: 110,
                      margin: const EdgeInsets.only(left: 12),
                      child: GestureDetector(
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            '/category',
                            arguments: category,
                          );
                        },
                        child: Column(
                          children: [
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: color.withOpacity(0.15),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: color.withOpacity(0.3),
                                  width: 2,
                                ),
                              ),
                              child: category['image'] != null && category['image'].toString().isNotEmpty
                                  ? ClipRRect(
                                      borderRadius: BorderRadius.circular(18),
                                      child: CachedNetworkImage(
                                        imageUrl: category['image'],
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) => Icon(icon, color: color, size: 36),
                                        errorWidget: (context, url, error) => Icon(icon, color: color, size: 36),
                                      ),
                                    )
                                  : Icon(icon, color: color, size: 36),
                            ),
                            const SizedBox(height: 10),
                            Text(
                              category['name'] ?? 'فئة غير محددة',
                              style: const TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedProductsSection() {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          const SizedBox(height: 24),
          _buildSectionHeader(
            title: 'المنتجات المميزة',
            subtitle: 'أفضل منتجاتنا المختارة بعناية',
            icon: Icons.star,
            color: Colors.amber,
          ),
          const SizedBox(height: 16),
          _buildHorizontalProductsList(_featuredProducts),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildOtherContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _selectedIndex == 1 ? Icons.category :
            _selectedIndex == 2 ? Icons.shopping_cart : Icons.person,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _selectedIndex == 1 ? 'صفحة الفئات' :
            _selectedIndex == 2 ? 'صفحة السلة' : 'صفحة الحساب',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قريباً...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavBar() {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: (index) {
        setState(() {
          _selectedIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      selectedItemColor: const Color(0xFF1976D2),
      unselectedItemColor: Colors.grey,
      items: [
        BottomNavigationBarItem(
          icon: const Icon(Icons.home),
          label: tr('home'),
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.category),
          label: tr('categories_tab'),
        ),
        BottomNavigationBarItem(
          icon: Stack(
            children: [
              const Icon(Icons.shopping_cart),
              if (cartItemsCount > 0)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding: const EdgeInsets.all(1),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 12,
                      minHeight: 12,
                    ),
                    child: Text(
                      '$cartItemsCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 8,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          label: tr('cart'),
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.person),
          label: tr('profile'),
        ),
      ],
    );
  }

  // قسم أحدث المنتجات
  Widget _buildLatestProductsSection() {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          const SizedBox(height: 24),
          _buildSectionHeader(
            title: 'أحدث المنتجات',
            subtitle: 'اكتشف أحدث إضافاتنا',
            icon: Icons.new_releases,
            color: Colors.green,
          ),
          const SizedBox(height: 16),
          _buildHorizontalProductsList(_latestProducts),
        ],
      ),
    );
  }

  // قسم الأكثر مبيعاً
  Widget _buildBestSellingProductsSection() {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          const SizedBox(height: 24),
          _buildSectionHeader(
            title: 'الأكثر مبيعاً',
            subtitle: 'المنتجات الأكثر طلباً',
            icon: Icons.trending_up,
            color: Colors.orange,
          ),
          const SizedBox(height: 16),
          _buildHorizontalProductsList(_bestSellingProducts),
        ],
      ),
    );
  }

  // قسم العروض والخصومات
  Widget _buildDiscountedProductsSection() {
    if (_discountedProducts.isEmpty) return const SliverToBoxAdapter(child: SizedBox.shrink());

    return SliverToBoxAdapter(
      child: Column(
        children: [
          const SizedBox(height: 24),
          _buildSectionHeader(
            title: 'عروض خاصة',
            subtitle: 'خصومات حصرية لفترة محدودة',
            icon: Icons.local_offer,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          _buildHorizontalProductsList(_discountedProducts),
        ],
      ),
    );
  }

  // بناء عنوان القسم
  Widget _buildSectionHeader({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () {
              // الانتقال لصفحة عرض جميع المنتجات
            },
            child: const Text('عرض الكل'),
          ),
        ],
      ),
    );
  }

  // بناء قائمة أفقية للمنتجات
  Widget _buildHorizontalProductsList(List<Map<String, dynamic>> products) {
    if (products.isEmpty) {
      return Container(
        height: 200,
        alignment: Alignment.center,
        child: Text(
          'لا توجد منتجات',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 16,
          ),
        ),
      );
    }

    return SizedBox(
      height: 280,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: products.length,
        itemBuilder: (context, index) {
          return Container(
            width: 200,
            margin: const EdgeInsets.only(left: 16),
            child: _buildEnhancedProductCard(products[index]),
          );
        },
      ),
    );
  }

  // بطاقة منتج محسنة
  Widget _buildEnhancedProductCard(Map<String, dynamic> product) {
    final hasDiscount = product['original_price'] != null &&
                       product['original_price'] > product['price'];

    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(
          context,
          '/product-detail',
          arguments: product,
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المنتج مع شارة الخصم
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                      child: _buildProductImage(product),
                    ),
                  ),

                  // شارة الخصم
                  if (hasDiscount)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${ProductService.calculateDiscountPercentage(product['original_price'], product['price'])}% خصم',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // معلومات المنتج
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم المنتج
                    Text(
                      product['name'] ?? 'منتج غير محدد',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const Spacer(),

                    // السعر
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${ProductService.formatPrice(product['price'] ?? 0)} د.ع',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Color(AppConfig.primaryColorValue),
                          ),
                        ),
                        if (hasDiscount)
                          Text(
                            '${ProductService.formatPrice(product['original_price'])} د.ع',
                            style: const TextStyle(
                              fontSize: 11,
                              decoration: TextDecoration.lineThrough,
                              color: Colors.grey,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء صورة المنتج
  Widget _buildProductImage(Map<String, dynamic> product) {
    final imageUrl = product['image_url'] ?? product['images']?.first ?? '';

    if (imageUrl.isEmpty) {
      return Container(
        color: Colors.grey[200],
        child: const Icon(
          Icons.car_repair,
          size: 60,
          color: Colors.grey,
        ),
      );
    }

    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        color: Colors.grey[100],
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
      errorWidget: (context, url, error) => Container(
        color: Colors.grey[200],
        child: const Icon(
          Icons.car_repair,
          size: 60,
          color: Colors.grey,
        ),
      ),
    );
  }
}
