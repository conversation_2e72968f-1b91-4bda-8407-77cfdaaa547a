$((function(){$(document).on("keyup","#shop-url",(function(){var e=$(this).closest(".shop-url-wrapper").find("small");e.html("".concat(e.data("base-url"),"/<strong>").concat($(this).val().toLowerCase(),"</strong>"))})).on("change","#shop-url",(function(){var e=$(this).closest("form"),s=e.find("button[type=submit]"),t=e.find(".form-label-description");s.addClass("btn-disabled").prop("disabled",!0),$httpClient.make().withLoading(e.find(".shop-url-wrapper")).post($(this).data("url"),{url:$(this).val(),reference_id:e.find("input[name=reference_id]").val()}).then((function(e){var a=e.data;a.error?t.removeClass("text-success").addClass("text-danger").text(a.message):(t.removeClass("text-danger").addClass("text-success").text(a.message),s.prop("disabled",!1).removeClass("btn-disabled"))}))}))}));