class VehicleCategory {
  final int id;
  final String name;
  final String? description;
  final String? icon;
  final List<VehicleCategory>? children;

  VehicleCategory({
    required this.id,
    required this.name,
    this.description,
    this.icon,
    this.children,
  });

  factory VehicleCategory.fromJson(Map<String, dynamic> json) {
    return VehicleCategory(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'],
      icon: json['icon'],
      children: json['children'] != null
          ? (json['children'] as List)
              .map((child) => VehicleCategory.fromJson(child))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'children': children?.map((c) => c.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'VehicleCategory(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VehicleCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class VehicleMake {
  final int id;
  final String name;
  final String? logo;
  final String? country;
  final int modelCount;

  VehicleMake({
    required this.id,
    required this.name,
    this.logo,
    this.country,
    this.modelCount = 0,
  });

  factory VehicleMake.fromJson(Map<String, dynamic> json) {
    return VehicleMake(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      logo: json['logo'],
      country: json['country'],
      modelCount: json['model_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'logo': logo,
      'country': country,
      'model_count': modelCount,
    };
  }

  @override
  String toString() {
    return 'VehicleMake(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VehicleMake && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class VehicleModel {
  final int id;
  final int makeId;
  final String name;
  final String? description;
  final List<int> years;
  final String? image;

  VehicleModel({
    required this.id,
    required this.makeId,
    required this.name,
    this.description,
    this.years = const [],
    this.image,
  });

  factory VehicleModel.fromJson(Map<String, dynamic> json) {
    return VehicleModel(
      id: json['id'] ?? 0,
      makeId: json['make_id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'],
      years: json['years'] != null 
          ? List<int>.from(json['years'])
          : [],
      image: json['image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'make_id': makeId,
      'name': name,
      'description': description,
      'years': years,
      'image': image,
    };
  }

  @override
  String toString() {
    return 'VehicleModel(id: $id, name: $name, makeId: $makeId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VehicleModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class SavedVehicle {
  final int id;
  final int categoryId;
  final int makeId;
  final int modelId;
  final int year;
  final String? nickname;
  final VehicleCategory? category;
  final VehicleMake? make;
  final VehicleModel? model;
  final DateTime? createdAt;

  SavedVehicle({
    required this.id,
    required this.categoryId,
    required this.makeId,
    required this.modelId,
    required this.year,
    this.nickname,
    this.category,
    this.make,
    this.model,
    this.createdAt,
  });

  factory SavedVehicle.fromJson(Map<String, dynamic> json) {
    return SavedVehicle(
      id: json['id'] ?? 0,
      categoryId: json['category_id'] ?? 0,
      makeId: json['make_id'] ?? 0,
      modelId: json['model_id'] ?? 0,
      year: json['year'] ?? 0,
      nickname: json['nickname'],
      category: json['category'] != null 
          ? VehicleCategory.fromJson(json['category'])
          : null,
      make: json['make'] != null 
          ? VehicleMake.fromJson(json['make'])
          : null,
      model: json['model'] != null 
          ? VehicleModel.fromJson(json['model'])
          : null,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category_id': categoryId,
      'make_id': makeId,
      'model_id': modelId,
      'year': year,
      'nickname': nickname,
      'category': category?.toJson(),
      'make': make?.toJson(),
      'model': model?.toJson(),
      'created_at': createdAt?.toIso8601String(),
    };
  }

  String get displayName {
    final parts = <String>[];
    if (make != null) parts.add(make!.name);
    if (model != null) parts.add(model!.name);
    parts.add(year.toString());
    return parts.join(' ');
  }

  @override
  String toString() {
    return 'SavedVehicle(id: $id, displayName: $displayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SavedVehicle && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
