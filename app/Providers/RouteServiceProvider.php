<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    public const HOME = '/home';

    /**
     * Define your route model bindings, pattern filters, etc.
     */
    public function boot(): void
    {
        parent::boot();
    }

    /**
     * Define the routes for the application.
     */
    public function map(): void
    {
        $this->mapThemeApiRoutes();
    }

    /**
     * Define the "theme api" routes for the application.
     */
    protected function mapThemeApiRoutes(): void
    {
        Route::middleware('api')
            ->group(base_path('routes/theme_api.php'));
    }
}
