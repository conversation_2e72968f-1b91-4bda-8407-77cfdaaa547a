import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/cart_provider.dart';

class CartService {
  static const String _cartKey = 'cart_items';

  // حفظ السلة محلياً
  Future<void> saveCart(List<CartItem> items) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartJson = items.map((item) => json.encode(item.toJson())).toList();
      await prefs.setStringList(_cartKey, cartJson);
    } catch (e) {
      throw Exception('Error saving cart: $e');
    }
  }

  // تحميل السلة المحفوظة
  Future<List<CartItem>> loadCart() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartJson = prefs.getStringList(_cartKey) ?? [];
      
      return cartJson
          .map((json) => CartItem.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      throw Exception('Error loading cart: $e');
    }
  }

  // مزامنة السلة مع الخادم
  Future<bool> syncWithServer(List<CartItem> items) async {
    try {
      // هنا يمكن إضافة منطق المزامنة مع الخادم
      // مثل إرسال POST request إلى API
      
      // محاكاة نجاح العملية
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      throw Exception('Error syncing cart with server: $e');
    }
  }

  // تحميل السلة من الخادم
  Future<List<CartItem>> loadFromServer() async {
    try {
      // هنا يمكن إضافة منطق تحميل السلة من الخادم
      // مثل إرسال GET request إلى API
      
      // محاكاة إرجاع قائمة فارغة
      await Future.delayed(const Duration(seconds: 1));
      return [];
    } catch (e) {
      throw Exception('Error loading cart from server: $e');
    }
  }

  // مسح السلة المحفوظة
  Future<void> clearCart() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cartKey);
    } catch (e) {
      throw Exception('Error clearing cart: $e');
    }
  }
}
