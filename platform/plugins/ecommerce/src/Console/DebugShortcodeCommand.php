<?php

namespace Bo<PERSON>ble\Ecommerce\Console;

use <PERSON><PERSON>ble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\ProductCategory;
use <PERSON><PERSON>ble\Ecommerce\Models\ProductCollection;
use Botble\Ecommerce\Repositories\Interfaces\ProductInterface;
use Botble\Ecommerce\Supports\EcommerceHelper;
use Illuminate\Console\Command;

class DebugShortcodeCommand extends Command
{
    protected $signature = 'ecommerce:debug-shortcode {category_id?} {collection_id?}';

    protected $description = 'Debug ecommerce-products shortcode issues';

    public function handle()
    {
        $categoryId = $this->argument('category_id') ?: 244;
        $collectionId = $this->argument('collection_id') ?: 5;

        $this->info("=== تشخيص مشكلة الشورت كود ecommerce-products ===");
        $this->newLine();

        // فحص المعاملات المرسلة
        $shortcodeParams = [
            'style' => 'slider',
            'title' => 'قطع غيار',
            'category_ids' => $categoryId,
            'collection_ids' => $collectionId,
            'show_featured_products_only' => 'yes',
            'limit' => '10',
            'slides_to_show' => '6',
            'items_per_row' => '4',
            'border_color' => '#fd4b6b',
            'background_color' => 'transparent',
            'enable_lazy_loading' => 'no'
        ];

        $this->info("1. فحص المعاملات المرسلة:");
        foreach ($shortcodeParams as $key => $value) {
            $this->line("   {$key}: {$value}");
        }
        $this->newLine();

        // فحص الفئة
        $this->info("2. فحص الفئة (category_ids = {$categoryId}):");
        $category = ProductCategory::find($categoryId);
        if ($category) {
            $this->line("   ✅ الفئة موجودة: {$category->name}");
            $this->line("   حالة النشر: " . ($category->status === 'published' ? 'منشورة' : 'غير منشورة'));

            // عدد المنتجات في هذه الفئة
            $productsInCategory = Product::whereHas('categories', function($q) use ($categoryId) {
                $q->where('ec_product_category_product.category_id', $categoryId);
            })->where('is_variation', 0)->where('status', 'published')->count();

            $this->line("   عدد المنتجات في الفئة: {$productsInCategory}");

            // عدد المنتجات المميزة في هذه الفئة
            $featuredInCategory = Product::whereHas('categories', function($q) use ($categoryId) {
                $q->where('ec_product_category_product.category_id', $categoryId);
            })->where('is_variation', 0)
              ->where('status', 'published')
              ->where('is_featured', 1)
              ->count();

            $this->line("   عدد المنتجات المميزة في الفئة: {$featuredInCategory}");
        } else {
            $this->error("   ❌ الفئة غير موجودة!");
        }
        $this->newLine();

        // فحص المجموعة
        $this->info("3. فحص المجموعة (collection_ids = {$collectionId}):");
        $collection = ProductCollection::find($collectionId);
        if ($collection) {
            $this->line("   ✅ المجموعة موجودة: {$collection->name}");
            $this->line("   حالة النشر: " . ($collection->status === 'published' ? 'منشورة' : 'غير منشورة'));

            // عدد المنتجات في هذه المجموعة
            $productsInCollection = Product::whereHas('productCollections', function($q) use ($collectionId) {
                $q->where('ec_product_collection_products.product_collection_id', $collectionId);
            })->where('is_variation', 0)->where('status', 'published')->count();

            $this->line("   عدد المنتجات في المجموعة: {$productsInCollection}");

            // عدد المنتجات المميزة في هذه المجموعة
            $featuredInCollection = Product::whereHas('productCollections', function($q) use ($collectionId) {
                $q->where('ec_product_collection_products.product_collection_id', $collectionId);
            })->where('is_variation', 0)
              ->where('status', 'published')
              ->where('is_featured', 1)
              ->count();

            $this->line("   عدد المنتجات المميزة في المجموعة: {$featuredInCollection}");
        } else {
            $this->error("   ❌ المجموعة غير موجودة!");
        }
        $this->newLine();

        // فحص المنتجات التي تحقق جميع الشروط
        $this->info("4. فحص المنتجات التي تحقق جميع الشروط:");

        $condition = [
            'ec_products.is_variation' => 0,
            'ec_products.is_featured' => 1, // لأن show_featured_products_only = yes
        ];

        $products = app(ProductInterface::class)->filterProducts([
            'categories' => [$categoryId],
            'collections' => [$collectionId],
        ], [
            'take' => 10,
            'order_by' => [
                'order' => 'ASC',
                'created_at' => 'DESC',
            ],
            'condition' => $condition,
            ...app(EcommerceHelper::class)->withReviewsParams(),
        ]);

        $this->line("   عدد المنتجات المسترجعة: " . $products->count());

        if ($products->count() > 0) {
            $this->line("   المنتجات المسترجعة:");
            foreach ($products as $product) {
                $this->line("   - {$product->name} (ID: {$product->id})");
                $this->line("     مميز: " . ($product->is_featured ? 'نعم' : 'لا'));
                $this->line("     حالة النشر: {$product->status}");
                $this->line("     الفئات: " . $product->categories->pluck('name')->implode(', '));
                $this->line("     المجموعات: " . $product->productCollections->pluck('name')->implode(', '));
                $this->newLine();
            }
        } else {
            $this->error("   ❌ لا توجد منتجات تحقق جميع الشروط!");
            $this->newLine();

            // تشخيص إضافي
            $this->info("5. تشخيص إضافي:");

            // فحص بدون شرط المميز
            $this->line("   أ. فحص بدون شرط المنتجات المميزة:");
            $productsWithoutFeatured = app(ProductInterface::class)->filterProducts([
                'categories' => [$categoryId],
                'collections' => [$collectionId],
            ], [
                'take' => 10,
                'condition' => ['ec_products.is_variation' => 0],
                'order_by' => ['order' => 'ASC', 'created_at' => 'DESC'],
            ]);
            $this->line("      عدد المنتجات: " . $productsWithoutFeatured->count());

            // فحص بدون شرط المجموعة
            $this->line("   ب. فحص بدون شرط المجموعة:");
            $productsWithoutCollection = app(ProductInterface::class)->filterProducts([
                'categories' => [$categoryId],
            ], [
                'take' => 10,
                'condition' => ['ec_products.is_variation' => 0, 'ec_products.is_featured' => 1],
                'order_by' => ['order' => 'ASC', 'created_at' => 'DESC'],
            ]);
            $this->line("      عدد المنتجات: " . $productsWithoutCollection->count());

            // فحص بدون شرط الفئة
            $this->line("   ج. فحص بدون شرط الفئة:");
            $productsWithoutCategory = app(ProductInterface::class)->filterProducts([
                'collections' => [$collectionId],
            ], [
                'take' => 10,
                'condition' => ['ec_products.is_variation' => 0, 'ec_products.is_featured' => 1],
                'order_by' => ['order' => 'ASC', 'created_at' => 'DESC'],
            ]);
            $this->line("      عدد المنتجات: " . $productsWithoutCategory->count());
        }

        $this->newLine();
        $this->info("=== انتهى التشخيص ===");
    }
}
