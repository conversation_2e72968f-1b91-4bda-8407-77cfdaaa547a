<?php

use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Botble\RevoWoocommerce\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(['prefix' => 'admin/revo-woocommerce', 'as' => 'revo-woocommerce.'], function () {
            
            // Dashboard routes
            Route::get('/', 'RevoWoocommerceController@index')->name('index');
            Route::get('settings', 'RevoWoocommerceController@settings')->name('settings');
            Route::post('settings', 'RevoWoocommerceController@saveSettings')->name('settings.save');
            
            // Intro pages management
            Route::group(['prefix' => 'intro-pages', 'as' => 'intro-pages.'], function () {
                Route::get('/', 'IntroPageController@index')->name('index');
                Route::get('create', 'IntroPageController@create')->name('create');
                Route::post('/', 'IntroPageController@store')->name('store');
                Route::get('{id}/edit', 'IntroPageController@edit')->name('edit');
                Route::put('{id}', 'IntroPageController@update')->name('update');
                Route::delete('{id}', 'IntroPageController@destroy')->name('destroy');
            });
            
            // Home slider management
            Route::group(['prefix' => 'home-slider', 'as' => 'home-slider.'], function () {
                Route::get('/', 'HomeSliderController@index')->name('index');
                Route::get('create', 'HomeSliderController@create')->name('create');
                Route::post('/', 'HomeSliderController@store')->name('store');
                Route::get('{id}/edit', 'HomeSliderController@edit')->name('edit');
                Route::put('{id}', 'HomeSliderController@update')->name('update');
                Route::delete('{id}', 'HomeSliderController@destroy')->name('destroy');
            });
            
            // Categories management
            Route::group(['prefix' => 'categories', 'as' => 'categories.'], function () {
                Route::get('/', 'CategoryController@index')->name('index');
                Route::post('update-order', 'CategoryController@updateOrder')->name('update-order');
            });
            
            // Mini banner management
            Route::group(['prefix' => 'mini-banner', 'as' => 'mini-banner.'], function () {
                Route::get('/', 'MiniBannerController@index')->name('index');
                Route::get('create', 'MiniBannerController@create')->name('create');
                Route::post('/', 'MiniBannerController@store')->name('store');
                Route::get('{id}/edit', 'MiniBannerController@edit')->name('edit');
                Route::put('{id}', 'MiniBannerController@update')->name('update');
                Route::delete('{id}', 'MiniBannerController@destroy')->name('destroy');
            });
            
            // Flash sale management
            Route::group(['prefix' => 'flash-sale', 'as' => 'flash-sale.'], function () {
                Route::get('/', 'FlashSaleController@index')->name('index');
                Route::post('update', 'FlashSaleController@update')->name('update');
            });
            
            // Push notifications
            Route::group(['prefix' => 'notifications', 'as' => 'notifications.'], function () {
                Route::get('/', 'NotificationController@index')->name('index');
                Route::get('create', 'NotificationController@create')->name('create');
                Route::post('/', 'NotificationController@store')->name('store');
                Route::post('send', 'NotificationController@send')->name('send');
            });
            
        });
});
