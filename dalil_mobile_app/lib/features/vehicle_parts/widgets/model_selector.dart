import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:easy_localization/easy_localization.dart';
import '../providers/vehicle_parts_provider.dart';
import '../models/vehicle_category_model.dart';

class ModelSelector extends StatelessWidget {
  const ModelSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<VehiclePartsProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'vehicle_parts_finder.model'.tr(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(
                  color: provider.selectedMake == null 
                      ? Colors.grey[300]! 
                      : Theme.of(context).primaryColor.withOpacity(0.3),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: _buildContent(context, provider),
            ),
          ],
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, VehiclePartsProvider provider) {
    if (provider.selectedMake == null) {
      return _buildDisabledState(context);
    }

    if (provider.isLoading && provider.models.isEmpty) {
      return _buildLoadingState(context);
    }

    if (provider.models.isEmpty) {
      return _buildEmptyState(context);
    }

    return _buildDropdown(context, provider);
  }

  Widget _buildDisabledState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          Icon(
            Icons.block,
            color: Colors.grey[400],
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            'اختر الماركة أولاً',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 12),
          Text(
            'تحميل الموديلات...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.grey[600],
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            'لا توجد موديلات متاحة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdown(BuildContext context, VehiclePartsProvider provider) {
    return DropdownButtonHideUnderline(
      child: DropdownButton<VehicleCategoryModel>(
        value: provider.selectedModel,
        hint: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Icon(
                Icons.car_rental,
                color: Colors.grey[600],
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'vehicle_parts_finder.select_model'.tr(),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        isExpanded: true,
        icon: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: Icon(
            Icons.keyboard_arrow_down,
            color: Colors.grey[600],
          ),
        ),
        items: provider.models.map((model) {
          return DropdownMenuItem<VehicleCategoryModel>(
            value: model,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.car_rental,
                      color: Theme.of(context).primaryColor,
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      model.name,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  if (model.childrenCount > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${model.childrenCount}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        }).toList(),
        onChanged: (model) {
          if (model != null) {
            provider.selectModel(model);
          }
        },
        dropdownColor: Colors.white,
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        menuMaxHeight: 300,
      ),
    );
  }
}
