<?php

namespace ArchiElite\Announcement\Http\Controllers\API;

use ArchiElite\Announcement\Http\Resources\AnnouncementResource;
use ArchiElite\Announcement\Models\Announcement;
use Botble\Base\Http\Controllers\BaseController;
use Carbon\Carbon;
use Illuminate\Http\Request;

class AnnouncementController extends BaseController
{
    /**
     * قائمة الإعلانات
     *
     * @group Announcement
     */
    public function index(Request $request)
    {
        try {
            $query = Announcement::query();

            // فلترة حسب الحالة النشطة
            if ($request->boolean('active_only', false)) {
                $query->available();
            }

            // البحث في المحتوى
            if ($request->has('search') && $request->input('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('content', 'LIKE', "%{$search}%");
                });
            }

            // فلترة حسب التاريخ
            if ($request->has('start_date') && $request->input('start_date')) {
                $query->where('start_date', '>=', $request->input('start_date'));
            }

            if ($request->has('end_date') && $request->input('end_date')) {
                $query->where('end_date', '<=', $request->input('end_date'));
            }

            // ترتيب النتائج
            $sortBy = $request->input('sort_by', 'created_at');
            $sortOrder = $request->input('sort_order', 'desc');
            
            if (in_array($sortBy, ['created_at', 'start_date', 'end_date', 'name'])) {
                $query->orderBy($sortBy, $sortOrder === 'asc' ? 'asc' : 'desc');
            }

            $perPage = $request->integer('per_page', 10);
            $perPage = min($perPage, 50);

            $announcements = $query->paginate($perPage);

            return $this
                ->httpResponse()
                ->setData(AnnouncementResource::collection($announcements))
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching announcements.'));
        }
    }

    /**
     * عرض إعلان محدد
     *
     * @group Announcement
     */
    public function show($id)
    {
        try {
            $announcement = Announcement::query()->findOrFail($id);

            return $this
                ->httpResponse()
                ->setData(new AnnouncementResource($announcement))
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(404)
                ->setMessage(__('Announcement not found.'));
        }
    }

    /**
     * الإعلانات النشطة حالياً
     *
     * @group Announcement
     */
    public function getActiveAnnouncements(Request $request)
    {
        try {
            $limit = $request->integer('limit', 5);
            $limit = min($limit, 20);

            $announcements = Announcement::query()
                ->available()
                ->orderByDesc('created_at')
                ->limit($limit)
                ->get();

            return $this
                ->httpResponse()
                ->setData(AnnouncementResource::collection($announcements))
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching active announcements.'));
        }
    }

    /**
     * إحصائيات الإعلانات
     *
     * @group Announcement
     */
    public function getStats()
    {
        try {
            $totalAnnouncements = Announcement::query()->count();
            
            $activeAnnouncements = Announcement::query()
                ->available()
                ->count();

            $scheduledAnnouncements = Announcement::query()
                ->where('is_active', true)
                ->where('start_date', '>', Carbon::now())
                ->count();

            $expiredAnnouncements = Announcement::query()
                ->where('is_active', true)
                ->where('end_date', '<', Carbon::now())
                ->count();

            $dismissibleAnnouncements = Announcement::query()
                ->where('dismissible', true)
                ->count();

            $withActionAnnouncements = Announcement::query()
                ->where('has_action', true)
                ->count();

            return $this
                ->httpResponse()
                ->setData([
                    'total_announcements' => $totalAnnouncements,
                    'active_announcements' => $activeAnnouncements,
                    'scheduled_announcements' => $scheduledAnnouncements,
                    'expired_announcements' => $expiredAnnouncements,
                    'dismissible_announcements' => $dismissibleAnnouncements,
                    'with_action_announcements' => $withActionAnnouncements,
                ])
                ->toApiResponse();

        } catch (\Exception $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(500)
                ->setMessage(__('An error occurred while fetching announcement statistics.'));
        }
    }
}
