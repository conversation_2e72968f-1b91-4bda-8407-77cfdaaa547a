class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final String? error;
  final Map<String, dynamic>? meta;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
    this.meta,
  });

  factory ApiResponse.success(T data, {String? message, Map<String, dynamic>? meta}) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      meta: meta,
    );
  }

  factory ApiResponse.error(String error, {Map<String, dynamic>? meta}) {
    return ApiResponse<T>(
      success: false,
      error: error,
      meta: meta,
    );
  }

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    try {
      if (json['success'] == true || json['status'] == 'success') {
        return ApiResponse.success(
          fromJsonT(json['data']),
          message: json['message'],
          meta: json['meta'],
        );
      } else {
        return ApiResponse.error(
          json['message'] ?? json['error'] ?? 'حدث خطأ غير متوقع',
          meta: json['meta'],
        );
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحليل البيانات: ${e.toString()}');
    }
  }

  bool get isSuccess => success;
  bool get isError => !success;
  bool get hasData => data != null;
  bool get hasError => error != null;

  @override
  String toString() {
    return 'ApiResponse(success: $success, data: $data, message: $message, error: $error)';
  }
}

// نموذج للاستجابة المقسمة (Paginated Response)
class PaginatedResponse<T> {
  final List<T> data;
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;
  final String? nextPageUrl;
  final String? prevPageUrl;

  PaginatedResponse({
    required this.data,
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
    this.nextPageUrl,
    this.prevPageUrl,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final dataList = (json['data'] as List?)
        ?.map((item) => fromJsonT(item as Map<String, dynamic>))
        .toList() ?? [];

    return PaginatedResponse<T>(
      data: dataList,
      currentPage: json['current_page'] ?? 1,
      lastPage: json['last_page'] ?? 1,
      perPage: json['per_page'] ?? 10,
      total: json['total'] ?? 0,
      nextPageUrl: json['next_page_url'],
      prevPageUrl: json['prev_page_url'],
    );
  }

  bool get hasNextPage => nextPageUrl != null;
  bool get hasPrevPage => prevPageUrl != null;
  bool get isFirstPage => currentPage == 1;
  bool get isLastPage => currentPage == lastPage;
  bool get isEmpty => data.isEmpty;
  bool get isNotEmpty => data.isNotEmpty;

  @override
  String toString() {
    return 'PaginatedResponse(data: ${data.length} items, page: $currentPage/$lastPage, total: $total)';
  }
}

// نموذج للأخطاء المفصلة
class ApiError {
  final String message;
  final String? code;
  final Map<String, List<String>>? errors;
  final int? statusCode;

  ApiError({
    required this.message,
    this.code,
    this.errors,
    this.statusCode,
  });

  factory ApiError.fromJson(Map<String, dynamic> json) {
    return ApiError(
      message: json['message'] ?? 'حدث خطأ غير متوقع',
      code: json['code'],
      errors: json['errors'] != null 
          ? Map<String, List<String>>.from(
              json['errors'].map((key, value) => MapEntry(
                key,
                List<String>.from(value),
              )),
            )
          : null,
      statusCode: json['status_code'],
    );
  }

  String get firstError {
    if (errors != null && errors!.isNotEmpty) {
      final firstKey = errors!.keys.first;
      final firstErrorList = errors![firstKey];
      if (firstErrorList != null && firstErrorList.isNotEmpty) {
        return firstErrorList.first;
      }
    }
    return message;
  }

  List<String> get allErrors {
    final List<String> allErrorsList = [message];
    if (errors != null) {
      for (final errorList in errors!.values) {
        allErrorsList.addAll(errorList);
      }
    }
    return allErrorsList;
  }

  @override
  String toString() {
    return 'ApiError(message: $message, code: $code, statusCode: $statusCode)';
  }
}

// نموذج للاستجابة العامة
class GeneralResponse {
  final bool success;
  final String message;
  final dynamic data;

  GeneralResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory GeneralResponse.fromJson(Map<String, dynamic> json) {
    return GeneralResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
    };
  }

  @override
  String toString() {
    return 'GeneralResponse(success: $success, message: $message)';
  }
}

// نموذج للإحصائيات
class StatsResponse {
  final Map<String, dynamic> stats;
  final String? period;
  final DateTime? lastUpdated;

  StatsResponse({
    required this.stats,
    this.period,
    this.lastUpdated,
  });

  factory StatsResponse.fromJson(Map<String, dynamic> json) {
    return StatsResponse(
      stats: json['stats'] ?? {},
      period: json['period'],
      lastUpdated: json['last_updated'] != null 
          ? DateTime.tryParse(json['last_updated'])
          : null,
    );
  }

  T? getStat<T>(String key) {
    return stats[key] as T?;
  }

  int getIntStat(String key, {int defaultValue = 0}) {
    final value = stats[key];
    if (value is int) return value;
    if (value is String) return int.tryParse(value) ?? defaultValue;
    return defaultValue;
  }

  double getDoubleStat(String key, {double defaultValue = 0.0}) {
    final value = stats[key];
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? defaultValue;
    return defaultValue;
  }

  String getStringStat(String key, {String defaultValue = ''}) {
    final value = stats[key];
    return value?.toString() ?? defaultValue;
  }

  @override
  String toString() {
    return 'StatsResponse(stats: ${stats.keys.length} items, period: $period)';
  }
}

// نموذج للبحث
class SearchResponse<T> {
  final List<T> results;
  final int total;
  final String query;
  final double? executionTime;
  final Map<String, dynamic>? filters;

  SearchResponse({
    required this.results,
    required this.total,
    required this.query,
    this.executionTime,
    this.filters,
  });

  factory SearchResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final resultsList = (json['results'] as List?)
        ?.map((item) => fromJsonT(item as Map<String, dynamic>))
        .toList() ?? [];

    return SearchResponse<T>(
      results: resultsList,
      total: json['total'] ?? 0,
      query: json['query'] ?? '',
      executionTime: json['execution_time']?.toDouble(),
      filters: json['filters'],
    );
  }

  bool get hasResults => results.isNotEmpty;
  bool get isEmpty => results.isEmpty;

  @override
  String toString() {
    return 'SearchResponse(results: ${results.length}, total: $total, query: "$query")';
  }
}
