import 'package:flutter/material.dart';
import '../services/vehicle_parts_service.dart';
import '../models/vehicle.dart';

class VehiclePartsFinderWidget extends StatefulWidget {
  const VehiclePartsFinderWidget({super.key});

  @override
  State<VehiclePartsFinderWidget> createState() => _VehiclePartsFinderWidgetState();
}

class _VehiclePartsFinderWidgetState extends State<VehiclePartsFinderWidget> {
  final VehiclePartsService _vehiclePartsService = VehiclePartsService();
  
  VehicleCategory? _selectedCategory;
  VehicleMake? _selectedMake;
  VehicleModel? _selectedModel;
  int? _selectedYear;

  List<VehicleCategory> _categories = [];
  List<VehicleMake> _makes = [];
  List<VehicleModel> _models = [];
  List<int> _years = [];
  
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _loadMakes();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF1976D2),
            const Color(0xFF1976D2).withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF1976D2).withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان والوصف
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.search,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'باحث قطع الغيار',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'ابحث عن قطع الغيار المناسبة لسيارتك',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // نموذج البحث
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          else
            _buildSearchForm(),
          
          const SizedBox(height: 16),
          
          // زر البحث
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _canSearch() ? _performSearch : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF1976D2),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.search, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'البحث عن قطع الغيار',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // روابط سريعة
          _buildQuickLinks(),
        ],
      ),
    );
  }

  Widget _buildSearchForm() {
    return Column(
      children: [
        // نوع المركبة
        _buildDropdown<VehicleCategory>(
          label: 'نوع المركبة',
          value: _selectedCategory,
          items: _categories,
          itemBuilder: (category) => category.name,
          onChanged: (category) {
            setState(() {
              _selectedCategory = category;
              _selectedMake = null;
              _selectedModel = null;
              _selectedYear = null;
              _models.clear();
              _years.clear();
            });
          },
          icon: Icons.directions_car,
        ),

        const SizedBox(height: 12),

        // الماركة
        _buildDropdown<VehicleMake>(
          label: 'الماركة',
          value: _selectedMake,
          items: _makes,
          itemBuilder: (make) => make.name,
          onChanged: (make) {
            setState(() {
              _selectedMake = make;
              _selectedModel = null;
              _selectedYear = null;
              _years.clear();
            });
            if (make != null) {
              _loadModels(make.id);
            }
          },
          icon: Icons.business,
        ),

        const SizedBox(height: 12),

        // الموديل
        _buildDropdown<VehicleModel>(
          label: 'الموديل',
          value: _selectedModel,
          items: _models,
          itemBuilder: (model) => model.name,
          onChanged: (model) {
            setState(() {
              _selectedModel = model;
              _selectedYear = null;
            });
            if (model != null) {
              _loadYears(model.id);
            }
          },
          icon: Icons.car_rental,
          enabled: _selectedMake != null,
        ),
        
        const SizedBox(height: 12),
        
        // السنة
        _buildDropdown<int>(
          label: 'سنة الصنع',
          value: _selectedYear,
          items: _years,
          itemBuilder: (year) => year.toString(),
          onChanged: (year) {
            setState(() {
              _selectedYear = year;
            });
          },
          icon: Icons.calendar_today,
          enabled: _selectedModel != null,
        ),
      ],
    );
  }

  Widget _buildDropdown<T>({
    required String label,
    required T? value,
    required List<T> items,
    required String Function(T) itemBuilder,
    required void Function(T?) onChanged,
    required IconData icon,
    bool enabled = true,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: DropdownButtonFormField<T>(
        value: value,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(color: Colors.white),
          prefixIcon: Icon(icon, color: Colors.white, size: 20),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        dropdownColor: Colors.white,
        style: const TextStyle(color: Colors.black),
        icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
        items: items.map((item) {
          return DropdownMenuItem<T>(
            value: item,
            child: Text(itemBuilder(item)),
          );
        }).toList(),
        onChanged: enabled ? onChanged : null,
      ),
    );
  }

  Widget _buildQuickLinks() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildQuickLink(
          icon: Icons.favorite,
          label: 'سياراتي',
          onTap: _showMySavedVehicles,
        ),
        _buildQuickLink(
          icon: Icons.qr_code_scanner,
          label: 'مسح QR',
          onTap: _scanQRCode,
        ),
        _buildQuickLink(
          icon: Icons.local_offer,
          label: 'العروض',
          onTap: _showSpecialOffers,
        ),
      ],
    );
  }

  Widget _buildQuickLink({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 16),
            const SizedBox(width: 6),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // === دوال التحميل ===

  Future<void> _loadCategories() async {
    setState(() => _isLoading = true);
    
    try {
      final response = await _vehiclePartsService.getRootCategories();
      if (response.isSuccess && response.data != null) {
        setState(() {
          _categories = response.data!;
        });
      }
    } catch (e) {
      _showError('فشل في تحميل فئات المركبات');
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _loadMakes() async {
    try {
      final response = await _vehiclePartsService.getVehicleMakes();
      if (response.isSuccess && response.data != null) {
        setState(() {
          _makes = response.data!;
        });
      }
    } catch (e) {
      _showError('فشل في تحميل ماركات السيارات');
    }
  }

  Future<void> _loadModels(int makeId) async {
    try {
      final response = await _vehiclePartsService.getVehicleModels(makeId);
      if (response.isSuccess && response.data != null) {
        setState(() {
          _models = response.data!;
        });
      }
    } catch (e) {
      _showError('فشل في تحميل موديلات السيارة');
    }
  }

  Future<void> _loadYears(int modelId) async {
    try {
      final response = await _vehiclePartsService.getVehicleYears(modelId);
      if (response.isSuccess && response.data != null) {
        setState(() {
          _years = response.data!;
        });
      }
    } catch (e) {
      _showError('فشل في تحميل سنوات الموديل');
    }
  }

  // === دوال الأحداث ===

  bool _canSearch() {
    return _selectedCategory != null || 
           _selectedMake != null || 
           _selectedModel != null || 
           _selectedYear != null;
  }

  void _performSearch() {
    if (!_canSearch()) return;

    // تنفيذ البحث
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'البحث عن قطع غيار ${_selectedMake?.name ?? ''} ${_selectedModel?.name ?? ''} ${_selectedYear ?? ''}',
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _showMySavedVehicles() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('عرض السيارات المحفوظة'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _scanQRCode() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('فتح ماسح QR Code'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showSpecialOffers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('عرض العروض الخاصة'),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
