import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import '../providers/vehicle_parts_provider.dart';
import '../widgets/vehicle_type_selector.dart';
import '../widgets/make_selector.dart';
import '../widgets/model_selector.dart';
import '../widgets/year_selector.dart';
import '../widgets/quick_search_bar.dart';
import '../widgets/saved_vehicles_section.dart';
import '../widgets/popular_parts_section.dart';
import '../../shared/widgets/loading_widget.dart';
import '../../shared/widgets/error_widget.dart';

class VehiclePartsFinderScreen extends StatefulWidget {
  const VehiclePartsFinderScreen({super.key});

  @override
  State<VehiclePartsFinderScreen> createState() => _VehiclePartsFinderScreenState();
}

class _VehiclePartsFinderScreenState extends State<VehiclePartsFinderScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<VehiclePartsProvider>().loadInitialData();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<VehiclePartsProvider>(
        builder: (context, provider, child) {
          return CustomScrollView(
            slivers: [
              // شريط التطبيق المخصص
              SliverAppBar(
                expandedHeight: 200,
                floating: false,
                pinned: true,
                backgroundColor: Theme.of(context).primaryColor,
                flexibleSpace: FlexibleSpaceBar(
                  title: const Text(
                    'باحث قطع الغيار',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Theme.of(context).primaryColor,
                          Theme.of(context).primaryColor.withOpacity(0.8),
                        ],
                      ),
                    ),
                    child: Stack(
                      children: [
                        // خلفية مزخرفة
                        Positioned(
                          top: -50,
                          right: -50,
                          child: Container(
                            width: 150,
                            height: 150,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withOpacity(0.1),
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: -30,
                          left: -30,
                          child: Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withOpacity(0.05),
                            ),
                          ),
                        ),
                        // أيقونة السيارة
                        const Center(
                          child: Icon(
                            Icons.car_repair,
                            size: 80,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.garage, color: Colors.white),
                    onPressed: () => context.push('/vehicle-parts-finder/my-vehicles'),
                    tooltip: 'سياراتي المحفوظة',
                  ),
                  IconButton(
                    icon: const Icon(Icons.help_outline, color: Colors.white),
                    onPressed: () => _showHelpDialog(),
                    tooltip: 'المساعدة',
                  ),
                ],
              ),

              // المحتوى الرئيسي
              SliverToBoxAdapter(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // شريط البحث السريع
                          const QuickSearchBar(),
                          
                          const SizedBox(height: 24),
                          
                          // عنوان القسم
                          Row(
                            children: [
                              Icon(
                                Icons.search,
                                color: Theme.of(context).primaryColor,
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'ابحث عن قطع الغيار',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 8),
                          
                          Text(
                            'اختر نوع وماركة وموديل سيارتك للعثور على قطع الغيار المناسبة',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // محدد نوع المركبة
                          const VehicleTypeSelector(),
                          
                          const SizedBox(height: 16),
                          
                          // محدد الماركة
                          const MakeSelector(),
                          
                          const SizedBox(height: 16),
                          
                          // محدد الموديل
                          const ModelSelector(),
                          
                          const SizedBox(height: 16),
                          
                          // محدد السنة
                          const YearSelector(),
                          
                          const SizedBox(height: 32),
                          
                          // زر البحث
                          if (provider.canSearch)
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: provider.isLoading 
                                    ? null 
                                    : () => _performSearch(provider),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Theme.of(context).primaryColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                icon: provider.isLoading
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                        ),
                                      )
                                    : const Icon(Icons.search),
                                label: Text(
                                  provider.isLoading ? 'جاري البحث...' : 'ابحث عن قطع الغيار',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          
                          const SizedBox(height: 32),
                          
                          // السيارات المحفوظة
                          const SavedVehiclesSection(),
                          
                          const SizedBox(height: 32),
                          
                          // قطع الغيار الشائعة
                          const PopularPartsSection(),
                          
                          const SizedBox(height: 100), // مساحة إضافية للتنقل السفلي
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _performSearch(VehiclePartsProvider provider) {
    final vehicleData = {
      'vehicle_type_id': provider.selectedVehicleType?.id,
      'make_id': provider.selectedMake?.id,
      'model_id': provider.selectedModel?.id,
      'year_id': provider.selectedYear?.id,
    };

    context.push(
      '/vehicle-parts-finder/results',
      extra: {'vehicleData': vehicleData},
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.help_outline,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 8),
            const Text('كيفية استخدام باحث قطع الغيار'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '1. اختر نوع المركبة (سيارة، شاحنة، دراجة نارية)',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 8),
              Text(
                '2. اختر ماركة السيارة من القائمة',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 8),
              Text(
                '3. اختر موديل السيارة',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 8),
              Text(
                '4. اختر سنة الصنع',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 8),
              Text(
                '5. اضغط على "ابحث عن قطع الغيار"',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 16),
              Text(
                'يمكنك أيضاً:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                '• حفظ سياراتك للوصول السريع',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                '• استخدام البحث السريع',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                '• تصفح قطع الغيار الشائعة',
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }
}
