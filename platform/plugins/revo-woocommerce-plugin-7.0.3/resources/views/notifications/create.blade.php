@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        إرسال إشعار جديد
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('revo-woocommerce.notifications.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="ti ti-brand-firebase me-2"></i>
                        <strong>تنبيه:</strong> تأكد من تكوين Firebase Server Key في الإعدادات لإرسال الإشعارات.
                    </div>

                    {!! Form::open(['route' => 'revo-woocommerce.notifications.store', 'method' => 'POST', 'class' => 'needs-validation', 'novalidate' => true]) !!}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label for="title" class="form-label">
                                    <i class="ti ti-heading me-1"></i>
                                    عنوان الإشعار
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="{{ old('title') }}" 
                                       placeholder="مثال: عرض خاص - خصم 50%" required>
                                <div class="form-text">العنوان الذي سيظهر في الإشعار</div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="type" class="form-label">
                                    <i class="ti ti-category me-1"></i>
                                    نوع الإشعار
                                    <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="type" name="type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="general" {{ old('type') == 'general' ? 'selected' : '' }}>عام</option>
                                    <option value="order" {{ old('type') == 'order' ? 'selected' : '' }}>طلب</option>
                                    <option value="promotion" {{ old('type') == 'promotion' ? 'selected' : '' }}>ترويجي</option>
                                    <option value="system" {{ old('type') == 'system' ? 'selected' : '' }}>نظام</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="message" class="form-label">
                            <i class="ti ti-message me-1"></i>
                            نص الإشعار
                            <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="message" name="message" rows="4" 
                                  placeholder="أدخل نص الإشعار الذي سيظهر للمستخدمين" required>{{ old('message') }}</textarea>
                        <div class="form-text">النص الذي سيظهر في الإشعار (أقل من 1000 حرف)</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="target_audience" class="form-label">
                                    <i class="ti ti-users me-1"></i>
                                    الجمهور المستهدف
                                    <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="target_audience" name="target_audience" required>
                                    <option value="">اختر الجمهور</option>
                                    <option value="all" {{ old('target_audience') == 'all' ? 'selected' : '' }}>جميع المستخدمين</option>
                                    <option value="customers" {{ old('target_audience') == 'customers' ? 'selected' : '' }}>العملاء فقط</option>
                                    <option value="specific" {{ old('target_audience') == 'specific' ? 'selected' : '' }}>مستخدمين محددين</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="schedule_at" class="form-label">
                                    <i class="ti ti-clock me-1"></i>
                                    جدولة الإرسال
                                </label>
                                <input type="datetime-local" class="form-control" id="schedule_at" name="schedule_at" 
                                       value="{{ old('schedule_at') }}">
                                <div class="form-text">اتركه فارغاً للإرسال الفوري</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="image" class="form-label">
                                    <i class="ti ti-photo me-1"></i>
                                    صورة الإشعار
                                </label>
                                <input type="url" class="form-control" id="image" name="image" 
                                       value="{{ old('image') }}" 
                                       placeholder="https://example.com/notification.jpg">
                                <div class="form-text">صورة اختيارية تظهر مع الإشعار</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="action_url" class="form-label">
                                    <i class="ti ti-link me-1"></i>
                                    رابط الإجراء
                                </label>
                                <input type="url" class="form-control" id="action_url" name="action_url" 
                                       value="{{ old('action_url') }}" 
                                       placeholder="https://example.com/action">
                                <div class="form-text">الرابط الذي سينتقل إليه المستخدم عند النقر</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <button type="submit" class="btn btn-success btn-lg me-3">
                                        <i class="ti ti-send me-2"></i>
                                        إرسال الإشعار
                                    </button>
                                    <a href="{{ route('revo-woocommerce.notifications.index') }}" class="btn btn-secondary btn-lg">
                                        <i class="ti ti-x me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
    $(document).ready(function() {
        // Form validation
        $('.needs-validation').on('submit', function(e) {
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            $(this).addClass('was-validated');
        });
        
        // Character counter for message
        $('#message').on('input', function() {
            const length = $(this).val().length;
            const maxLength = 1000;
            const remaining = maxLength - length;
            
            if (remaining < 0) {
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
    });
</script>
@endpush
