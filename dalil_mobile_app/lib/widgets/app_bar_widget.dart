import 'package:flutter/material.dart';
import '../services/auth_service_new.dart';

class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  const CustomAppBar({super.key});

  @override
  State<CustomAppBar> createState() => _CustomAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _CustomAppBarState extends State<CustomAppBar> {
  final AuthServiceNew _authService = AuthServiceNew();
  String _currentLanguage = 'العربية';

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Row(
        children: [
          // لوجو التطبيق
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.directions_car,
              color: Color(0xFF1976D2),
              size: 20,
            ),
          ),
          const SizedBox(width: 8),
          const Text(
            'دليل',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
        ],
      ),
      actions: [
        // أيقونة البحث
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            _showSearchDialog(context);
          },
          tooltip: 'البحث',
        ),
        
        // أيقونة الإشعارات
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications_outlined),
              onPressed: () {
                _showNotifications(context);
              },
              tooltip: 'الإشعارات',
            ),
            // نقطة الإشعارات الجديدة
            Positioned(
              right: 8,
              top: 8,
              child: Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        ),
        
        // قائمة اللغات
        PopupMenuButton<String>(
          icon: const Icon(Icons.language),
          tooltip: 'تغيير اللغة',
          onSelected: _changeLanguage,
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'العربية',
              child: Row(
                children: [
                  const Text('🇸🇦'),
                  const SizedBox(width: 8),
                  const Text('العربية'),
                  if (_currentLanguage == 'العربية')
                    const Spacer(),
                  if (_currentLanguage == 'العربية')
                    const Icon(Icons.check, color: Colors.green, size: 16),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'English',
              child: Row(
                children: [
                  const Text('🇺🇸'),
                  const SizedBox(width: 8),
                  const Text('English'),
                  if (_currentLanguage == 'English')
                    const Spacer(),
                  if (_currentLanguage == 'English')
                    const Icon(Icons.check, color: Colors.green, size: 16),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'کوردی',
              child: Row(
                children: [
                  const Text('🏴'),
                  const SizedBox(width: 8),
                  const Text('کوردی'),
                  if (_currentLanguage == 'کوردی')
                    const Spacer(),
                  if (_currentLanguage == 'کوردی')
                    const Icon(Icons.check, color: Colors.green, size: 16),
                ],
              ),
            ),
          ],
        ),
        
        // قائمة المستخدم
        PopupMenuButton<String>(
          icon: CircleAvatar(
            radius: 16,
            backgroundColor: Colors.white.withOpacity(0.2),
            child: Icon(
              _authService.isLoggedIn ? Icons.person : Icons.person_outline,
              color: Colors.white,
              size: 20,
            ),
          ),
          tooltip: _authService.isLoggedIn ? 'حسابي' : 'تسجيل الدخول',
          onSelected: _handleUserAction,
          itemBuilder: (context) => _authService.isLoggedIn
              ? [
                  PopupMenuItem(
                    value: 'profile',
                    child: Row(
                      children: [
                        const Icon(Icons.person, size: 20),
                        const SizedBox(width: 8),
                        Text(_authService.currentUser?.displayName ?? 'الملف الشخصي'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'orders',
                    child: Row(
                      children: [
                        Icon(Icons.shopping_bag, size: 20),
                        SizedBox(width: 8),
                        Text('طلباتي'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'wishlist',
                    child: Row(
                      children: [
                        Icon(Icons.favorite, size: 20),
                        SizedBox(width: 8),
                        Text('المفضلة'),
                      ],
                    ),
                  ),
                  if (_authService.isWholesaleCustomer)
                    const PopupMenuItem(
                      value: 'wholesale',
                      child: Row(
                        children: [
                          Icon(Icons.business, size: 20),
                          SizedBox(width: 8),
                          Text('حساب الجملة'),
                        ],
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: Row(
                      children: [
                        Icon(Icons.settings, size: 20),
                        SizedBox(width: 8),
                        Text('الإعدادات'),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout, size: 20, color: Colors.red),
                        SizedBox(width: 8),
                        Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ]
              : [
                  const PopupMenuItem(
                    value: 'login',
                    child: Row(
                      children: [
                        Icon(Icons.login, size: 20),
                        SizedBox(width: 8),
                        Text('تسجيل الدخول'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'register',
                    child: Row(
                      children: [
                        Icon(Icons.person_add, size: 20),
                        SizedBox(width: 8),
                        Text('إنشاء حساب'),
                      ],
                    ),
                  ),
                ],
        ),
      ],
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.1),
    );
  }

  void _changeLanguage(String language) {
    setState(() {
      _currentLanguage = language;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تغيير اللغة إلى: $language'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _handleUserAction(String action) {
    switch (action) {
      case 'login':
        _showLoginDialog();
        break;
      case 'register':
        _showRegisterDialog();
        break;
      case 'profile':
        _navigateToProfile();
        break;
      case 'orders':
        _navigateToOrders();
        break;
      case 'wishlist':
        _navigateToWishlist();
        break;
      case 'wholesale':
        _navigateToWholesale();
        break;
      case 'settings':
        _navigateToSettings();
        break;
      case 'logout':
        _logout();
        break;
    }
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: 'ابحث عن قطع الغيار...',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('جاري البحث...'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  void _showNotifications(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('لا توجد إشعارات جديدة'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showLoginDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم فتح صفحة تسجيل الدخول قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showRegisterDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم فتح صفحة إنشاء الحساب قريباً'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _navigateToProfile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال للملف الشخصي'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _navigateToOrders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('عرض الطلبات'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _navigateToWishlist() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('عرض المفضلة'),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _navigateToWholesale() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حساب الجملة'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  void _navigateToSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الإعدادات'),
        backgroundColor: Colors.grey,
      ),
    );
  }

  void _logout() async {
    final result = await _authService.logout();
    if (result.isSuccess) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تسجيل الخروج بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        setState(() {}); // تحديث الواجهة
      }
    }
  }
}
