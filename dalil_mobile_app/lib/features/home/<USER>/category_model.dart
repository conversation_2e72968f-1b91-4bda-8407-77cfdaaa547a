class CategoryModel {
  final int id;
  final String name;
  final String? description;
  final String? imageUrl;
  final String? iconUrl;
  final String slug;
  final int? parentId;
  final bool isActive;
  final int order;
  final int productsCount;
  final List<CategoryModel> children;
  final DateTime? createdAt;

  CategoryModel({
    required this.id,
    required this.name,
    this.description,
    this.imageUrl,
    this.iconUrl,
    required this.slug,
    this.parentId,
    this.isActive = true,
    this.order = 0,
    this.productsCount = 0,
    this.children = const [],
    this.createdAt,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      iconUrl: json['icon_url'] as String?,
      slug: json['slug'] as String,
      parentId: json['parent_id'] as int?,
      isActive: json['is_active'] as bool? ?? true,
      order: json['order'] as int? ?? 0,
      productsCount: json['products_count'] as int? ?? 0,
      children: json['children'] != null
          ? (json['children'] as List)
              .map((child) => CategoryModel.fromJson(child))
              .toList()
          : [],
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image_url': imageUrl,
      'icon_url': iconUrl,
      'slug': slug,
      'parent_id': parentId,
      'is_active': isActive,
      'order': order,
      'products_count': productsCount,
      'children': children.map((child) => child.toJson()).toList(),
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // التحقق من كون الفئة جذرية
  bool get isRoot => parentId == null;

  // التحقق من وجود فئات فرعية
  bool get hasChildren => children.isNotEmpty;

  // الحصول على مستوى الفئة
  int get level {
    if (isRoot) return 0;
    // يمكن حساب المستوى بناءً على الهيكل الهرمي
    return 1; // مبسط للمثال
  }

  // الحصول على الصورة المناسبة للعرض
  String? get displayImage => imageUrl ?? iconUrl;

  // الحصول على عدد المنتجات مع الفئات الفرعية
  int get totalProductsCount {
    int total = productsCount;
    for (final child in children) {
      total += child.totalProductsCount;
    }
    return total;
  }

  // البحث في الفئات الفرعية
  CategoryModel? findChildById(int childId) {
    for (final child in children) {
      if (child.id == childId) {
        return child;
      }
      final found = child.findChildById(childId);
      if (found != null) {
        return found;
      }
    }
    return null;
  }

  // الحصول على جميع الفئات الفرعية (مسطحة)
  List<CategoryModel> get allChildren {
    List<CategoryModel> result = [];
    for (final child in children) {
      result.add(child);
      result.addAll(child.allChildren);
    }
    return result;
  }

  // الحصول على مسار الفئة (breadcrumb)
  List<CategoryModel> getBreadcrumb() {
    // هذا يتطلب معرفة الفئة الأب، مبسط للمثال
    return [this];
  }

  CategoryModel copyWith({
    int? id,
    String? name,
    String? description,
    String? imageUrl,
    String? iconUrl,
    String? slug,
    int? parentId,
    bool? isActive,
    int? order,
    int? productsCount,
    List<CategoryModel>? children,
    DateTime? createdAt,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      iconUrl: iconUrl ?? this.iconUrl,
      slug: slug ?? this.slug,
      parentId: parentId ?? this.parentId,
      isActive: isActive ?? this.isActive,
      order: order ?? this.order,
      productsCount: productsCount ?? this.productsCount,
      children: children ?? this.children,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'CategoryModel(id: $id, name: $name, slug: $slug)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
