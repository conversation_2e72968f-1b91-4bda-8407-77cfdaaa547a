<?php

if (!function_exists('revo_woocommerce_setting')) {
    /**
     * Get RevoWoocommerce setting value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function revo_woocommerce_setting(string $key, $default = null)
    {
        return setting('revo_woocommerce_' . $key, $default);
    }
}

if (!function_exists('revo_woocommerce_set_setting')) {
    /**
     * Set RevoWoocommerce setting value
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    function revo_woocommerce_set_setting(string $key, $value): void
    {
        setting()->set('revo_woocommerce_' . $key, $value);
        setting()->save();
    }
}

if (!function_exists('revo_woocommerce_config')) {
    /**
     * Get RevoWoocommerce config value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function revo_woocommerce_config(string $key, $default = null)
    {
        return config('plugins.revo-woocommerce-plugin-7.0.3.general.' . $key, $default);
    }
}

if (!function_exists('revo_woocommerce_asset')) {
    /**
     * Get RevoWoocommerce asset URL
     *
     * @param string $path
     * @return string
     */
    function revo_woocommerce_asset(string $path): string
    {
        return asset('vendor/core/plugins/revo-woocommerce-plugin-7.0.3/' . ltrim($path, '/'));
    }
}

if (!function_exists('revo_woocommerce_view')) {
    /**
     * Get RevoWoocommerce view path
     *
     * @param string $view
     * @return string
     */
    function revo_woocommerce_view(string $view): string
    {
        return 'plugins/revo-woocommerce-plugin-7.0.3::' . $view;
    }
}

if (!function_exists('revo_woocommerce_trans')) {
    /**
     * Get RevoWoocommerce translation
     *
     * @param string $key
     * @param array $replace
     * @return string
     */
    function revo_woocommerce_trans(string $key, array $replace = []): string
    {
        return trans('plugins/revo-woocommerce-plugin-7.0.3::revo-woocommerce.' . $key, $replace);
    }
}

if (!function_exists('revo_woocommerce_route')) {
    /**
     * Get RevoWoocommerce route URL
     *
     * @param string $name
     * @param array $parameters
     * @return string
     */
    function revo_woocommerce_route(string $name, array $parameters = []): string
    {
        return route('revo-woocommerce.' . $name, $parameters);
    }
}

if (!function_exists('is_revo_woocommerce_enabled')) {
    /**
     * Check if RevoWoocommerce is enabled
     *
     * @return bool
     */
    function is_revo_woocommerce_enabled(): bool
    {
        return is_plugin_active('revo-woocommerce-plugin-7.0.3') && is_plugin_active('ecommerce');
    }
}
