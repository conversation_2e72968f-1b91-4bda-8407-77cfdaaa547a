<?php

namespace Bo<PERSON>ble\Ecommerce\Models;

use Botble\Base\Facades\MacroableModels;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Bo<PERSON>ble\Base\Models\BaseQueryBuilder;
use Botble\Base\Supports\Avatar;
use Botble\Ecommerce\Enums\CustomerStatusEnum;
use Botble\Ecommerce\Enums\DiscountTypeEnum;
use Botble\Ecommerce\Notifications\ConfirmEmailNotification;
use Botble\Ecommerce\Notifications\ResetPasswordNotification;
use Botble\Media\Facades\RvMedia;
use Botble\Payment\Models\Payment;
use Carbon\Carbon;
use Exception;
use Illuminate\Auth\Authenticatable;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Laravel\Sanctum\HasApiTokens;

class Customer extends BaseModel implements
    AuthenticatableContract,
    AuthorizableContract,
    CanResetPasswordContract
{
    use Authenticatable;
    use Authorizable;
    use CanResetPassword;
    use MustVerifyEmail;
    use HasApiTokens;
    use Notifiable;

    protected $table = 'ec_customers';

    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar',
        'phone',
        'phone_country_code',
        'phone_verified_at',
        'two_factor_enabled',
        'last_otp_sent_at',
        'status',
        'private_notes',
        'is_approved',
        'is_wholesale',
        'company',
        'tax_id',
        'business_license',
        'wholesale_application_status',
        'wholesale_application_date',
        'wholesale_approval_date',
        'wholesale_application_notes',
        'business_type',
        'years_in_business',
        'expected_monthly_volume',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'status' => CustomerStatusEnum::class,
        'dob' => 'date',
        'phone_verified_at' => 'datetime',
        'last_otp_sent_at' => 'datetime',
        'two_factor_enabled' => 'boolean',
        'wholesale_application_date' => 'datetime',
        'wholesale_approval_date' => 'datetime',
        'is_wholesale' => 'boolean',
        'expected_monthly_volume' => 'decimal:2',
    ];

    public function sendPasswordResetNotification($token): void
    {
        $this->notify(new ResetPasswordNotification($token));
    }

    public function sendEmailVerificationNotification(): void
    {
        $this->notify(new ConfirmEmailNotification());
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'user_id', 'id');
    }

    public function completedOrders(): HasMany
    {
        return $this->orders()->whereNotNull('completed_at');
    }

    public function addresses(): HasMany
    {
        return $this
            ->hasMany(Address::class, 'customer_id', 'id')
            ->when(is_plugin_active('location'), function (HasMany|BaseQueryBuilder $query) {
                return $query->with(['locationCountry', 'locationState', 'locationCity']);
            });
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'customer_id', 'id');
    }

    public function discounts(): BelongsToMany
    {
        return $this->belongsToMany(Discount::class, 'ec_discount_customers', 'customer_id', 'id');
    }

    public function wishlist(): HasMany
    {
        return $this->hasMany(Wishlist::class, 'customer_id');
    }

    protected static function booted(): void
    {
        self::deleted(function (Customer $customer): void {
            $customer->discounts()->detach();
            $customer->usedCoupons()->detach();
            $customer->orders()->update(['user_id' => 0]);
            $customer->addresses()->delete();
            $customer->wishlist()->delete();
            $customer->reviews()->each(fn (Review $review) => $review->delete());
        });

        static::deleted(function (Customer $customer): void {
            $folder = Storage::path($customer->upload_folder);
            if (File::isDirectory($folder) && Str::endsWith($customer->upload_folder, '/' . $customer->id)) {
                File::deleteDirectory($folder);
            }
        });
    }

    public function __get($key)
    {
        if (class_exists('MacroableModels')) {
            $method = 'get' . Str::studly($key) . 'Attribute';
            if (MacroableModels::modelHasMacro(get_class($this), $method)) {
                return call_user_func([$this, $method]);
            }
        }

        return parent::__get($key);
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class, 'customer_id');
    }

    public function promotions(): BelongsToMany
    {
        return $this
            ->belongsToMany(Discount::class, 'ec_discount_customers', 'customer_id')
            ->where('type', DiscountTypeEnum::PROMOTION)
            ->where('start_date', '<=', Carbon::now())
            ->where('target', 'customer')
            ->where(function ($query) {
                return $query
                    ->whereNull('end_date')
                    ->orWhere('end_date', '>=', Carbon::now());
            })
            ->where('product_quantity', 1);
    }

    public function viewedProducts(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'ec_customer_recently_viewed_products');
    }

    public function usedCoupons(): BelongsToMany
    {
        return $this->belongsToMany(Discount::class, 'ec_customer_used_coupons');
    }

    public function deletionRequest(): HasOne
    {
        return $this->hasOne(CustomerDeletionRequest::class, 'customer_id');
    }

    /**
     * العلاقة مع تأكيدات OTP
     */
    public function otpVerifications(): HasMany
    {
        return $this->hasMany(\App\Models\CustomerOtpVerification::class);
    }

    /**
     * التحقق من تأكيد رقم الهاتف
     */
    public function hasVerifiedPhone(): bool
    {
        return !is_null($this->phone_verified_at);
    }

    /**
     * تحديد رقم الهاتف كمؤكد
     */
    public function markPhoneAsVerified(): void
    {
        $this->update(['phone_verified_at' => now()]);
    }

    /**
     * الحصول على رقم الهاتف الكامل مع رمز البلد
     */
    public function getFullPhoneAttribute(): string
    {
        return ($this->phone_country_code ?? '+964') . $this->phone;
    }

    /**
     * التحقق من إمكانية إرسال OTP
     */
    public function canSendOtp(): bool
    {
        if (!$this->phone) {
            return false;
        }

        // التحقق من الحد الزمني بين الرسائل (دقيقة واحدة على الأقل)
        if ($this->last_otp_sent_at && $this->last_otp_sent_at->diffInMinutes(now()) < 1) {
            return false;
        }

        return true;
    }

    /**
     * تسجيل إرسال OTP
     */
    public function recordOtpSent(): void
    {
        $this->update(['last_otp_sent_at' => now()]);
    }

    protected function avatarUrl(): Attribute
    {
        return Attribute::get(function () {
            if ($this->avatar) {
                return RvMedia::getImageUrl($this->avatar, 'thumb');
            }

            if ($defaultAvatar = get_ecommerce_setting('customer_default_avatar')) {
                return RvMedia::getImageUrl($defaultAvatar);
            }

            try {
                return (new Avatar())->create(Str::ucfirst($this->name))->toBase64();
            } catch (Exception) {
                return RvMedia::getDefaultImage();
            }
        });
    }

    protected function uploadFolder(): Attribute
    {
        return Attribute::get(function () {
            $folder = $this->id ? 'customers/' . $this->id : 'customers';

            return apply_filters('ecommerce_customer_upload_folder', $folder, $this);
        });
    }
}
