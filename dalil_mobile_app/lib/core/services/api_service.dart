import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import '../config/app_config.dart';
import 'storage_service.dart';

class ApiService {
  static late Dio _dio;
  static late ApiService _instance;

  ApiService._internal();

  static ApiService get instance => _instance;

  static void init() {
    _instance = ApiService._internal();
    _dio = Dio();
    _setupDio();
  }

  static void _setupDio() {
    _dio.options = BaseOptions(
      baseUrl: AppConfig.baseUrl,
      connectTimeout: Duration(seconds: AppConfig.requestTimeout),
      receiveTimeout: Duration(seconds: AppConfig.requestTimeout),
      sendTimeout: Duration(seconds: AppConfig.requestTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Accept-Language': 'ar',
      },
    );

    // إضافة interceptor للتوكن
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final token = await StorageService.getToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          if (error.response?.statusCode == 401) {
            // إزالة التوكن المنتهي الصلاحية
            await StorageService.removeToken();
            // يمكن إضافة منطق إعادة التوجيه لصفحة تسجيل الدخول
          }
          handler.next(error);
        },
      ),
    );

    // إضافة logger في وضع التطوير
    if (AppConfig.isDebugMode) {
      _dio.interceptors.add(
        PrettyDioLogger(
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
        ),
      );
    }
  }

  // طلبات GET
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
        options: options,
      );
      return ApiResponse<T>.fromResponse(response);
    } on DioException catch (e) {
      return ApiResponse<T>.fromError(e);
    } catch (e) {
      return ApiResponse<T>.fromException(e);
    }
  }

  // طلبات POST
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return ApiResponse<T>.fromResponse(response);
    } on DioException catch (e) {
      return ApiResponse<T>.fromError(e);
    } catch (e) {
      return ApiResponse<T>.fromException(e);
    }
  }

  // طلبات PUT
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return ApiResponse<T>.fromResponse(response);
    } on DioException catch (e) {
      return ApiResponse<T>.fromError(e);
    } catch (e) {
      return ApiResponse<T>.fromException(e);
    }
  }

  // طلبات DELETE
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.delete(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return ApiResponse<T>.fromResponse(response);
    } on DioException catch (e) {
      return ApiResponse<T>.fromError(e);
    } catch (e) {
      return ApiResponse<T>.fromException(e);
    }
  }

  // رفع الملفات
  Future<ApiResponse<T>> uploadFile<T>(
    String endpoint,
    File file, {
    String fieldName = 'file',
    Map<String, dynamic>? additionalData,
    ProgressCallback? onSendProgress,
  }) async {
    try {
      final formData = FormData();
      
      // إضافة الملف
      formData.files.add(
        MapEntry(
          fieldName,
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
          ),
        ),
      );

      // إضافة البيانات الإضافية
      if (additionalData != null) {
        additionalData.forEach((key, value) {
          formData.fields.add(MapEntry(key, value.toString()));
        });
      }

      final response = await _dio.post(
        endpoint,
        data: formData,
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
        ),
        onSendProgress: onSendProgress,
      );

      return ApiResponse<T>.fromResponse(response);
    } on DioException catch (e) {
      return ApiResponse<T>.fromError(e);
    } catch (e) {
      return ApiResponse<T>.fromException(e);
    }
  }
}

// فئة الاستجابة الموحدة
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final int? statusCode;
  final Map<String, dynamic>? errors;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.statusCode,
    this.errors,
  });

  factory ApiResponse.fromResponse(Response response) {
    final responseData = response.data;
    
    return ApiResponse<T>(
      success: response.statusCode == 200 || response.statusCode == 201,
      data: responseData is Map<String, dynamic> 
          ? responseData['data'] as T?
          : responseData as T?,
      message: responseData is Map<String, dynamic> 
          ? responseData['message'] as String?
          : null,
      statusCode: response.statusCode,
    );
  }

  factory ApiResponse.fromError(DioException error) {
    String message = 'حدث خطأ غير متوقع';
    Map<String, dynamic>? errors;

    if (error.response?.data is Map<String, dynamic>) {
      final errorData = error.response!.data as Map<String, dynamic>;
      message = errorData['message'] ?? message;
      errors = errorData['errors'] as Map<String, dynamic>?;
    } else {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          message = 'انتهت مهلة الاتصال';
          break;
        case DioExceptionType.badResponse:
          message = 'خطأ في الخادم';
          break;
        case DioExceptionType.cancel:
          message = 'تم إلغاء الطلب';
          break;
        case DioExceptionType.unknown:
          message = 'تحقق من اتصال الإنترنت';
          break;
        default:
          message = 'حدث خطأ غير متوقع';
      }
    }

    return ApiResponse<T>(
      success: false,
      message: message,
      statusCode: error.response?.statusCode,
      errors: errors,
    );
  }

  factory ApiResponse.fromException(dynamic exception) {
    return ApiResponse<T>(
      success: false,
      message: 'حدث خطأ غير متوقع: ${exception.toString()}',
    );
  }
}
