import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../features/home/<USER>/product_model.dart';

class FavoriteItem {
  final ProductModel product;
  final DateTime addedAt;

  FavoriteItem({
    required this.product,
    required this.addedAt,
  });

  Map<String, dynamic> toJson() => {
    'product': product.toJson(),
    'addedAt': addedAt.toIso8601String(),
  };

  factory FavoriteItem.fromJson(Map<String, dynamic> json) => FavoriteItem(
    product: ProductModel.fromJson(json['product']),
    addedAt: DateTime.parse(json['addedAt']),
  );
}

class FavoritesProvider extends ChangeNotifier {
  static const String _favoritesKey = 'favorites';
  
  final List<FavoriteItem> _favorites = [];
  bool _isLoading = false;

  List<FavoriteItem> get favorites => List.unmodifiable(_favorites);
  List<ProductModel> get favoriteProducts => _favorites.map((f) => f.product).toList();
  bool get isLoading => _isLoading;
  bool get isEmpty => _favorites.isEmpty;
  bool get isNotEmpty => _favorites.isNotEmpty;
  int get count => _favorites.length;

  // تحميل المفضلة المحفوظة
  Future<void> loadFavorites() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList(_favoritesKey) ?? [];
      
      _favorites.clear();
      _favorites.addAll(
        favoritesJson.map((json) => FavoriteItem.fromJson(jsonDecode(json))),
      );
      
      // ترتيب حسب تاريخ الإضافة (الأحدث أولاً)
      _favorites.sort((a, b) => b.addedAt.compareTo(a.addedAt));
      
    } catch (e) {
      debugPrint('Error loading favorites: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // حفظ المفضلة
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = _favorites
          .map((favorite) => json.encode(favorite.toJson()))
          .toList();
      
      await prefs.setStringList(_favoritesKey, favoritesJson);
    } catch (e) {
      debugPrint('Error saving favorites: $e');
    }
  }

  // إضافة منتج للمفضلة
  Future<void> addToFavorites(ProductModel product) async {
    if (isFavorite(product.id)) return;

    final favoriteItem = FavoriteItem(
      product: product,
      addedAt: DateTime.now(),
    );

    _favorites.insert(0, favoriteItem);
    await _saveFavorites();
    notifyListeners();
  }

  // إزالة منتج من المفضلة
  Future<void> removeFromFavorites(int productId) async {
    _favorites.removeWhere((favorite) => favorite.product.id == productId);
    await _saveFavorites();
    notifyListeners();
  }

  // تبديل حالة المفضلة
  Future<void> toggleFavorite(ProductModel product) async {
    if (isFavorite(product.id)) {
      await removeFromFavorites(product.id);
    } else {
      await addToFavorites(product);
    }
  }

  // التحقق من وجود منتج في المفضلة
  bool isFavorite(int productId) {
    return _favorites.any((favorite) => favorite.product.id == productId);
  }

  // مسح جميع المفضلة
  Future<void> clearFavorites() async {
    _favorites.clear();
    await _saveFavorites();
    notifyListeners();
  }

  // الحصول على المفضلة حسب الفئة
  List<FavoriteItem> getFavoritesByCategory(String categoryId) {
    return _favorites.where((favorite) {
      return favorite.product.categoryIds.contains(int.parse(categoryId));
    }).toList();
  }

  // البحث في المفضلة
  List<FavoriteItem> searchFavorites(String query) {
    if (query.isEmpty) return _favorites;
    
    final lowerQuery = query.toLowerCase();
    return _favorites.where((favorite) {
      return favorite.product.name.toLowerCase().contains(lowerQuery) ||
             (favorite.product.description?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  // ترتيب المفضلة
  void sortFavorites(String sortBy, {bool ascending = true}) {
    _favorites.sort((a, b) {
      int comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.product.name.compareTo(b.product.name);
          break;
        case 'price':
          comparison = (a.product.price ?? 0).compareTo(b.product.price ?? 0);
          break;
        case 'rating':
          comparison = a.product.rating.compareTo(b.product.rating);
          break;
        case 'date':
          comparison = a.addedAt.compareTo(b.addedAt);
          break;
        default:
          comparison = 0;
      }
      
      return ascending ? comparison : -comparison;
    });
    
    notifyListeners();
  }

  // الحصول على إحصائيات المفضلة
  Map<String, dynamic> getFavoritesStats() {
    if (_favorites.isEmpty) {
      return {
        'totalCount': 0,
        'averagePrice': 0.0,
        'averageRating': 0.0,
        'inStockCount': 0,
        'categoriesCount': 0,
      };
    }

    final prices = _favorites
        .map((f) => f.product.price ?? 0)
        .where((price) => price > 0)
        .toList();
    
    final ratings = _favorites
        .map((f) => f.product.rating)
        .where((rating) => rating > 0)
        .toList();

    final categories = <int>{};
    for (var favorite in _favorites) {
      categories.addAll(favorite.product.categoryIds);
    }

    return {
      'totalCount': _favorites.length,
      'averagePrice': prices.isEmpty 
          ? 0.0 
          : prices.reduce((a, b) => a + b) / prices.length,
      'averageRating': ratings.isEmpty 
          ? 0.0 
          : ratings.reduce((a, b) => a + b) / ratings.length,
      'inStockCount': _favorites.where((f) => f.product.inStock).length,
      'categoriesCount': categories.length,
    };
  }

  // الحصول على المفضلة الأحدث
  List<FavoriteItem> getRecentFavorites([int limit = 5]) {
    return _favorites.take(limit).toList();
  }

  // الحصول على المفضلة حسب السعر
  List<FavoriteItem> getFavoritesByPriceRange(double minPrice, double maxPrice) {
    return _favorites.where((favorite) {
      final price = favorite.product.price ?? 0;
      return price >= minPrice && price <= maxPrice;
    }).toList();
  }

  // الحصول على المفضلة المتوفرة فقط
  List<FavoriteItem> getInStockFavorites() {
    return _favorites.where((favorite) => favorite.product.inStock).toList();
  }

  // الحصول على المفضلة عالية التقييم
  List<FavoriteItem> getHighRatedFavorites([double minRating = 4.0]) {
    return _favorites.where((favorite) => favorite.product.rating >= minRating).toList();
  }

  // تصدير المفضلة كنص
  String exportFavoritesAsText() {
    if (_favorites.isEmpty) return 'لا توجد منتجات مفضلة';

    final buffer = StringBuffer();
    buffer.writeln('قائمة المنتجات المفضلة');
    buffer.writeln('=' * 30);
    
    for (int i = 0; i < _favorites.length; i++) {
      final favorite = _favorites[i];
      buffer.writeln('${i + 1}. ${favorite.product.name}');
      buffer.writeln('   السعر: ${favorite.product.price ?? 'غير محدد'} ريال');
      buffer.writeln('   التقييم: ${favorite.product.rating}/5');
      buffer.writeln('   تاريخ الإضافة: ${_formatDate(favorite.addedAt)}');
      buffer.writeln();
    }
    
    return buffer.toString();
  }

  // تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // إحصائيات سريعة للعرض
  String getQuickStats() {
    final stats = getFavoritesStats();
    return '${stats['totalCount']} منتج • '
           '${stats['inStockCount']} متوفر • '
           'متوسط السعر ${stats['averagePrice'].toStringAsFixed(0)} ر.س';
  }

  // التحقق من وجود منتجات منتهية الصلاحية (إذا كان هناك تاريخ انتهاء)
  List<FavoriteItem> getExpiredFavorites() {
    // يمكن تطبيق منطق التحقق من انتهاء الصلاحية هنا
    // مثلاً إذا كان المنتج غير متوفر لفترة طويلة
    return [];
  }

  // اقتراح منتجات مشابهة للمفضلة
  List<ProductModel> getSuggestedProducts() {
    // يمكن تطبيق منطق اقتراح المنتجات بناءً على المفضلة
    // مثلاً منتجات من نفس الفئات أو بأسعار مشابهة
    return [];
  }

  // مزامنة المفضلة مع الخادم (للمستخدمين المسجلين)
  Future<bool> syncWithServer() async {
    try {
      // يمكن إضافة منطق المزامنة مع الخادم هنا
      // مثل إرسال POST request مع قائمة المفضلة
      
      await Future.delayed(const Duration(seconds: 1)); // محاكاة
      return true;
    } catch (e) {
      debugPrint('Error syncing favorites with server: $e');
      return false;
    }
  }

  // تحميل المفضلة من الخادم
  Future<void> loadFromServer() async {
    try {
      // يمكن إضافة منطق تحميل المفضلة من الخادم هنا
      // مثل إرسال GET request للحصول على المفضلة
      
      await Future.delayed(const Duration(seconds: 1)); // محاكاة
    } catch (e) {
      debugPrint('Error loading favorites from server: $e');
    }
  }
}
