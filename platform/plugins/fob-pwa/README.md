# FOB PWA Support

This is a plugin for Botble CMS that adds Progressive Web App (PWA) support to any theme.

## Requirements

- Botble Core 7.3.0 or higher
- PHP 8.1 or higher

## Installation

1. Download the plugin
2. Extract to `platform/plugins/fob-pwa`
3. Go to Admin -> Plugins and activate "FOB PWA Support"
4. Go to Settings -> PWA to configure your Progressive Web App

## Features

- Add PWA support to any theme
- Customizable PWA settings
- Service worker for offline functionality
- Automatic PWA icon generation from site logo
- Manifest.json generation based on settings

![Screenshot](./art/01.png)

## Usage

1. Go to Settings -> P<PERSON> in the admin panel
2. Enable PWA support
3. Configure your PWA settings:
   - App name and short name
   - Theme color and background color
   - App icon (uses site logo by default)
   - Display mode and orientation

## Contributing

Please see [CONTRIBUTING](CONTRIBUTING.md) for details.

## Security

If you discover any security related issues, <NAME_EMAIL> instead of using the issue tracker.

## Credits

- [Friends Of Botble](https://github.com/FriendsOfBotble)
- [All Contributors](../../contributors)

## License

The MIT License (MIT). Please see [License File](LICENSE) for more information.
