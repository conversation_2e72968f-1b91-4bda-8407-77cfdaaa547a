<?php

use Illuminate\Support\Facades\Route;

// Admin routes for RevoWOO
Route::group([
    'prefix' => 'admin/revo-woocommerce',
    'as' => 'revo-woocommerce.',
    'namespace' => 'Botble\RevoWoocommerce\Http\Controllers',
    'middleware' => ['web', 'core', 'auth']
], function () {
    
    // Main dashboard
    Route::get('/', 'RevoWoocommerceController@index')->name('index');
    
    // Settings
    Route::get('settings', 'RevoWoocommerceController@settings')->name('settings');
    Route::post('settings', 'RevoWoocommerceController@saveSettings')->name('settings.save');
    
    // Home Slider routes
    Route::group(['prefix' => 'home-slider', 'as' => 'home-slider.'], function () {
        Route::get('/', 'HomeSliderController@index')->name('index');
        Route::get('create', 'HomeSliderController@create')->name('create');
        Route::post('/', 'HomeSliderController@store')->name('store');
        Route::get('{id}/edit', 'HomeSliderController@edit')->name('edit');
        Route::put('{id}', 'HomeSliderController@update')->name('update');
        Route::delete('{id}', 'HomeSliderController@destroy')->name('destroy');
    });

    // Categories routes
    Route::group(['prefix' => 'categories', 'as' => 'categories.'], function () {
        Route::get('/', 'CategoriesController@index')->name('index');
        Route::post('update-order', 'CategoriesController@updateOrder')->name('update-order');
        Route::post('{id}/toggle-visibility', 'CategoriesController@toggleVisibility')->name('toggle-visibility');
    });

    // Mini Banner routes
    Route::group(['prefix' => 'mini-banner', 'as' => 'mini-banner.'], function () {
        Route::get('/', 'MiniBannerController@index')->name('index');
        Route::get('create', 'MiniBannerController@create')->name('create');
        Route::post('/', 'MiniBannerController@store')->name('store');
        Route::get('{id}/edit', 'MiniBannerController@edit')->name('edit');
        Route::put('{id}', 'MiniBannerController@update')->name('update');
        Route::delete('{id}', 'MiniBannerController@destroy')->name('destroy');
    });

    // Flash Sale routes
    Route::group(['prefix' => 'flash-sale', 'as' => 'flash-sale.'], function () {
        Route::get('/', 'FlashSaleController@index')->name('index');
        Route::get('create', 'FlashSaleController@create')->name('create');
        Route::post('/', 'FlashSaleController@store')->name('store');
        Route::get('{id}/edit', 'FlashSaleController@edit')->name('edit');
        Route::put('{id}', 'FlashSaleController@update')->name('update');
        Route::delete('{id}', 'FlashSaleController@destroy')->name('destroy');
    });

    // Notifications routes
    Route::group(['prefix' => 'notifications', 'as' => 'notifications.'], function () {
        Route::get('/', 'NotificationsController@index')->name('index');
        Route::get('create', 'NotificationsController@create')->name('create');
        Route::post('/', 'NotificationsController@store')->name('store');
        Route::get('{id}', 'NotificationsController@show')->name('show');
        Route::delete('{id}', 'NotificationsController@destroy')->name('destroy');
        Route::post('{id}/resend', 'NotificationsController@resend')->name('resend');
    });

    // Intro Pages routes
    Route::group(['prefix' => 'intro-pages', 'as' => 'intro-pages.'], function () {
        Route::get('/', 'IntroPagesController@index')->name('index');
        Route::get('create', 'IntroPagesController@create')->name('create');
        Route::post('/', 'IntroPagesController@store')->name('store');
        Route::get('{id}/edit', 'IntroPagesController@edit')->name('edit');
        Route::put('{id}', 'IntroPagesController@update')->name('update');
        Route::delete('{id}', 'IntroPagesController@destroy')->name('destroy');
    });

    // Test route
    Route::get('test', function () {
        return view('plugins/revo-woocommerce-plugin-7.0.3::index');
    })->name('test');

    // Test form route
    Route::get('test-form', function () {
        return view('plugins/revo-woocommerce-plugin-7.0.3::test-form');
    })->name('test-form');

    // Direct test save route
    Route::get('test-save', function () {
        try {
            $testData = [
                'id' => time(),
                'title' => 'اختبار مباشر ' . now()->format('H:i:s'),
                'image' => '/test/direct-' . rand(1,100) . '.jpg',
                'link' => 'https://direct-test.com',
                'order' => rand(1,10),
                'status' => true,
            ];

            $sliders = json_decode(\Botble\Setting\Facades\Setting::get('revo_home_sliders', '[]'), true) ?: [];
            $sliders[] = $testData;

            \Botble\Setting\Facades\Setting::set('revo_home_sliders', json_encode($sliders));
            \Botble\Setting\Facades\Setting::save();

            return response()->json([
                'success' => true,
                'message' => 'تم الحفظ بنجاح!',
                'data' => $testData,
                'all_sliders' => $sliders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    })->name('test-save');

    // Direct test update route
    Route::get('test-update/{id}', function ($id) {
        try {
            $sliders = json_decode(\Botble\Setting\Facades\Setting::get('revo_home_sliders', '[]'), true) ?: [];

            $found = false;
            foreach ($sliders as $key => $slider) {
                if ((int)$slider['id'] === (int)$id) {
                    $sliders[$key]['title'] = 'تم التحديث ' . now()->format('H:i:s');
                    $sliders[$key]['image'] = '/test/updated-' . rand(1,100) . '.jpg';
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                return response()->json([
                    'success' => false,
                    'message' => 'السلايدر غير موجود',
                    'id' => $id,
                    'available_ids' => array_column($sliders, 'id')
                ]);
            }

            \Botble\Setting\Facades\Setting::set('revo_home_sliders', json_encode($sliders));
            \Botble\Setting\Facades\Setting::save();

            return response()->json([
                'success' => true,
                'message' => 'تم التحديث بنجاح!',
                'id' => $id,
                'updated_slider' => $sliders[$key],
                'all_sliders' => $sliders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    })->name('test-update');

    // Test update form route
    Route::get('test-update-form', function () {
        return view('plugins/revo-woocommerce-plugin-7.0.3::test-update-form');
    })->name('test-update-form');

    // Clean and reset sliders data
    Route::get('clean-sliders', function () {
        try {
            // Get current data
            $slidersJson = \Botble\Setting\Facades\Setting::get('revo_home_sliders', '[]');
            $sliders = json_decode($slidersJson, true) ?: [];

            // Clean and normalize data
            $cleanSliders = [];
            $usedIds = [];
            $newId = 1;

            foreach ($sliders as $slider) {
                // Skip duplicates and invalid data
                if (empty($slider['image']) || in_array($slider['id'], $usedIds)) {
                    continue;
                }

                $cleanSliders[] = [
                    'id' => $newId,
                    'title' => $slider['title'] ?? '',
                    'image' => $slider['image'],
                    'link' => $slider['link'] ?? '',
                    'order' => $newId,
                    'status' => true,
                ];

                $usedIds[] = $slider['id'];
                $newId++;
            }

            // Save cleaned data
            \Botble\Setting\Facades\Setting::set('revo_home_sliders', json_encode($cleanSliders));
            \Botble\Setting\Facades\Setting::save();

            return response()->json([
                'success' => true,
                'message' => 'تم تنظيف البيانات بنجاح!',
                'before_count' => count($sliders),
                'after_count' => count($cleanSliders),
                'clean_data' => $cleanSliders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ: ' . $e->getMessage()
            ]);
        }
    })->name('clean-sliders');

    // Debug route to check current data
    Route::get('debug-sliders', function () {
        $slidersJson = \Botble\Setting\Facades\Setting::get('revo_home_sliders', '[]');
        $sliders = json_decode($slidersJson, true) ?: [];

        return response()->json([
            'raw_data' => $slidersJson,
            'parsed_data' => $sliders,
            'count' => count($sliders),
            'ids' => array_column($sliders, 'id'),
            'titles' => array_column($sliders, 'title')
        ], 200, [], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    })->name('debug-sliders');

    // Quick update test
    Route::get('quick-update/{id}', function ($id) {
        try {
            $controller = new \Botble\RevoWoocommerce\Http\Controllers\HomeSliderController();

            // Simulate form data
            $data = [
                'title' => 'تحديث سريع ' . now()->format('H:i:s'),
                'image' => 'https://dalilakauto.com/storage/updated-' . rand(1,100) . '.png',
                'link' => 'https://updated-link.com',
                'order' => rand(1,5)
            ];

            // Create a fake request
            $request = new \Illuminate\Http\Request();
            $request->merge($data);

            // Call the update method
            $response = new \Botble\Base\Http\Responses\BaseHttpResponse();
            $result = $controller->update($request, $id, $response);

            return response()->json([
                'success' => true,
                'message' => 'تم التحديث بنجاح!',
                'data' => $data,
                'id' => $id
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    })->name('quick-update');

    // Debug edit page
    Route::get('debug-edit', function () {
        return view('plugins/revo-woocommerce-plugin-7.0.3::debug-edit');
    })->name('debug-edit');

    // Simple form test
    Route::post('simple-update/{id}', function ($id, \Illuminate\Http\Request $request) {
        try {
            file_put_contents(storage_path('logs/revo-debug.log'),
                "[" . now() . "] 🚀 SIMPLE UPDATE RECEIVED - ID: $id, Data: " . json_encode($request->all()) . "\n",
                FILE_APPEND
            );

            $controller = new \Botble\RevoWoocommerce\Http\Controllers\HomeSliderController();
            $response = new \Botble\Base\Http\Responses\BaseHttpResponse();

            $result = $controller->update($request, $id, $response);

            return redirect()->back()->with('success', 'تم التحديث بنجاح!');
        } catch (\Exception $e) {
            file_put_contents(storage_path('logs/revo-debug.log'),
                "[" . now() . "] ❌ SIMPLE UPDATE ERROR: " . $e->getMessage() . "\n",
                FILE_APPEND
            );

            return redirect()->back()->with('error', 'خطأ: ' . $e->getMessage());
        }
    })->name('simple-update');

    // Simple test page
    Route::get('simple-test', function () {
        return view('plugins/revo-woocommerce-plugin-7.0.3::simple-test');
    })->name('simple-test');

});
