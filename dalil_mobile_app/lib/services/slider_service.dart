import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';

class SliderService {
  // جلب جميع السلايدرز من API فقط
  static Future<List<Map<String, dynamic>>> getAllSliders() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/simple-sliders'),
        headers: AppConfig.defaultHeaders,
      );

      print('Slider API Response Status: ${response.statusCode}');
      print('Slider API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        // التحقق من البنية الصحيحة للاستجابة
        if (data is Map && data['data'] != null) {
          print('Found ${data['data'].length} sliders from API');
          return List<Map<String, dynamic>>.from(data['data']);
        } else if (data is List) {
          print('Found ${data.length} sliders from API (direct list)');
          return List<Map<String, dynamic>>.from(data);
        }
      }

      // في حالة فشل API، ارجع قائمة فارغة
      print('API failed - Status: ${response.statusCode}');
      return [];
    } catch (e) {
      print('خطأ في جلب السلايدرز: $e');
      return [];
    }
  }

  // جلب سلايدر محدد بالمفتاح من API فقط
  static Future<Map<String, dynamic>?> getSliderByKey(String key) async {
    try {
      // استخدام GET مع query parameter
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/simple-sliders?keys[]=$key'),
        headers: AppConfig.defaultHeaders,
      );

      print('Slider by key API Response Status: ${response.statusCode}');
      print('Slider by key API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        List<Map<String, dynamic>> sliders = [];

        // التحقق من البنية الصحيحة للاستجابة
        if (data is Map && data['data'] != null) {
          sliders = List<Map<String, dynamic>>.from(data['data']);
        } else if (data is List) {
          sliders = List<Map<String, dynamic>>.from(data);
        }

        // البحث عن السلايدر بالمفتاح المحدد
        for (final slider in sliders) {
          if (slider['key'] == key) {
            print('Found slider with key: $key');
            return slider;
          }
        }
      }

      print('No slider found with key: $key');
      return null;
    } catch (e) {
      print('خطأ في جلب السلايدر: $e');
      return null;
    }
  }

  // جلب عدة سلايدرز بواسطة المفاتيح
  static Future<List<Map<String, dynamic>>> getSlidersByKeys(List<String> keys) async {
    try {
      // بناء query parameters للمفاتيح
      final queryParams = keys.map((key) => 'keys[]=$key').join('&');
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/simple-sliders?$queryParams'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data is Map && data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        } else if (data is List) {
          return List<Map<String, dynamic>>.from(data);
        }
      }

      return [];
    } catch (e) {
      print('خطأ في جلب السلايدرز: $e');
      return [];
    }
  }

  // جلب سلايدر الصفحة الرئيسية من API فقط
  static Future<Map<String, dynamic>?> getHomeSlider() async {
    try {
      final slider = await getSliderByKey('home-slider');
      return slider;
    } catch (e) {
      print('خطأ في جلب سلايدر الصفحة الرئيسية: $e');
      return null;
    }
  }

  // جلب عناصر السلايدر
  static List<Map<String, dynamic>> getSliderItems(Map<String, dynamic> slider) {
    if (slider['items'] != null) {
      return List<Map<String, dynamic>>.from(slider['items']);
    }
    return [];
  }

  // تنسيق عنصر السلايدر للعرض
  static Map<String, dynamic> formatSliderItem(Map<String, dynamic> item) {
    return {
      'id': item['id'],
      'title': item['title'] ?? '',
      'description': item['description'] ?? '',
      'image': item['image'] ?? '',
      'link': item['link'] ?? '',
      'order': item['order'] ?? 0,
      // استخراج البيانات الإضافية من metadata أو الحقول المباشرة
      'background_color': item['background_color'] ?? '#115061',
      'is_light': item['is_light'] ?? false,
      'subtitle': item['subtitle'] ?? '',
      'button_label': item['button_label'] ?? 'تسوق الآن',
      // دعم البيانات الإضافية من Simple Slider
      'tablet_image': item['tablet_image'],
      'mobile_image': item['mobile_image'],
    };
  }

  // استخراج البيانات الإضافية من metadata
  static Map<String, dynamic> extractMetadata(Map<String, dynamic> item) {
    final metadata = <String, dynamic>{};

    // إذا كانت البيانات تحتوي على metadata مباشرة
    if (item.containsKey('metadata') && item['metadata'] is Map) {
      metadata.addAll(Map<String, dynamic>.from(item['metadata']));
    }

    // استخراج البيانات الشائعة
    final commonFields = [
      'background_color', 'is_light', 'subtitle', 'button_label',
      'tablet_image', 'mobile_image'
    ];

    for (final field in commonFields) {
      if (item.containsKey(field)) {
        metadata[field] = item[field];
      }
    }

    return metadata;
  }


}
