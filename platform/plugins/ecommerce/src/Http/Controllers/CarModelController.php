<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Http\Controllers;

use Bo<PERSON>ble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Botble\Base\Forms\FormBuilder;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Ecommerce\Forms\CarModelForm;
use Botble\Ecommerce\Http\Requests\CarModelRequest;
use Botble\Ecommerce\Models\CarModel;
use Bo<PERSON>ble\Ecommerce\Tables\CarModelTable;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CarModelController extends BaseController
{
    protected const CAR_MODEL_MODULE_SCREEN_NAME = 'car-model';

    public function index(CarModelTable $table)
    {
        PageTitle::setTitle(trans('plugins/ecommerce::car-models.name'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/ecommerce::car-models.create'));

        return $formBuilder->create(CarModelForm::class)->renderForm();
    }

    public function store(CarModelRequest $request, BaseHttpResponse $response)
    {
        $data = $request->input();

        // التحقق من فرادة الـ slug وإضافة رمز عشوائي إذا كان مكرراً
        $slug = $data['slug'] ?? Str::slug($data['name']);
        $originalSlug = $slug;

        $count = 0;
        while (CarModel::query()->where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . ++$count;
        }

        $data['slug'] = $slug;

        $carModel = CarModel::query()->create($data);

        event(new CreatedContentEvent(self::CAR_MODEL_MODULE_SCREEN_NAME, $request, $carModel));

        return $response
            ->setPreviousUrl(route('car-models.index'))
            ->setNextUrl(route('car-models.edit', $carModel->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(CarModel $carModel, FormBuilder $formBuilder, Request $request)
    {
        event(new BeforeEditContentEvent($request, $carModel));

        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $carModel->name]));

        return $formBuilder->create(CarModelForm::class, ['model' => $carModel])->renderForm();
    }

    public function update(CarModel $carModel, CarModelRequest $request, BaseHttpResponse $response)
    {
        $data = $request->input();

        // التحقق من فرادة الـ slug وإضافة رمز عشوائي إذا كان مكرراً
        if ($request->input('slug') != $carModel->slug) {
            $slug = $data['slug'] ?? Str::slug($data['name']);
            $originalSlug = $slug;

            $count = 0;
            while (CarModel::query()->where('slug', $slug)->where('id', '!=', $carModel->id)->exists()) {
                $slug = $originalSlug . '-' . ++$count;
            }

            $data['slug'] = $slug;
        }

        $carModel->fill($data);
        $carModel->save();

        event(new UpdatedContentEvent(self::CAR_MODEL_MODULE_SCREEN_NAME, $request, $carModel));

        return $response
            ->setPreviousUrl(route('car-models.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(CarModel $carModel, Request $request, BaseHttpResponse $response)
    {
        try {
            $carModel->delete();

            event(new DeletedContentEvent(self::CAR_MODEL_MODULE_SCREEN_NAME, $request, $carModel));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function deletes(Request $request, BaseHttpResponse $response)
    {
        $ids = $request->input('ids');
        if (empty($ids)) {
            return $response
                ->setError()
                ->setMessage(trans('core/base::notices.no_select'));
        }

        foreach ($ids as $id) {
            $carModel = CarModel::query()->findOrFail($id);
            $carModel->delete();
            event(new DeletedContentEvent(self::CAR_MODEL_MODULE_SCREEN_NAME, $request, $carModel));
        }

        return $response->setMessage(trans('core/base::notices.delete_success_message'));
    }
}
