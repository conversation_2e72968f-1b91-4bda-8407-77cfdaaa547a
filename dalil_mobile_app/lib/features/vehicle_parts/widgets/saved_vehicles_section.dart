import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';
import '../providers/vehicle_parts_provider.dart';
import '../models/saved_vehicle_model.dart';

class SavedVehiclesSection extends StatelessWidget {
  const SavedVehiclesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<VehiclePartsProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'vehicle_parts_finder.saved_vehicles'.tr(),
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (provider.savedVehicles.isNotEmpty)
                  TextButton(
                    onPressed: () => context.push('/vehicle-parts-finder/my-vehicles'),
                    child: Text('عرض الكل'),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            
            if (provider.savedVehicles.isEmpty)
              _buildEmptyState(context, provider)
            else
              _buildVehiclesList(context, provider),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, VehiclePartsProvider provider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.garage,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 12),
          Text(
            'vehicle_parts_finder.no_saved_vehicles'.tr(),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'احفظ سياراتك للوصول السريع لقطع الغيار',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: provider.canSearch 
                ? () => _showAddVehicleDialog(context, provider)
                : null,
            icon: const Icon(Icons.add),
            label: Text('vehicle_parts_finder.add_vehicle'.tr()),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVehiclesList(BuildContext context, VehiclePartsProvider provider) {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: provider.savedVehicles.length + 1, // +1 للزر إضافة
        itemBuilder: (context, index) {
          if (index == provider.savedVehicles.length) {
            return _buildAddVehicleCard(context, provider);
          }
          
          final vehicle = provider.savedVehicles[index];
          return _buildVehicleCard(context, vehicle, provider);
        },
      ),
    );
  }

  Widget _buildVehicleCard(
    BuildContext context,
    SavedVehicleModel vehicle,
    VehiclePartsProvider provider,
  ) {
    return Container(
      width: 200,
      margin: const EdgeInsets.only(left: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: () => provider.selectSavedVehicle(vehicle),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.directions_car,
                        color: Theme.of(context).primaryColor,
                        size: 16,
                      ),
                    ),
                    const Spacer(),
                    PopupMenuButton<String>(
                      icon: Icon(
                        Icons.more_vert,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      onSelected: (value) {
                        if (value == 'delete') {
                          _showDeleteConfirmation(context, vehicle, provider);
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              const Icon(Icons.delete, color: Colors.red, size: 16),
                              const SizedBox(width: 8),
                              Text('common.delete'.tr()),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  vehicle.name,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  vehicle.shortName,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAddVehicleCard(BuildContext context, VehiclePartsProvider provider) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(left: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            style: BorderStyle.solid,
          ),
        ),
        child: InkWell(
          onTap: provider.canSearch 
              ? () => _showAddVehicleDialog(context, provider)
              : null,
          borderRadius: BorderRadius.circular(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add_circle_outline,
                color: provider.canSearch 
                    ? Theme.of(context).primaryColor 
                    : Colors.grey[400],
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                'إضافة سيارة',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: provider.canSearch 
                      ? Theme.of(context).primaryColor 
                      : Colors.grey[400],
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddVehicleDialog(BuildContext context, VehiclePartsProvider provider) {
    final nameController = TextEditingController();
    
    // إنشاء اسم تلقائي
    if (provider.canSearch) {
      nameController.text = 
          '${provider.selectedMake?.name} ${provider.selectedModel?.name} ${provider.selectedYear?.name}';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('vehicle_parts_finder.add_vehicle'.tr()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: 'اسم السيارة',
                hintText: 'مثال: سيارة العائلة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تفاصيل السيارة:',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${provider.selectedVehicleType?.name} - ${provider.selectedMake?.name} ${provider.selectedModel?.name} ${provider.selectedYear?.name}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('common.cancel'.tr()),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isNotEmpty) {
                await provider.addSavedVehicle(nameController.text.trim());
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('vehicle_parts_finder.vehicle_added'.tr()),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: Text('common.save'.tr()),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    SavedVehicleModel vehicle,
    VehiclePartsProvider provider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف السيارة'),
        content: Text('هل أنت متأكد من حذف "${vehicle.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('common.cancel'.tr()),
          ),
          ElevatedButton(
            onPressed: () async {
              await provider.removeSavedVehicle(vehicle.id);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('vehicle_parts_finder.vehicle_removed'.tr()),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('common.delete'.tr()),
          ),
        ],
      ),
    );
  }
}
