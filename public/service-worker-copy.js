// Service Worker for PWA Copy Theme - Enhanced Version
const CACHE_NAME = 'dalilak-auto-enhanced-v2';
const STATIC_CACHE = 'dalilak-static-v2';
const DYNAMIC_CACHE = 'dalilak-dynamic-v2';

const urlsToCache = [
  '/theme2/',
  '/pwa-copy/offline.html',
  '/pwa-copy/manifest.json',
  // Add more static assets as needed
];

// Install event - cache assets
self.addEventListener('install', event => {
  console.log('SW: Installing Enhanced Service Worker for Dalilak Auto');
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('SW: Caching static assets');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        console.log('SW: Static assets cached successfully');
        self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('SW: Activating Enhanced Service Worker');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.filter(cacheName => {
          return cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE;
        }).map(cacheName => {
          console.log('SW: Deleting old cache:', cacheName);
          return caches.delete(cacheName);
        })
      );
    }).then(() => {
      console.log('SW: Service Worker activated');
      return self.clients.claim();
    })
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Return cached response if found
        if (response) {
          return response;
        }

        // Clone the request
        const fetchRequest = event.request.clone();

        // Make network request and cache the response
        return fetch(fetchRequest).then(
          response => {
            // Check if valid response
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response
            const responseToCache = response.clone();

            caches.open(CACHE_NAME)
              .then(cache => {
                cache.put(event.request, responseToCache);
              });

            return response;
          }
        ).catch(() => {
          // If network request fails and it's a document request, show offline page
          if (event.request.mode === 'navigate') {
            return caches.match('/pwa-copy/offline.html');
          }
        });
      })
  );
});
