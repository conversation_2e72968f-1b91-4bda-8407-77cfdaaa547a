import '../models/api_response.dart';
import '../models/vehicle.dart';
import 'api_service.dart';
import '../utils/storage_helper.dart';

class VehiclePartsService {
  static final VehiclePartsService _instance = VehiclePartsService._internal();
  factory VehiclePartsService() => _instance;
  VehiclePartsService._internal();

  final ApiService _apiService = ApiService();

  // النماذج مستوردة من ملف منفصل

  // === APIs الفئات والتصنيفات ===

  // الحصول على الفئات الجذرية للمركبات
  Future<ApiResponse<List<VehicleCategory>>> getRootCategories() async {
    try {
      final response = await _apiService.getVehicleRootCategories();

      if (response.isSuccess && response.data != null) {
        final categories = (response.data!['data'] as List?)
            ?.map((json) => VehicleCategory.fromJson(json))
            .toList() ?? [];
        return ApiResponse.success(categories);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل فئات المركبات');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل فئات المركبات: ${e.toString()}');
    }
  }

  // الحصول على الفئات الفرعية
  Future<ApiResponse<List<VehicleCategory>>> getChildCategories(int parentId) async {
    try {
      final response = await _apiService.get('/vehicle-parts/categories/$parentId/children');

      if (response.isSuccess && response.data != null) {
        final categories = (response.data!['data'] as List?)
            ?.map((json) => VehicleCategory.fromJson(json))
            .toList() ?? [];
        return ApiResponse.success(categories);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل الفئات الفرعية');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل الفئات الفرعية: ${e.toString()}');
    }
  }

  // === APIs الماركات والموديلات ===

  // الحصول على ماركات السيارات
  Future<ApiResponse<List<VehicleMake>>> getVehicleMakes() async {
    try {
      final response = await _apiService.getVehicleMakes();

      if (response.isSuccess && response.data != null) {
        final makes = (response.data!['data'] as List?)
            ?.map((json) => VehicleMake.fromJson(json))
            .toList() ?? [];
        return ApiResponse.success(makes);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل ماركات السيارات');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل ماركات السيارات: ${e.toString()}');
    }
  }

  // الحصول على موديلات ماركة معينة
  Future<ApiResponse<List<VehicleModel>>> getVehicleModels(int makeId) async {
    try {
      final response = await _apiService.getVehicleModels(makeId);

      if (response.isSuccess && response.data != null) {
        final models = (response.data!['data'] as List?)
            ?.map((json) => VehicleModel.fromJson(json))
            .toList() ?? [];
        return ApiResponse.success(models);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل موديلات السيارة');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل موديلات السيارة: ${e.toString()}');
    }
  }

  // الحصول على سنوات موديل معين
  Future<ApiResponse<List<int>>> getVehicleYears(int modelId) async {
    try {
      final response = await _apiService.getVehicleYears(modelId);

      if (response.isSuccess && response.data != null) {
        final years = (response.data!['data'] as List?)
            ?.map((year) => year as int)
            .toList() ?? [];
        return ApiResponse.success(years);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل سنوات الموديل');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل سنوات الموديل: ${e.toString()}');
    }
  }

  // === البحث عن قطع الغيار ===

  // البحث عن قطع الغيار
  Future<ApiResponse<Map<String, dynamic>>> searchVehicleParts({
    List<int>? categoryIds,
    String? keyword,
    int page = 1,
    int perPage = 12,
  }) async {
    try {
      final response = await _apiService.searchVehicleParts(
        categoryIds: categoryIds,
        keyword: keyword,
        page: page,
        perPage: perPage,
      );

      return response;
    } catch (e) {
      return ApiResponse.error('خطأ في البحث عن قطع الغيار: ${e.toString()}');
    }
  }

  // البحث السريع
  Future<ApiResponse<Map<String, dynamic>>> quickSearch(String keyword, {int limit = 10}) async {
    try {
      final response = await _apiService.quickSearchVehicleParts(keyword, limit: limit);
      return response;
    } catch (e) {
      return ApiResponse.error('خطأ في البحث السريع: ${e.toString()}');
    }
  }

  // الحصول على قطع الغيار الشائعة لفئة معينة
  Future<ApiResponse<Map<String, dynamic>>> getPopularParts(int categoryId, {int limit = 10}) async {
    try {
      final response = await _apiService.getPopularPartsByCategory(categoryId, limit: limit);
      return response;
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل القطع الشائعة: ${e.toString()}');
    }
  }

  // الحصول على قطع الغيار لفئة معينة
  Future<ApiResponse<Map<String, dynamic>>> getPartsByCategory(int categoryId, {int page = 1, int perPage = 12}) async {
    try {
      final response = await _apiService.getVehiclePartsByCategory(categoryId, page: page, perPage: perPage);
      return response;
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل قطع الغيار للفئة: ${e.toString()}');
    }
  }

  // === إدارة السيارات المحفوظة ===

  // الحصول على السيارات المحفوظة
  Future<ApiResponse<List<SavedVehicle>>> getMyVehicles() async {
    try {
      final response = await _apiService.getMyVehicles();

      if (response.isSuccess && response.data != null) {
        final vehicles = (response.data!['data'] as List?)
            ?.map((json) => SavedVehicle.fromJson(json))
            .toList() ?? [];
        return ApiResponse.success(vehicles);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في تحميل السيارات المحفوظة');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل السيارات المحفوظة: ${e.toString()}');
    }
  }

  // إضافة سيارة جديدة
  Future<ApiResponse<SavedVehicle>> addVehicle({
    required int categoryId,
    required int makeId,
    required int modelId,
    required int year,
    String? nickname,
  }) async {
    try {
      final response = await _apiService.addVehicle(
        categoryId: categoryId,
        makeId: makeId,
        modelId: modelId,
        year: year,
        nickname: nickname,
      );

      if (response.isSuccess && response.data != null) {
        final vehicle = SavedVehicle.fromJson(response.data!);
        return ApiResponse.success(vehicle);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في إضافة السيارة');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في إضافة السيارة: ${e.toString()}');
    }
  }

  // حذف سيارة محفوظة
  Future<ApiResponse<bool>> removeVehicle(int vehicleId) async {
    try {
      final response = await _apiService.removeVehicle(vehicleId);

      if (response.isSuccess) {
        return ApiResponse.success(true);
      } else {
        return ApiResponse.error(response.error ?? 'فشل في حذف السيارة');
      }
    } catch (e) {
      return ApiResponse.error('خطأ في حذف السيارة: ${e.toString()}');
    }
  }

  // الحصول على قطع غيار سيارة محفوظة
  Future<ApiResponse<Map<String, dynamic>>> getVehicleParts(int vehicleId) async {
    try {
      final response = await _apiService.get('/vehicle-parts/my-vehicles/$vehicleId/parts');

      return response as ApiResponse<Map<String, dynamic>>;
    } catch (e) {
      return ApiResponse.error('خطأ في تحميل قطع غيار السيارة: ${e.toString()}');
    }
  }

  // === التخزين المحلي ===

  // حفظ السيارات محلياً
  Future<bool> saveVehiclesLocally(List<SavedVehicle> vehicles) async {
    try {
      final vehiclesJson = vehicles.map((v) => v.toJson()).toList();
      return await StorageHelper.setJson('saved_vehicles', {'vehicles': vehiclesJson});
    } catch (e) {
      return false;
    }
  }

  // الحصول على السيارات المحفوظة محلياً
  Future<List<SavedVehicle>> getLocalVehicles() async {
    try {
      final data = await StorageHelper.getJson('saved_vehicles');
      if (data != null && data['vehicles'] != null) {
        final vehiclesList = data['vehicles'] as List;
        return vehiclesList
            .map((json) => SavedVehicle.fromJson(json))
            .toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  // === دوال مساعدة ===

  // تنسيق اسم السيارة
  String formatVehicleName(SavedVehicle vehicle) {
    return '${vehicle.make?.name ?? ''} ${vehicle.model?.name ?? ''} ${vehicle.year}';
  }

  // التحقق من صحة بيانات السيارة
  bool isValidVehicleData({
    required int categoryId,
    required int makeId,
    required int modelId,
    required int year,
  }) {
    return categoryId > 0 && 
           makeId > 0 && 
           modelId > 0 && 
           year >= 1900 && 
           year <= DateTime.now().year + 1;
  }

  // الحصول على السنوات المتاحة (من 1990 إلى السنة الحالية + 1)
  List<int> getAvailableYears() {
    final currentYear = DateTime.now().year;
    final years = <int>[];
    for (int year = currentYear + 1; year >= 1990; year--) {
      years.add(year);
    }
    return years;
  }
}
