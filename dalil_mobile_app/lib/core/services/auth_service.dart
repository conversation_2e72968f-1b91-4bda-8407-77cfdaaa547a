import 'dart:convert';
import 'api_service.dart';
import 'storage_service.dart';
import '../models/user_model.dart';

class AuthService {
  static late AuthService _instance;
  static AuthService get instance => _instance;

  static Future<void> init() async {
    _instance = AuthService._internal();
  }

  AuthService._internal();

  // تسجيل الدخول
  Future<AuthResult> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      final response = await ApiService.instance.post(
        '/auth/login',
        data: {
          'email': email,
          'password': password,
          'remember_me': rememberMe,
        },
      );

      if (response.success && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final token = data['token'] as String;
        final userData = data['user'] as Map<String, dynamic>;

        // حفظ التوكن وبيانات المستخدم
        await StorageService.saveToken(token);
        await StorageService.saveUser(userData);

        final user = UserModel.fromJson(userData);
        
        return AuthResult(
          success: true,
          user: user,
          message: response.message ?? 'تم تسجيل الدخول بنجاح',
        );
      } else {
        return AuthResult(
          success: false,
          message: response.message ?? 'فشل في تسجيل الدخول',
          errors: response.errors,
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء تسجيل الدخول',
      );
    }
  }

  // تسجيل حساب جديد
  Future<AuthResult> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    required String phone,
    String? address,
    String userType = 'customer',
  }) async {
    try {
      final response = await ApiService.instance.post(
        '/auth/register',
        data: {
          'name': name,
          'email': email,
          'password': password,
          'password_confirmation': passwordConfirmation,
          'phone': phone,
          'address': address,
          'user_type': userType,
        },
      );

      if (response.success && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final token = data['token'] as String?;
        final userData = data['user'] as Map<String, dynamic>;

        if (token != null) {
          // حفظ التوكن وبيانات المستخدم
          await StorageService.saveToken(token);
          await StorageService.saveUser(userData);
        }

        final user = UserModel.fromJson(userData);
        
        return AuthResult(
          success: true,
          user: user,
          message: response.message ?? 'تم إنشاء الحساب بنجاح',
        );
      } else {
        return AuthResult(
          success: false,
          message: response.message ?? 'فشل في إنشاء الحساب',
          errors: response.errors,
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء إنشاء الحساب',
      );
    }
  }

  // تسجيل الخروج
  Future<bool> logout() async {
    try {
      // إرسال طلب تسجيل الخروج للخادم
      await ApiService.instance.post('/auth/logout');
      
      // مسح البيانات المحلية
      await StorageService.removeToken();
      await StorageService.removeUser();
      
      return true;
    } catch (e) {
      // مسح البيانات المحلية حتى لو فشل الطلب
      await StorageService.removeToken();
      await StorageService.removeUser();
      return false;
    }
  }

  // التحقق من حالة تسجيل الدخول
  Future<bool> isLoggedIn() async {
    return await StorageService.hasToken();
  }

  // الحصول على المستخدم الحالي
  Future<UserModel?> getCurrentUser() async {
    final userData = await StorageService.getUser();
    if (userData != null) {
      return UserModel.fromJson(userData);
    }
    return null;
  }

  // تحديث بيانات المستخدم
  Future<AuthResult> updateProfile({
    required String name,
    required String email,
    required String phone,
    String? address,
  }) async {
    try {
      final response = await ApiService.instance.put(
        '/auth/profile',
        data: {
          'name': name,
          'email': email,
          'phone': phone,
          'address': address,
        },
      );

      if (response.success && response.data != null) {
        final userData = response.data as Map<String, dynamic>;
        await StorageService.saveUser(userData);

        final user = UserModel.fromJson(userData);
        
        return AuthResult(
          success: true,
          user: user,
          message: response.message ?? 'تم تحديث البيانات بنجاح',
        );
      } else {
        return AuthResult(
          success: false,
          message: response.message ?? 'فشل في تحديث البيانات',
          errors: response.errors,
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء تحديث البيانات',
      );
    }
  }

  // تغيير كلمة المرور
  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
    required String passwordConfirmation,
  }) async {
    try {
      final response = await ApiService.instance.put(
        '/auth/change-password',
        data: {
          'current_password': currentPassword,
          'password': newPassword,
          'password_confirmation': passwordConfirmation,
        },
      );

      return AuthResult(
        success: response.success,
        message: response.message ?? 
            (response.success ? 'تم تغيير كلمة المرور بنجاح' : 'فشل في تغيير كلمة المرور'),
        errors: response.errors,
      );
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء تغيير كلمة المرور',
      );
    }
  }

  // إعادة تعيين كلمة المرور
  Future<AuthResult> forgotPassword({required String email}) async {
    try {
      final response = await ApiService.instance.post(
        '/auth/forgot-password',
        data: {'email': email},
      );

      return AuthResult(
        success: response.success,
        message: response.message ?? 
            (response.success ? 'تم إرسال رابط إعادة التعيين' : 'فشل في إرسال الرابط'),
        errors: response.errors,
      );
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء إرسال طلب إعادة التعيين',
      );
    }
  }

  // تحديث التوكن
  Future<bool> refreshToken() async {
    try {
      final response = await ApiService.instance.post('/auth/refresh');
      
      if (response.success && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final token = data['token'] as String;
        await StorageService.saveToken(token);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // التحقق من البريد الإلكتروني
  Future<AuthResult> verifyEmail({required String token}) async {
    try {
      final response = await ApiService.instance.post(
        '/auth/verify-email',
        data: {'token': token},
      );

      return AuthResult(
        success: response.success,
        message: response.message ?? 
            (response.success ? 'تم التحقق من البريد الإلكتروني' : 'فشل في التحقق'),
        errors: response.errors,
      );
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء التحقق من البريد الإلكتروني',
      );
    }
  }

  // إعادة إرسال رمز التحقق
  Future<AuthResult> resendVerificationCode() async {
    try {
      final response = await ApiService.instance.post('/auth/resend-verification');

      return AuthResult(
        success: response.success,
        message: response.message ?? 
            (response.success ? 'تم إرسال رمز التحقق' : 'فشل في إرسال الرمز'),
        errors: response.errors,
      );
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء إرسال رمز التحقق',
      );
    }
  }
}

// فئة نتيجة المصادقة
class AuthResult {
  final bool success;
  final UserModel? user;
  final String message;
  final Map<String, dynamic>? errors;

  AuthResult({
    required this.success,
    this.user,
    required this.message,
    this.errors,
  });

  // الحصول على أول خطأ
  String? get firstError {
    if (errors != null && errors!.isNotEmpty) {
      final firstKey = errors!.keys.first;
      final firstValue = errors![firstKey];
      if (firstValue is List && firstValue.isNotEmpty) {
        return firstValue.first.toString();
      } else if (firstValue is String) {
        return firstValue;
      }
    }
    return null;
  }

  // الحصول على رسالة الخطأ المناسبة
  String get errorMessage {
    return firstError ?? message;
  }
}
