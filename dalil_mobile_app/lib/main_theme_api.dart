import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'config/app_config.dart';
import 'services/theme_service.dart';
import 'package:cached_network_image/cached_network_image.dart';

void main() {
  runApp(const DalilApp());
}

class DalilApp extends StatelessWidget {
  const DalilApp({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>>(
      future: ThemeService.getThemeSettings(),
      builder: (context, snapshot) {
        final theme = snapshot.data ?? {};
        
        return MaterialApp(
          title: ThemeService.getBrandingInfo(theme, 'site_title'),
          debugShowCheckedModeBanner: false,
          locale: const Locale('ar', 'IQ'),
          supportedLocales: const [
            Locale('ar', 'IQ'),
            Locale('en', 'US'),
            Locale('ku', 'IQ'),
          ],
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          theme: _buildThemeData(theme),
          home: DynamicThemeHomeScreen(theme: theme),
        );
      },
    );
  }

  ThemeData _buildThemeData(Map<String, dynamic> theme) {
    return ThemeData(
      primarySwatch: Colors.blue,
      primaryColor: Color(ThemeService.getColor(theme, 'primary')),
      fontFamily: ThemeService.getFontFamily(theme),
      textTheme: TextTheme(
        bodyLarge: TextStyle(
          fontSize: ThemeService.getFontSize(theme, 'base'),
          fontWeight: FontWeight.w400,
        ),
        bodyMedium: TextStyle(
          fontSize: ThemeService.getFontSize(theme, 'sm'),
          fontWeight: FontWeight.w400,
        ),
        headlineLarge: TextStyle(
          fontSize: ThemeService.getFontSize(theme, '4xl'),
          fontWeight: FontWeight.w700,
        ),
        headlineMedium: TextStyle(
          fontSize: ThemeService.getFontSize(theme, '3xl'),
          fontWeight: FontWeight.w600,
        ),
        headlineSmall: TextStyle(
          fontSize: ThemeService.getFontSize(theme, '2xl'),
          fontWeight: FontWeight.w600,
        ),
      ),
      colorScheme: ColorScheme.fromSeed(
        seedColor: Color(ThemeService.getColor(theme, 'primary')),
        secondary: Color(ThemeService.getColor(theme, 'secondary')),
      ),
    );
  }

  Widget _buildCategoriesSection() {
    if (_categories.isEmpty) return const SliverToBoxAdapter(child: SizedBox.shrink());

    return SliverToBoxAdapter(
      child: Column(
        children: [
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'الفئات الرئيسية',
                    style: TextStyle(
                      fontSize: ThemeService.getFontSize(widget.theme, 'xl'),
                      fontWeight: FontWeight.w700,
                      fontFamily: ThemeService.getFontFamily(widget.theme),
                      color: Color(ThemeService.getColor(widget.theme, 'text')),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                return _buildCategoryCard(_categories[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(Map<String, dynamic> category) {
    final primaryColor = Color(ThemeService.getColor(widget.theme, 'primary'));

    return Container(
      width: 110,
      margin: const EdgeInsets.only(left: 16),
      child: Column(
        children: [
          Container(
            height: 80,
            width: 80,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  primaryColor.withOpacity(0.1),
                  primaryColor.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: primaryColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: category['icon_image'] != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: CachedNetworkImage(
                      imageUrl: '${AppConfig.baseUrl}${category['icon_image']}',
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) => Icon(
                        Icons.category,
                        size: 40,
                        color: primaryColor,
                      ),
                    ),
                  )
                : Icon(
                    Icons.category,
                    size: 40,
                    color: primaryColor,
                  ),
          ),
          const SizedBox(height: 12),
          Text(
            category['name'] ?? 'فئة',
            style: TextStyle(
              fontSize: ThemeService.getFontSize(widget.theme, 'xs'),
              fontWeight: FontWeight.w600,
              fontFamily: ThemeService.getFontFamily(widget.theme),
              color: Color(ThemeService.getColor(widget.theme, 'text')),
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildProductSection(String title, List<Map<String, dynamic>> products) {
    if (products.isEmpty) return const SliverToBoxAdapter(child: SizedBox.shrink());

    return SliverToBoxAdapter(
      child: Column(
        children: [
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: ThemeService.getFontSize(widget.theme, 'xl'),
                      fontWeight: FontWeight.w700,
                      fontFamily: ThemeService.getFontFamily(widget.theme),
                      color: Color(ThemeService.getColor(widget.theme, 'text')),
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () {},
                  child: Text(
                    'عرض الكل',
                    style: TextStyle(
                      color: Color(ThemeService.getColor(widget.theme, 'primary')),
                      fontFamily: ThemeService.getFontFamily(widget.theme),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 280,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: products.length,
              itemBuilder: (context, index) {
                return Container(
                  width: 200,
                  margin: const EdgeInsets.only(left: 16),
                  child: _buildProductCard(products[index]),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

class DynamicThemeHomeScreen extends StatefulWidget {
  final Map<String, dynamic> theme;
  
  const DynamicThemeHomeScreen({super.key, required this.theme});

  @override
  State<DynamicThemeHomeScreen> createState() => _DynamicThemeHomeScreenState();
}

class _DynamicThemeHomeScreenState extends State<DynamicThemeHomeScreen> {
  int _selectedIndex = 0;
  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> _products = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final results = await Future.wait([
        _loadCategories(),
        _loadProducts(),
      ]);

      setState(() {
        _categories = results[0];
        _products = results[1];
        _isLoading = false;
      });
    } catch (e) {
      print('خطأ في تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<List<Map<String, dynamic>>> _loadCategories() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/ecommerce/product-categories'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          List<Map<String, dynamic>> categories = List<Map<String, dynamic>>.from(data['data']);
          return categories.where((cat) => cat['parent_id'] == null || cat['parent_id'] == 0).take(6).toList();
        }
      }
    } catch (e) {
      print('خطأ في جلب الفئات: $e');
    }
    return [];
  }

  Future<List<Map<String, dynamic>>> _loadProducts() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.apiBaseUrl}/ecommerce/products?per_page=30'),
        headers: AppConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
      }
    } catch (e) {
      print('خطأ في جلب المنتجات: $e');
    }
    return [];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _selectedIndex == 0 ? _buildHomeContent() : _buildOtherContent(),
      bottomNavigationBar: _buildBottomNavBar(),
    );
  }

  Widget _buildHomeContent() {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: Color(ThemeService.getColor(widget.theme, 'primary')),
        ),
      );
    }

    return CustomScrollView(
      slivers: [
        _buildAppBar(),
        _buildSearchBar(),
        _buildVehiclePartsFinderCard(),
        _buildCategoriesSection(),
        _buildProductSection('أحدث المنتجات', _products.take(10).toList()),
        _buildProductSection('الأكثر مبيعاً', _products.skip(10).take(10).toList()),
        _buildProductSection('المنتجات المميزة', _products.skip(20).take(10).toList()),
      ],
    );
  }

  Widget _buildAppBar() {
    final primaryColor = Color(ThemeService.getColor(widget.theme, 'primary'));
    final gradientColors = widget.theme['mobile_specific']?['gradient_colors'] as List? ?? ['#0C55AA', '#3c77bb'];
    
    return SliverAppBar(
      expandedHeight: 140,
      floating: false,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(ThemeService.hexToColor(gradientColors[0])),
                Color(ThemeService.hexToColor(gradientColors[1])),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        title: Text(
          ThemeService.getBrandingInfo(widget.theme, 'site_name'),
          style: TextStyle(
            color: Colors.white,
            fontSize: ThemeService.getFontSize(widget.theme, 'xl'),
            fontWeight: FontWeight.w700,
            fontFamily: ThemeService.getFontFamily(widget.theme),
          ),
        ),
        centerTitle: true,
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.shopping_cart, color: Colors.white),
          onPressed: () {},
        ),
        IconButton(
          icon: const Icon(Icons.person, color: Colors.white),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(16),
        child: TextField(
          decoration: InputDecoration(
            hintText: 'ابحث عن قطع الغيار...',
            hintStyle: TextStyle(
              fontFamily: ThemeService.getFontFamily(widget.theme),
              fontSize: ThemeService.getFontSize(widget.theme, 'sm'),
            ),
            prefixIcon: Icon(
              Icons.search,
              color: Color(ThemeService.getColor(widget.theme, 'primary')),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(25),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: Color(ThemeService.getPaletteColor(widget.theme, 'grey_100')),
          ),
        ),
      ),
    );
  }

  Widget _buildVehiclePartsFinderCard() {
    final primaryColor = Color(ThemeService.getColor(widget.theme, 'primary'));
    final gradientColors = widget.theme['mobile_specific']?['gradient_colors'] as List? ?? ['#0C55AA', '#3c77bb'];
    
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Card(
          elevation: 12,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                colors: [
                  Color(ThemeService.hexToColor(gradientColors[0])),
                  Color(ThemeService.hexToColor(gradientColors[1])),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: const Icon(
                    Icons.car_repair,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'باحث قطع الغيار',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: ThemeService.getFontSize(widget.theme, 'xl'),
                          fontWeight: FontWeight.w700,
                          fontFamily: ThemeService.getFontFamily(widget.theme),
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        'ابحث عن قطع الغيار حسب نوع السيارة',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: ThemeService.getFontSize(widget.theme, 'sm'),
                          fontFamily: ThemeService.getFontFamily(widget.theme),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    final primaryColor = Color(ThemeService.getColor(widget.theme, 'primary'));

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color(ThemeService.getPaletteColor(widget.theme, 'grey_300')).withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المنتج
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                child: _buildProductImage(product),
              ),
            ),
          ),

          // معلومات المنتج
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product['name'] ?? 'منتج غير محدد',
                    style: TextStyle(
                      fontSize: ThemeService.getFontSize(widget.theme, 'sm'),
                      fontWeight: FontWeight.w600,
                      fontFamily: ThemeService.getFontFamily(widget.theme),
                      color: Color(ThemeService.getColor(widget.theme, 'text')),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const Spacer(),

                  Text(
                    '${_formatPrice(product['price'] ?? 0)} د.ع',
                    style: TextStyle(
                      fontSize: ThemeService.getFontSize(widget.theme, 'lg'),
                      fontWeight: FontWeight.w700,
                      fontFamily: ThemeService.getFontFamily(widget.theme),
                      color: primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductImage(Map<String, dynamic> product) {
    final imageUrl = product['image'] ?? product['images']?.first ?? '';

    if (imageUrl.isEmpty) {
      return Container(
        color: Color(ThemeService.getPaletteColor(widget.theme, 'grey_200')),
        child: Icon(
          Icons.car_repair,
          size: 60,
          color: Color(ThemeService.getPaletteColor(widget.theme, 'grey_500')),
        ),
      );
    }

    final fullImageUrl = imageUrl.startsWith('http') ? imageUrl : '${AppConfig.baseUrl}$imageUrl';

    return CachedNetworkImage(
      imageUrl: fullImageUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        color: Color(ThemeService.getPaletteColor(widget.theme, 'grey_100')),
        child: Center(
          child: CircularProgressIndicator(
            color: Color(ThemeService.getColor(widget.theme, 'primary')),
          ),
        ),
      ),
      errorWidget: (context, url, error) => Container(
        color: Color(ThemeService.getPaletteColor(widget.theme, 'grey_200')),
        child: Icon(
          Icons.car_repair,
          size: 60,
          color: Color(ThemeService.getPaletteColor(widget.theme, 'grey_500')),
        ),
      ),
    );
  }

  String _formatPrice(dynamic price) {
    if (price == null) return '0';
    return price.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }

  Widget _buildOtherContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _selectedIndex == 1 ? Icons.category :
            _selectedIndex == 2 ? Icons.shopping_cart : Icons.person,
            size: 80,
            color: Color(ThemeService.getPaletteColor(widget.theme, 'grey_400')),
          ),
          const SizedBox(height: 16),
          Text(
            _selectedIndex == 1 ? 'صفحة الفئات' :
            _selectedIndex == 2 ? 'صفحة السلة' : 'صفحة الحساب',
            style: TextStyle(
              fontSize: ThemeService.getFontSize(widget.theme, '2xl'),
              fontWeight: FontWeight.w700,
              fontFamily: ThemeService.getFontFamily(widget.theme),
              color: Color(ThemeService.getPaletteColor(widget.theme, 'grey_600')),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قريباً...',
            style: TextStyle(
              fontSize: ThemeService.getFontSize(widget.theme, 'base'),
              fontFamily: ThemeService.getFontFamily(widget.theme),
              color: Color(ThemeService.getPaletteColor(widget.theme, 'grey_500')),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavBar() {
    final primaryColor = Color(ThemeService.getColor(widget.theme, 'primary'));

    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: (index) {
        setState(() {
          _selectedIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      selectedItemColor: primaryColor,
      unselectedItemColor: Color(ThemeService.getPaletteColor(widget.theme, 'grey_500')),
      selectedLabelStyle: TextStyle(
        fontFamily: ThemeService.getFontFamily(widget.theme),
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: TextStyle(
        fontFamily: ThemeService.getFontFamily(widget.theme),
        fontWeight: FontWeight.w400,
      ),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.category),
          label: 'الفئات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.shopping_cart),
          label: 'السلة',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'الحساب',
        ),
      ],
    );
  }
}
