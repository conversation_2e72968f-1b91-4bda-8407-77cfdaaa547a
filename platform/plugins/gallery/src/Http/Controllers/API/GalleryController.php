<?php

namespace Botble\Gallery\Http\Controllers\API;

use Botble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Gallery\Http\Resources\GalleryResource;
use Botble\Gallery\Models\Gallery;
use Botble\Gallery\Models\GalleryMeta;
use Illuminate\Http\Request;

class GalleryController extends BaseController
{
    /**
     * قائمة المعارض
     *
     * @group Gallery
     */
    public function index(Request $request)
    {
        $query = Gallery::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with('user');

        // فلترة المعارض المميزة
        if ($request->boolean('featured')) {
            $query->where('is_featured', 1);
        }

        // البحث في الاسم والوصف
        if ($request->has('search') && $request->input('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%");
            });
        }

        // ترتيب النتائج
        $sortBy = $request->input('sort_by', 'order');
        $sortOrder = $request->input('sort_order', 'asc');
        
        if (in_array($sortBy, ['order', 'name', 'created_at', 'updated_at'])) {
            $query->orderBy($sortBy, $sortOrder === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy('order', 'asc')->orderByDesc('created_at');
        }

        $perPage = $request->integer('per_page', 12);
        $perPage = min($perPage, 50); // حد أقصى 50 عنصر

        $galleries = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData(GalleryResource::collection($galleries))
            ->toApiResponse();
    }

    /**
     * عرض معرض محدد
     *
     * @group Gallery
     */
    public function show($id)
    {
        $gallery = Gallery::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with('user')
            ->findOrFail($id);

        return $this
            ->httpResponse()
            ->setData(new GalleryResource($gallery))
            ->toApiResponse();
    }

    /**
     * الحصول على صور معرض محدد
     *
     * @group Gallery
     */
    public function getImages($id)
    {
        $gallery = Gallery::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->findOrFail($id);

        $galleryMeta = GalleryMeta::query()
            ->where([
                'reference_id' => $gallery->id,
                'reference_type' => Gallery::class,
            ])
            ->first();

        $images = [];
        if ($galleryMeta && $galleryMeta->images) {
            $images = is_string($galleryMeta->images) 
                ? json_decode($galleryMeta->images, true) 
                : $galleryMeta->images;
        }

        return $this
            ->httpResponse()
            ->setData([
                'gallery' => new GalleryResource($gallery),
                'images' => $images ?: [],
                'total_images' => count($images ?: [])
            ])
            ->toApiResponse();
    }

    /**
     * المعارض المميزة
     *
     * @group Gallery
     */
    public function getFeatured(Request $request)
    {
        $query = Gallery::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->where('is_featured', 1)
            ->with('user')
            ->orderBy('order', 'asc')
            ->orderByDesc('created_at');

        $limit = $request->integer('limit', 6);
        $limit = min($limit, 20); // حد أقصى 20 عنصر

        $galleries = $query->limit($limit)->get();

        return $this
            ->httpResponse()
            ->setData(GalleryResource::collection($galleries))
            ->toApiResponse();
    }

    /**
     * البحث في المعارض
     *
     * @group Gallery
     */
    public function search(Request $request)
    {
        $request->validate([
            'q' => 'required|string|min:2|max:255',
            'per_page' => 'nullable|integer|min:1|max:50'
        ]);

        $query = Gallery::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with('user');

        $searchTerm = $request->input('q');
        
        // البحث في الاسم والوصف
        $query->where(function ($q) use ($searchTerm) {
            $q->where('name', 'LIKE', "%{$searchTerm}%")
              ->orWhere('description', 'LIKE', "%{$searchTerm}%");
        });

        // ترتيب النتائج حسب الصلة (الأسماء التي تحتوي على المصطلح أولاً)
        $query->orderByRaw("CASE WHEN name LIKE '%{$searchTerm}%' THEN 1 ELSE 2 END")
              ->orderBy('order', 'asc')
              ->orderByDesc('created_at');

        $perPage = $request->integer('per_page', 12);
        $galleries = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData([
                'search_term' => $searchTerm,
                'total_results' => $galleries->total(),
                'galleries' => GalleryResource::collection($galleries)
            ])
            ->toApiResponse();
    }
}
