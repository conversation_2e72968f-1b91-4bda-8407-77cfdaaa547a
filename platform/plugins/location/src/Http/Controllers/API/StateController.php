<?php

namespace Botble\Location\Http\Controllers\API;

use Botble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Location\Http\Resources\StateResource;
use Botble\Location\Http\Resources\CityResource;
use Botble\Location\Models\State;
use Illuminate\Http\Request;

class StateController extends BaseController
{
    /**
     * قائمة الولايات/المحافظات
     *
     * @group Location
     */
    public function index(Request $request)
    {
        $query = State::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with('country');

        // فلترة حسب الدولة
        if ($request->has('country_id') && $request->input('country_id')) {
            $query->where('country_id', $request->input('country_id'));
        }

        // البحث في اسم الولاية
        if ($request->has('search') && $request->input('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('abbreviation', 'LIKE', "%{$search}%");
            });
        }

        // ترتيب النتائج
        $sortBy = $request->input('sort_by', 'order');
        $sortOrder = $request->input('sort_order', 'asc');
        
        if (in_array($sortBy, ['order', 'name'])) {
            $query->orderBy($sortBy, $sortOrder === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy('order', 'asc')->orderBy('name', 'asc');
        }

        $perPage = $request->integer('per_page', 50);
        $perPage = min($perPage, 100);

        $states = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData(StateResource::collection($states))
            ->toApiResponse();
    }

    /**
     * عرض ولاية محددة
     *
     * @group Location
     */
    public function show($id)
    {
        $state = State::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with('country')
            ->withCount('cities')
            ->findOrFail($id);

        return $this
            ->httpResponse()
            ->setData(new StateResource($state))
            ->toApiResponse();
    }

    /**
     * الحصول على مدن ولاية محددة
     *
     * @group Location
     */
    public function getCities($id, Request $request)
    {
        $state = State::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with('country')
            ->findOrFail($id);

        $query = $state->cities()
            ->where('status', BaseStatusEnum::PUBLISHED);

        // البحث في اسم المدينة
        if ($request->has('search') && $request->input('search')) {
            $search = $request->input('search');
            $query->where('name', 'LIKE', "%{$search}%");
        }

        // ترتيب النتائج
        $sortBy = $request->input('sort_by', 'order');
        $sortOrder = $request->input('sort_order', 'asc');
        
        if (in_array($sortBy, ['order', 'name'])) {
            $query->orderBy($sortBy, $sortOrder === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy('order', 'asc')->orderBy('name', 'asc');
        }

        $perPage = $request->integer('per_page', 50);
        $perPage = min($perPage, 100);

        $cities = $query->paginate($perPage);

        return $this
            ->httpResponse()
            ->setData([
                'state' => new StateResource($state),
                'cities' => CityResource::collection($cities)
            ])
            ->toApiResponse();
    }
}
