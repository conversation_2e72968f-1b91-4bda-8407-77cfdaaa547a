import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import '../models/api_response.dart';
import '../utils/storage_helper.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  // الحصول على Headers مع المصادقة
  Future<Map<String, String>> _getHeaders({bool requireAuth = false}) async {
    Map<String, String> headers = Map.from(AppConfig.defaultHeaders);
    
    if (requireAuth) {
      final token = await StorageHelper.getToken();
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }
    
    return headers;
  }

  // دالة عامة لإرسال طلبات GET
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    bool requireAuth = false,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('${AppConfig.apiBaseUrl}$endpoint');
      final finalUri = queryParams != null 
          ? uri.replace(queryParameters: queryParams.map((k, v) => MapEntry(k, v.toString())))
          : uri;

      final headers = await _getHeaders(requireAuth: requireAuth);
      
      final response = await http.get(finalUri, headers: headers)
          .timeout(AppConfig.connectionTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return ApiResponse.error('خطأ في الاتصال: ${e.toString()}');
    }
  }

  // دالة عامة لإرسال طلبات POST
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    bool requireAuth = false,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('${AppConfig.apiBaseUrl}$endpoint');
      final headers = await _getHeaders(requireAuth: requireAuth);
      
      final response = await http.post(
        uri,
        headers: headers,
        body: data != null ? jsonEncode(data) : null,
      ).timeout(AppConfig.connectionTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return ApiResponse.error('خطأ في الاتصال: ${e.toString()}');
    }
  }

  // دالة عامة لإرسال طلبات PUT
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    bool requireAuth = false,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('${AppConfig.apiBaseUrl}$endpoint');
      final headers = await _getHeaders(requireAuth: requireAuth);
      
      final response = await http.put(
        uri,
        headers: headers,
        body: data != null ? jsonEncode(data) : null,
      ).timeout(AppConfig.connectionTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return ApiResponse.error('خطأ في الاتصال: ${e.toString()}');
    }
  }

  // دالة عامة لإرسال طلبات DELETE
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    bool requireAuth = false,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('${AppConfig.apiBaseUrl}$endpoint');
      final headers = await _getHeaders(requireAuth: requireAuth);
      
      final response = await http.delete(uri, headers: headers)
          .timeout(AppConfig.connectionTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return ApiResponse.error('خطأ في الاتصال: ${e.toString()}');
    }
  }

  // معالجة الاستجابة
  ApiResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(dynamic)? fromJson,
  ) {
    try {
      final data = jsonDecode(response.body);
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (fromJson != null && data['data'] != null) {
          return ApiResponse.success(fromJson(data['data']));
        }
        return ApiResponse.success(data as T);
      } else {
        final message = data['message'] ?? 'حدث خطأ غير متوقع';
        return ApiResponse.error(message);
      }
    } catch (e) {
      return ApiResponse.error('خطأ في تحليل البيانات: ${e.toString()}');
    }
  }

  // === APIs التجارة الإلكترونية ===

  // المنتجات
  Future<ApiResponse<Map<String, dynamic>>> getProducts({
    int page = 1,
    int perPage = 12,
    String? search,
    List<int>? categoryIds,
    List<int>? brandIds,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'per_page': perPage,
    };

    if (search != null && search.isNotEmpty) queryParams['search'] = search;
    if (categoryIds != null && categoryIds.isNotEmpty) {
      queryParams['categories'] = categoryIds;
    }
    if (brandIds != null && brandIds.isNotEmpty) {
      queryParams['brands'] = brandIds;
    }
    if (minPrice != null) queryParams['min_price'] = minPrice;
    if (maxPrice != null) queryParams['max_price'] = maxPrice;
    if (sortBy != null) queryParams['sort_by'] = sortBy;

    return get(AppConfig.productsEndpoint, queryParams: queryParams);
  }

  // تفاصيل منتج
  Future<ApiResponse<Map<String, dynamic>>> getProduct(String slug) async {
    return get('${AppConfig.productsEndpoint}/$slug');
  }

  // المنتجات ذات الصلة
  Future<ApiResponse<Map<String, dynamic>>> getRelatedProducts(String slug) async {
    return get('${AppConfig.productsEndpoint}/$slug/related');
  }

  // الفئات
  Future<ApiResponse<Map<String, dynamic>>> getCategories({
    int? parentId,
    bool includeChildren = false,
  }) async {
    final queryParams = <String, dynamic>{};
    if (parentId != null) queryParams['parent_id'] = parentId;
    if (includeChildren) queryParams['include_children'] = true;

    return get(AppConfig.categoriesEndpoint, queryParams: queryParams);
  }

  // تفاصيل فئة
  Future<ApiResponse<Map<String, dynamic>>> getCategory(String slug) async {
    return get('${AppConfig.categoriesEndpoint}/$slug');
  }

  // العلامات التجارية
  Future<ApiResponse<Map<String, dynamic>>> getBrands() async {
    return get(AppConfig.brandsEndpoint);
  }

  // السلة
  Future<ApiResponse<Map<String, dynamic>>> getCart() async {
    return get(AppConfig.cartEndpoint, requireAuth: true);
  }

  Future<ApiResponse<Map<String, dynamic>>> addToCart({
    required int productId,
    required int quantity,
    int? variationId,
  }) async {
    return post(
      AppConfig.cartEndpoint,
      data: {
        'product_id': productId,
        'quantity': quantity,
        if (variationId != null) 'variation_id': variationId,
      },
      requireAuth: true,
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> updateCartItem(
    int itemId,
    int quantity,
  ) async {
    return put(
      '${AppConfig.cartEndpoint}/$itemId',
      data: {'quantity': quantity},
      requireAuth: true,
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> removeFromCart(int itemId) async {
    return delete('${AppConfig.cartEndpoint}/$itemId', requireAuth: true);
  }

  // الطلبات
  Future<ApiResponse<Map<String, dynamic>>> getOrders({
    int page = 1,
    String? status,
  }) async {
    final queryParams = <String, dynamic>{'page': page};
    if (status != null) queryParams['status'] = status;

    return get(AppConfig.ordersEndpoint, queryParams: queryParams, requireAuth: true);
  }

  Future<ApiResponse<Map<String, dynamic>>> getOrder(int orderId) async {
    return get('${AppConfig.ordersEndpoint}/$orderId', requireAuth: true);
  }

  // المراجعات
  Future<ApiResponse<Map<String, dynamic>>> getProductReviews(String productSlug) async {
    return get('${AppConfig.productsEndpoint}/$productSlug/reviews');
  }

  Future<ApiResponse<Map<String, dynamic>>> addReview({
    required int productId,
    required int rating,
    required String comment,
  }) async {
    return post(
      AppConfig.reviewsEndpoint,
      data: {
        'product_id': productId,
        'rating': rating,
        'comment': comment,
      },
      requireAuth: true,
    );
  }

  // المفضلة
  Future<ApiResponse<Map<String, dynamic>>> getWishlist() async {
    return get(AppConfig.wishlistEndpoint, requireAuth: true);
  }

  Future<ApiResponse<Map<String, dynamic>>> addToWishlist(int productId) async {
    return post(
      AppConfig.wishlistEndpoint,
      data: {'product_id': productId},
      requireAuth: true,
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> removeFromWishlist(int productId) async {
    return delete('${AppConfig.wishlistEndpoint}/$productId', requireAuth: true);
  }

  // === Vehicle Parts Finder ===

  // الفئات الجذرية للمركبات
  Future<ApiResponse<Map<String, dynamic>>> getVehicleRootCategories() async {
    return get(AppConfig.vehicleCategoriesEndpoint);
  }

  // الماركات
  Future<ApiResponse<Map<String, dynamic>>> getVehicleMakes() async {
    return get(AppConfig.vehicleMakesEndpoint);
  }

  // الموديلات
  Future<ApiResponse<Map<String, dynamic>>> getVehicleModels(int makeId) async {
    return get('${AppConfig.vehicleMakesEndpoint}/$makeId/models');
  }

  // السنوات
  Future<ApiResponse<Map<String, dynamic>>> getVehicleYears(int modelId) async {
    return get('${AppConfig.vehicleModelsEndpoint}/$modelId/years');
  }

  // البحث عن قطع الغيار
  Future<ApiResponse<Map<String, dynamic>>> searchVehicleParts({
    List<int>? categoryIds,
    String? keyword,
    int page = 1,
    int perPage = 12,
  }) async {
    return post(
      AppConfig.vehicleSearchEndpoint,
      data: {
        if (categoryIds != null) 'category_ids': categoryIds,
        if (keyword != null) 'keyword': keyword,
        'page': page,
        'per_page': perPage,
      },
    );
  }

  // السيارات المحفوظة
  Future<ApiResponse<Map<String, dynamic>>> getMyVehicles() async {
    return get(AppConfig.myVehiclesEndpoint, requireAuth: true);
  }

  Future<ApiResponse<Map<String, dynamic>>> addVehicle({
    required int categoryId,
    required int makeId,
    required int modelId,
    required int year,
    String? nickname,
  }) async {
    return post(
      AppConfig.myVehiclesEndpoint,
      data: {
        'category_id': categoryId,
        'make_id': makeId,
        'model_id': modelId,
        'year': year,
        if (nickname != null) 'nickname': nickname,
      },
      requireAuth: true,
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> removeVehicle(int vehicleId) async {
    return delete('${AppConfig.myVehiclesEndpoint}/$vehicleId', requireAuth: true);
  }

  // الحصول على قطع الغيار لفئة معينة
  Future<ApiResponse<Map<String, dynamic>>> getVehiclePartsByCategory(int categoryId, {int page = 1, int perPage = 12}) async {
    return get(
      '${AppConfig.vehiclePartsEndpoint2}/$categoryId/parts',
      queryParams: {
        'page': page.toString(),
        'per_page': perPage.toString(),
      },
    );
  }

  // الحصول على المنتجات الشائعة لفئة معينة
  Future<ApiResponse<Map<String, dynamic>>> getPopularPartsByCategory(int categoryId, {int limit = 10}) async {
    return get(
      '${AppConfig.popularPartsEndpoint}/$categoryId/popular-parts',
      queryParams: {
        'limit': limit.toString(),
      },
    );
  }

  // البحث السريع
  Future<ApiResponse<Map<String, dynamic>>> quickSearchVehicleParts(String keyword, {int limit = 10}) async {
    return get(
      AppConfig.quickSearchEndpoint,
      queryParams: {
        'keyword': keyword,
        'limit': limit.toString(),
      },
    );
  }

  // === المصادقة والمستخدمين ===

  // تسجيل الدخول
  Future<ApiResponse<Map<String, dynamic>>> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    return post(
      AppConfig.loginEndpoint,
      data: {
        'email': email,
        'password': password,
        'remember_me': rememberMe,
      },
    );
  }

  // تسجيل حساب جديد
  Future<ApiResponse<Map<String, dynamic>>> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    String? phone,
    String accountType = 'customer',
    Map<String, dynamic>? additionalData,
  }) async {
    final data = <String, dynamic>{
      'name': name,
      'email': email,
      'password': password,
      'password_confirmation': passwordConfirmation,
      'account_type': accountType,
    };

    if (phone != null) data['phone'] = phone;
    if (additionalData != null) {
      additionalData.forEach((key, value) {
        data[key] = value;
      });
    }

    return post(AppConfig.registerEndpoint, data: data);
  }

  // تسجيل الخروج
  Future<ApiResponse<Map<String, dynamic>>> logout() async {
    return post(AppConfig.logoutEndpoint, requireAuth: true);
  }

  // الحصول على الملف الشخصي
  Future<ApiResponse<Map<String, dynamic>>> getProfile() async {
    return get(AppConfig.profileEndpoint, requireAuth: true);
  }

  // تحديث الملف الشخصي
  Future<ApiResponse<Map<String, dynamic>>> updateProfile(Map<String, dynamic> data) async {
    return put(AppConfig.profileEndpoint, data: data, requireAuth: true);
  }

  // إرسال OTP
  Future<ApiResponse<Map<String, dynamic>>> sendOtp({
    required String phone,
    String type = 'verification',
  }) async {
    return post(
      AppConfig.otpSendEndpoint,
      data: {
        'phone': phone,
        'type': type,
      },
    );
  }

  // التحقق من OTP
  Future<ApiResponse<Map<String, dynamic>>> verifyOtp({
    required String phone,
    required String code,
    String type = 'verification',
  }) async {
    return post(
      AppConfig.otpVerifyEndpoint,
      data: {
        'phone': phone,
        'code': code,
        'type': type,
      },
    );
  }

  // === نظام التسعير للجملة ===

  // طلب تحويل لعميل جملة
  Future<ApiResponse<Map<String, dynamic>>> applyForWholesale({
    required String companyName,
    required String businessType,
    required String taxNumber,
    String? businessAddress,
    String? businessPhone,
    String? businessEmail,
    String? notes,
  }) async {
    return post(
      AppConfig.wholesaleApplicationEndpoint,
      data: {
        'company_name': companyName,
        'business_type': businessType,
        'tax_number': taxNumber,
        if (businessAddress != null) 'business_address': businessAddress,
        if (businessPhone != null) 'business_phone': businessPhone,
        if (businessEmail != null) 'business_email': businessEmail,
        if (notes != null) 'notes': notes,
      },
      requireAuth: true,
    );
  }

  // الحصول على حالة طلب التحويل
  Future<ApiResponse<Map<String, dynamic>>> getWholesaleApplicationStatus() async {
    return get('${AppConfig.wholesaleEndpoint}/application-status', requireAuth: true);
  }

  // الحصول على أسعار الجملة
  Future<ApiResponse<Map<String, dynamic>>> getWholesalePrices({
    List<int>? productIds,
  }) async {
    final queryParams = <String, dynamic>{};
    if (productIds != null && productIds.isNotEmpty) {
      queryParams['product_ids'] = productIds;
    }

    return get(
      AppConfig.wholesalePricesEndpoint,
      queryParams: queryParams,
      requireAuth: true,
    );
  }

  // إحصائيات عميل الجملة
  Future<ApiResponse<Map<String, dynamic>>> getWholesaleStats() async {
    return get(AppConfig.wholesaleStatsEndpoint, requireAuth: true);
  }

  // === المحتوى والوسائط ===

  // مقالات المدونة
  Future<ApiResponse<Map<String, dynamic>>> getBlogPosts({
    int page = 1,
    int perPage = 10,
    String? search,
    int? categoryId,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'per_page': perPage,
    };

    if (search != null && search.isNotEmpty) queryParams['search'] = search;
    if (categoryId != null) queryParams['category_id'] = categoryId;

    return get(AppConfig.blogEndpoint, queryParams: queryParams);
  }

  // تفاصيل مقال
  Future<ApiResponse<Map<String, dynamic>>> getBlogPost(String slug) async {
    return get('${AppConfig.blogEndpoint}/$slug');
  }

  // السلايدرات
  Future<ApiResponse<Map<String, dynamic>>> getSliders() async {
    return get(AppConfig.slidersEndpoint);
  }

  // الإعلانات
  Future<ApiResponse<Map<String, dynamic>>> getAds() async {
    return get(AppConfig.adsEndpoint);
  }

  // المعارض
  Future<ApiResponse<Map<String, dynamic>>> getGalleries() async {
    return get(AppConfig.galleriesEndpoint);
  }

  // صور معرض محدد
  Future<ApiResponse<Map<String, dynamic>>> getGalleryImages(int galleryId) async {
    return get('${AppConfig.galleriesEndpoint}/$galleryId/images');
  }

  // آراء العملاء
  Future<ApiResponse<Map<String, dynamic>>> getTestimonials() async {
    return get(AppConfig.testimonialsEndpoint);
  }

  // إضافة رأي عميل
  Future<ApiResponse<Map<String, dynamic>>> addTestimonial({
    required String name,
    required String message,
    int rating = 5,
    String? company,
  }) async {
    return post(
      AppConfig.testimonialsEndpoint,
      data: {
        'name': name,
        'message': message,
        'rating': rating,
        if (company != null) 'company': company,
      },
    );
  }

  // === الدعم والتواصل ===

  // إرسال رسالة اتصال
  Future<ApiResponse<Map<String, dynamic>>> sendContactMessage({
    required String name,
    required String email,
    required String subject,
    required String message,
    String? phone,
  }) async {
    return post(
      AppConfig.contactEndpoint,
      data: {
        'name': name,
        'email': email,
        'subject': subject,
        'message': message,
        if (phone != null) 'phone': phone,
      },
    );
  }

  // الأسئلة الشائعة
  Future<ApiResponse<Map<String, dynamic>>> getFAQs({
    String? search,
    int? categoryId,
  }) async {
    final queryParams = <String, dynamic>{};
    if (search != null && search.isNotEmpty) queryParams['search'] = search;
    if (categoryId != null) queryParams['category_id'] = categoryId;

    return get(AppConfig.faqEndpoint, queryParams: queryParams);
  }

  // الاشتراك في النشرة الإخبارية
  Future<ApiResponse<Map<String, dynamic>>> subscribeNewsletter(String email) async {
    return post(
      '${AppConfig.newsletterEndpoint}/subscribe',
      data: {'email': email},
    );
  }

  // إلغاء الاشتراك في النشرة الإخبارية
  Future<ApiResponse<Map<String, dynamic>>> unsubscribeNewsletter(String email) async {
    return post(
      '${AppConfig.newsletterEndpoint}/unsubscribe',
      data: {'email': email},
    );
  }

  // === العروض والخصومات ===

  // العروض السريعة
  Future<ApiResponse<Map<String, dynamic>>> getFlashSales() async {
    return get(AppConfig.flashSalesEndpoint);
  }

  // تطبيق كوبون خصم
  Future<ApiResponse<Map<String, dynamic>>> applyCoupon(String code) async {
    return post(
      '${AppConfig.couponsEndpoint}/apply',
      data: {'code': code},
      requireAuth: true,
    );
  }

  // إزالة كوبون خصم
  Future<ApiResponse<Map<String, dynamic>>> removeCoupon() async {
    return post('${AppConfig.couponsEndpoint}/remove', requireAuth: true);
  }

  // === العناوين ===

  // الحصول على عناوين العميل
  Future<ApiResponse<Map<String, dynamic>>> getAddresses() async {
    return get(AppConfig.addressesEndpoint, requireAuth: true);
  }

  // إضافة عنوان جديد
  Future<ApiResponse<Map<String, dynamic>>> addAddress({
    required String name,
    required String address,
    required String city,
    required String state,
    required String country,
    String? postalCode,
    String? phone,
    bool isDefault = false,
  }) async {
    return post(
      AppConfig.addressesEndpoint,
      data: {
        'name': name,
        'address': address,
        'city': city,
        'state': state,
        'country': country,
        if (postalCode != null) 'postal_code': postalCode,
        if (phone != null) 'phone': phone,
        'is_default': isDefault,
      },
      requireAuth: true,
    );
  }

  // تحديث عنوان
  Future<ApiResponse<Map<String, dynamic>>> updateAddress(
    int addressId,
    Map<String, dynamic> data,
  ) async {
    return put('${AppConfig.addressesEndpoint}/$addressId', data: data, requireAuth: true);
  }

  // حذف عنوان
  Future<ApiResponse<Map<String, dynamic>>> deleteAddress(int addressId) async {
    return delete('${AppConfig.addressesEndpoint}/$addressId', requireAuth: true);
  }

  // === العملات والدول ===

  // الحصول على العملات المتاحة
  Future<ApiResponse<Map<String, dynamic>>> getCurrencies() async {
    return get(AppConfig.currenciesEndpoint);
  }

  // الحصول على الدول المتاحة
  Future<ApiResponse<Map<String, dynamic>>> getCountries() async {
    return get(AppConfig.countriesEndpoint);
  }

  // === الفلاتر والبحث ===

  // الحصول على فلاتر المنتجات
  Future<ApiResponse<Map<String, dynamic>>> getProductFilters() async {
    return get(AppConfig.filtersEndpoint);
  }

  // === إدارة الطلبات المتقدمة ===

  // تتبع الطلب
  Future<ApiResponse<Map<String, dynamic>>> trackOrder(String trackingNumber) async {
    return post(
      AppConfig.orderTrackingEndpoint,
      data: {'tracking_number': trackingNumber},
    );
  }

  // إلغاء طلب
  Future<ApiResponse<Map<String, dynamic>>> cancelOrder(int orderId) async {
    return post('${AppConfig.ordersEndpoint}/$orderId/cancel', requireAuth: true);
  }

  // تأكيد استلام الطلب
  Future<ApiResponse<Map<String, dynamic>>> confirmOrderDelivery(int orderId) async {
    return post('${AppConfig.ordersEndpoint}/$orderId/confirm-delivery', requireAuth: true);
  }

  // === طلبات الإرجاع ===

  // الحصول على طلبات الإرجاع
  Future<ApiResponse<Map<String, dynamic>>> getOrderReturns() async {
    return get(AppConfig.orderReturnsEndpoint, requireAuth: true);
  }

  // إنشاء طلب إرجاع
  Future<ApiResponse<Map<String, dynamic>>> createOrderReturn({
    required int orderId,
    required String reason,
    String? notes,
    List<Map<String, dynamic>>? items,
  }) async {
    return post(
      AppConfig.orderReturnsEndpoint,
      data: {
        'order_id': orderId,
        'reason': reason,
        if (notes != null) 'notes': notes,
        if (items != null) 'items': items,
      },
      requireAuth: true,
    );
  }
}
