import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/notification_provider.dart';
import '../widgets/custom_button.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, NotificationProvider>(
      builder: (context, themeProvider, notificationProvider, child) {
        final isDark = themeProvider.isDarkMode;
        
        return Scaffold(
          backgroundColor: isDark ? Colors.grey[900] : Colors.grey[50],
          appBar: AppBar(
            title: const Text('الإعدادات'),
            backgroundColor: isDark ? Colors.grey[850] : Colors.white,
            foregroundColor: isDark ? Colors.white : Colors.black,
          ),
          body: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // قسم المظهر
              _buildSectionCard(
                'المظهر والثيم',
                Icons.palette,
                [
                  _buildThemeToggle(themeProvider, isDark),
                  _buildColorPicker(themeProvider, isDark),
                ],
                isDark,
              ),
              
              const SizedBox(height: 16),
              
              // قسم الإشعارات
              _buildSectionCard(
                'الإشعارات',
                Icons.notifications,
                [
                  _buildNotificationSetting(
                    'المنتجات الجديدة',
                    'تلقي إشعارات عند إضافة منتجات جديدة',
                    notificationProvider.settings['newProducts'] ?? true,
                    (value) => notificationProvider.updateSetting('newProducts', value),
                    isDark,
                  ),
                  _buildNotificationSetting(
                    'العروض الخاصة',
                    'تلقي إشعارات العروض والخصومات',
                    notificationProvider.settings['offers'] ?? true,
                    (value) => notificationProvider.updateSetting('offers', value),
                    isDark,
                  ),
                  _buildNotificationSetting(
                    'تحديثات الطلبات',
                    'تلقي إشعارات حالة الطلبات',
                    notificationProvider.settings['orderUpdates'] ?? true,
                    (value) => notificationProvider.updateSetting('orderUpdates', value),
                    isDark,
                  ),
                  _buildNotificationSetting(
                    'إشعارات عامة',
                    'تلقي الإشعارات العامة من التطبيق',
                    notificationProvider.settings['general'] ?? true,
                    (value) => notificationProvider.updateSetting('general', value),
                    isDark,
                  ),
                ],
                isDark,
              ),
              
              const SizedBox(height: 16),
              
              // قسم التطبيق
              _buildSectionCard(
                'التطبيق',
                Icons.app_settings_alt,
                [
                  _buildSettingItem(
                    'حول التطبيق',
                    'معلومات عن التطبيق والإصدار',
                    Icons.info,
                    () => _showAboutDialog(),
                    isDark,
                  ),
                  _buildSettingItem(
                    'تقييم التطبيق',
                    'قيم التطبيق في المتجر',
                    Icons.star,
                    () => _rateApp(),
                    isDark,
                  ),
                  _buildSettingItem(
                    'مشاركة التطبيق',
                    'شارك التطبيق مع الأصدقاء',
                    Icons.share,
                    () => _shareApp(),
                    isDark,
                  ),
                ],
                isDark,
              ),
              
              const SizedBox(height: 16),
              
              // قسم الدعم
              _buildSectionCard(
                'الدعم والمساعدة',
                Icons.help,
                [
                  _buildSettingItem(
                    'الأسئلة الشائعة',
                    'إجابات على الأسئلة المتكررة',
                    Icons.quiz,
                    () => _showFAQ(),
                    isDark,
                  ),
                  _buildSettingItem(
                    'تواصل معنا',
                    'راسلنا للحصول على المساعدة',
                    Icons.contact_support,
                    () => _contactSupport(),
                    isDark,
                  ),
                  _buildSettingItem(
                    'سياسة الخصوصية',
                    'اطلع على سياسة الخصوصية',
                    Icons.privacy_tip,
                    () => _showPrivacyPolicy(),
                    isDark,
                  ),
                ],
                isDark,
              ),
              
              const SizedBox(height: 32),
              
              // أزرار الاختبار (للتطوير فقط)
              if (true) // يمكن إزالة هذا في الإنتاج
                _buildTestSection(notificationProvider, isDark),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionCard(String title, IconData icon, List<Widget> children, bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: Theme.of(context).primaryColor),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildThemeToggle(ThemeProvider themeProvider, bool isDark) {
    return ListTile(
      leading: Icon(
        isDark ? Icons.dark_mode : Icons.light_mode,
        color: Theme.of(context).primaryColor,
      ),
      title: Text(
        'الوضع المظلم',
        style: TextStyle(
          color: isDark ? Colors.white : Colors.black,
        ),
      ),
      subtitle: Text(
        isDark ? 'مفعل' : 'غير مفعل',
        style: TextStyle(
          color: isDark ? Colors.grey[400] : Colors.grey[600],
        ),
      ),
      trailing: Switch(
        value: isDark,
        onChanged: (value) => themeProvider.setDarkMode(value),
        activeColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildColorPicker(ThemeProvider themeProvider, bool isDark) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اللون الأساسي',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: ThemeProvider.availableColors.map((color) {
              final isSelected = color.value == themeProvider.primaryColor.value;
              return GestureDetector(
                onTap: () => themeProvider.setPrimaryColor(color),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: isSelected
                        ? Border.all(color: Colors.white, width: 3)
                        : null,
                    boxShadow: [
                      BoxShadow(
                        color: color.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 20)
                      : null,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSetting(
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
    bool isDark,
  ) {
    return ListTile(
      leading: Icon(
        Icons.notifications,
        color: Theme.of(context).primaryColor,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDark ? Colors.white : Colors.black,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: isDark ? Colors.grey[400] : Colors.grey[600],
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildSettingItem(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
    bool isDark,
  ) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).primaryColor),
      title: Text(
        title,
        style: TextStyle(
          color: isDark ? Colors.white : Colors.black,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: isDark ? Colors.grey[400] : Colors.grey[600],
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: isDark ? Colors.grey[400] : Colors.grey[600],
      ),
      onTap: onTap,
    );
  }

  Widget _buildTestSection(NotificationProvider notificationProvider, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.bug_report, color: Colors.orange),
              const SizedBox(width: 8),
              Text(
                'أدوات التطوير',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: SecondaryButton(
                  text: 'إشعارات تجريبية',
                  onPressed: () => notificationProvider.createSampleNotifications(),
                  icon: Icons.notifications_active,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SecondaryButton(
                  text: 'مسح الإشعارات',
                  onPressed: () => notificationProvider.clearAllNotifications(),
                  icon: Icons.clear_all,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'دليل - Dalil',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.car_repair, size: 48),
      children: [
        const Text('تطبيق دليل لقطع غيار السيارات'),
        const Text('تطبيق شامل لجميع احتياجاتك من قطع الغيار'),
      ],
    );
  }

  void _rateApp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح متجر التطبيقات قريباً...')),
    );
  }

  void _shareApp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة التطبيق قريباً...')),
    );
  }

  void _showFAQ() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('الأسئلة الشائعة قريباً...')),
    );
  }

  void _contactSupport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('صفحة التواصل قريباً...')),
    );
  }

  void _showPrivacyPolicy() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سياسة الخصوصية قريباً...')),
    );
  }
}
