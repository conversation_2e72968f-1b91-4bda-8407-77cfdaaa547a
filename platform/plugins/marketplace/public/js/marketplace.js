!function(e){"use strict";e((function(){e(".ps-tab-list  li > a ").on("click",(function(i){i.preventDefault();var s=e(this).attr("href");e(this).closest("li").siblings("li").removeClass("active"),e(this).closest("li").addClass("active"),e(s).addClass("active"),e(s).siblings(".ps-tab").removeClass("active")})),e(".ps-drawer-toggle").on("click",(function(){e(".ps-drawer--mobile").addClass("active"),e(".ps-site-overlay").addClass("active")})),e(".ps-drawer__close").on("click",(function(){e(".ps-drawer--mobile").removeClass("active"),e(".ps-site-overlay").removeClass("active")})),e("body").on("click",(function(i){e(i.target).siblings(".ps-drawer--mobile").hasClass("active")&&(e(".ps-drawer--mobile").removeClass("active"),e(".ps-site-overlay").removeClass("active"))})),e(".custom-select-image").on("click",(function(i){i.preventDefault(),e(this).closest(".image-box").find(".image_input").trigger("click")})),e(".image_input").on("change",(function(){!function(i){if(i.files&&i.files[0]){var s=new FileReader;s.onload=function(s){e(i).closest(".image-box").find(".preview_image").prop("src",s.target.result)},s.readAsDataURL(i.files[0])}}(this)})),e(document).on("click",".btn_remove_image",(function(i){i.preventDefault();var s=e(i.currentTarget).closest(".image-box").find(".preview-image-wrapper .preview_image");s.attr("src",s.data("default-image")),e(i.currentTarget).closest(".image-box").find(".image-data").val("")})),window.noticeMessages&&window.noticeMessages.length&&noticeMessages.map((function(e){Botble.showNotice(e.type,e.message,"")}))}))}(jQuery);