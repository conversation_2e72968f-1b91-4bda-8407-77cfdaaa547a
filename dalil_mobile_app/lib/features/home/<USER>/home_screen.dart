import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:easy_localization/easy_localization.dart';
import '../providers/home_provider.dart';
import '../widgets/vehicle_parts_finder_card.dart';
import '../widgets/quick_actions_grid.dart';
import '../widgets/featured_products_section.dart';
import '../widgets/categories_section.dart';
import '../widgets/promotional_banners.dart';
import '../widgets/recent_searches_section.dart';
import '../../shared/widgets/custom_app_bar.dart';
import '../../shared/widgets/loading_widget.dart';
import '../../shared/widgets/error_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScrollController _scrollController = ScrollController();
  int _currentBottomNavIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeProvider>().loadHomeData();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<HomeProvider>(
        builder: (context, homeProvider, child) {
          if (homeProvider.isLoading && homeProvider.banners.isEmpty) {
            return const LoadingWidget();
          }

          if (homeProvider.hasError && homeProvider.banners.isEmpty) {
            return CustomErrorWidget(
              message: homeProvider.errorMessage,
              onRetry: () => homeProvider.loadHomeData(),
            );
          }

          return RefreshIndicator(
            onRefresh: () => homeProvider.loadHomeData(),
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                // شريط التطبيق المخصص
                SliverAppBar(
                  expandedHeight: 120,
                  floating: true,
                  pinned: true,
                  backgroundColor: Theme.of(context).primaryColor,
                  flexibleSpace: FlexibleSpaceBar(
                    title: Row(
                      children: [
                        Image.asset(
                          'assets/logos/dalil_logo.png',
                          height: 32,
                          width: 32,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'app_name'.tr(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Theme.of(context).primaryColor,
                            Theme.of(context).primaryColor.withOpacity(0.8),
                          ],
                        ),
                      ),
                    ),
                  ),
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.search, color: Colors.white),
                      onPressed: () => context.push('/search'),
                    ),
                    IconButton(
                      icon: const Icon(Icons.shopping_cart, color: Colors.white),
                      onPressed: () => context.push('/cart'),
                    ),
                    IconButton(
                      icon: const Icon(Icons.notifications, color: Colors.white),
                      onPressed: () {
                        // عرض الإشعارات
                      },
                    ),
                  ],
                ),

                // المحتوى الرئيسي
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // البانرات الترويجية
                      if (homeProvider.banners.isNotEmpty)
                        PromotionalBanners(banners: homeProvider.banners),

                      const SizedBox(height: 16),

                      // بطاقة Vehicle Parts Finder - الميزة الرئيسية
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🚗 ${'home.vehicle_parts_finder'.tr()}',
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'home.vehicle_parts_finder_desc'.tr(),
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 12),
                            const VehiclePartsFinderCard(),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // الإجراءات السريعة
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: QuickActionsGrid(),
                      ),

                      const SizedBox(height: 24),

                      // عمليات البحث الأخيرة
                      const RecentSearchesSection(),

                      const SizedBox(height: 24),

                      // الفئات الرئيسية
                      CategoriesSection(categories: homeProvider.categories),

                      const SizedBox(height: 24),

                      // المنتجات المميزة
                      FeaturedProductsSection(products: homeProvider.featuredProducts),

                      const SizedBox(height: 24),

                      // منتجات مقترحة بناءً على التاريخ
                      if (homeProvider.recommendedProducts.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            'مقترح لك',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        SizedBox(
                          height: 200,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemCount: homeProvider.recommendedProducts.length,
                            itemBuilder: (context, index) {
                              final product = homeProvider.recommendedProducts[index];
                              return Container(
                                width: 160,
                                margin: const EdgeInsets.only(left: 12),
                                child: Card(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: Container(
                                          decoration: BoxDecoration(
                                            borderRadius: const BorderRadius.vertical(
                                              top: Radius.circular(12),
                                            ),
                                            image: DecorationImage(
                                              image: NetworkImage(product.imageUrl),
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(8),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              product.name,
                                              style: Theme.of(context).textTheme.bodySmall,
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              '${product.price} د.ع',
                                              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                                color: Theme.of(context).primaryColor,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],

                      // مساحة إضافية في الأسفل
                      const SizedBox(height: 100),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),

      // شريط التنقل السفلي
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentBottomNavIndex,
        onTap: (index) {
          setState(() {
            _currentBottomNavIndex = index;
          });
          
          switch (index) {
            case 0:
              // الرئيسية - البقاء في نفس الصفحة
              break;
            case 1:
              context.push('/vehicle-parts-finder');
              break;
            case 2:
              context.push('/search');
              break;
            case 3:
              context.push('/cart');
              break;
            case 4:
              context.push('/profile');
              break;
          }
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.car_repair),
            label: 'قطع الغيار',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'البحث',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: 'السلة',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'الملف الشخصي',
          ),
        ],
      ),

      // زر عائم للوصول السريع لـ Vehicle Parts Finder
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.push('/vehicle-parts-finder'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.car_repair),
        label: const Text('باحث قطع الغيار'),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}
