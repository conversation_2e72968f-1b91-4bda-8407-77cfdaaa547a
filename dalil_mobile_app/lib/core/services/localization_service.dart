import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'storage_service.dart';

class LocalizationService {
  static const String _languageKey = 'selected_language';
  
  // اللغات المدعومة
  static const List<LocaleModel> supportedLocales = [
    LocaleModel(
      locale: Locale('ar', 'EG'),
      name: 'العربية',
      nativeName: 'العربية',
      flag: '🇪🇬',
      isRTL: true,
    ),
    LocaleModel(
      locale: Locale('en', 'US'),
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
      isRTL: false,
    ),
    LocaleModel(
      locale: Locale('ku', 'IQ'),
      name: 'Kurdish',
      nativeName: 'کوردی',
      flag: '🇮🇶',
      isRTL: true,
    ),
  ];

  // الحصول على اللغة الحالية
  static LocaleModel getCurrentLocale(BuildContext context) {
    final currentLocale = context.locale;
    return supportedLocales.firstWhere(
      (locale) => locale.locale == currentLocale,
      orElse: () => supportedLocales.first,
    );
  }

  // تغيير اللغة
  static Future<void> changeLanguage(BuildContext context, LocaleModel localeModel) async {
    await context.setLocale(localeModel.locale);
    await StorageService.saveSetting(_languageKey, localeModel.locale.languageCode);
  }

  // الحصول على اللغة المحفوظة
  static Future<Locale?> getSavedLanguage() async {
    final languageCode = await StorageService.getSetting<String>(_languageKey);
    if (languageCode != null) {
      return supportedLocales
          .firstWhere(
            (locale) => locale.locale.languageCode == languageCode,
            orElse: () => supportedLocales.first,
          )
          .locale;
    }
    return null;
  }

  // التحقق من كون اللغة RTL
  static bool isRTL(BuildContext context) {
    final currentLocale = getCurrentLocale(context);
    return currentLocale.isRTL;
  }

  // الحصول على اتجاه النص
  static TextDirection getTextDirection(BuildContext context) {
    return isRTL(context) ? TextDirection.rtl : TextDirection.ltr;
  }

  // الحصول على محاذاة النص
  static TextAlign getTextAlign(BuildContext context) {
    return isRTL(context) ? TextAlign.right : TextAlign.left;
  }

  // الحصول على محاذاة النص المعاكسة
  static TextAlign getOppositeTextAlign(BuildContext context) {
    return isRTL(context) ? TextAlign.left : TextAlign.right;
  }

  // الحصول على EdgeInsets مع مراعاة الاتجاه
  static EdgeInsets getDirectionalPadding({
    double start = 0,
    double top = 0,
    double end = 0,
    double bottom = 0,
    required BuildContext context,
  }) {
    if (isRTL(context)) {
      return EdgeInsets.only(
        right: start,
        top: top,
        left: end,
        bottom: bottom,
      );
    } else {
      return EdgeInsets.only(
        left: start,
        top: top,
        right: end,
        bottom: bottom,
      );
    }
  }

  // الحصول على BorderRadius مع مراعاة الاتجاه
  static BorderRadius getDirectionalBorderRadius({
    double topStart = 0,
    double topEnd = 0,
    double bottomStart = 0,
    double bottomEnd = 0,
    required BuildContext context,
  }) {
    if (isRTL(context)) {
      return BorderRadius.only(
        topRight: Radius.circular(topStart),
        topLeft: Radius.circular(topEnd),
        bottomRight: Radius.circular(bottomStart),
        bottomLeft: Radius.circular(bottomEnd),
      );
    } else {
      return BorderRadius.only(
        topLeft: Radius.circular(topStart),
        topRight: Radius.circular(topEnd),
        bottomLeft: Radius.circular(bottomStart),
        bottomRight: Radius.circular(bottomEnd),
      );
    }
  }

  // تنسيق الأرقام حسب اللغة
  static String formatNumber(BuildContext context, num number) {
    final locale = getCurrentLocale(context).locale;
    final formatter = NumberFormat.decimalPattern(locale.toString());
    return formatter.format(number);
  }

  // تنسيق العملة
  static String formatCurrency(BuildContext context, double amount) {
    final currentLocale = getCurrentLocale(context);
    
    switch (currentLocale.locale.languageCode) {
      case 'ar':
      case 'ku':
        return '${formatNumber(context, amount)} د.ع';
      case 'en':
        return 'IQD ${formatNumber(context, amount)}';
      default:
        return '${formatNumber(context, amount)} د.ع';
    }
  }

  // تنسيق التاريخ
  static String formatDate(BuildContext context, DateTime date) {
    final locale = getCurrentLocale(context).locale;
    final formatter = DateFormat.yMMMd(locale.toString());
    return formatter.format(date);
  }

  // تنسيق الوقت
  static String formatTime(BuildContext context, DateTime time) {
    final locale = getCurrentLocale(context).locale;
    final formatter = DateFormat.Hm(locale.toString());
    return formatter.format(time);
  }

  // تنسيق التاريخ والوقت
  static String formatDateTime(BuildContext context, DateTime dateTime) {
    final locale = getCurrentLocale(context).locale;
    final formatter = DateFormat.yMMMd(locale.toString()).add_Hm();
    return formatter.format(dateTime);
  }

  // الحصول على نص مترجم مع معاملات
  static String tr(String key, {Map<String, dynamic>? namedArgs, List<dynamic>? args}) {
    return key.tr(namedArgs: namedArgs, args: args);
  }

  // الحصول على نص مترجم للجمع
  static String plural(String key, num count, {Map<String, dynamic>? namedArgs}) {
    return key.plural(count, namedArgs: namedArgs);
  }

  // التحقق من دعم اللغة
  static bool isLanguageSupported(String languageCode) {
    return supportedLocales.any((locale) => locale.locale.languageCode == languageCode);
  }

  // الحصول على معلومات اللغة بالكود
  static LocaleModel? getLocaleByCode(String languageCode) {
    try {
      return supportedLocales.firstWhere(
        (locale) => locale.locale.languageCode == languageCode,
      );
    } catch (e) {
      return null;
    }
  }
}

// نموذج اللغة
class LocaleModel {
  final Locale locale;
  final String name;
  final String nativeName;
  final String flag;
  final bool isRTL;

  const LocaleModel({
    required this.locale,
    required this.name,
    required this.nativeName,
    required this.flag,
    required this.isRTL,
  });

  @override
  String toString() {
    return 'LocaleModel(locale: $locale, name: $name, nativeName: $nativeName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocaleModel && other.locale == locale;
  }

  @override
  int get hashCode => locale.hashCode;
}
