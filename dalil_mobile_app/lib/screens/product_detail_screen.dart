import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../config/app_config.dart';
import '../services/product_service.dart';
import '../services/cart_service.dart';
import '../services/auth_service.dart';
import '../providers/theme_provider.dart';
import '../providers/cart_provider.dart';

class ProductDetailScreen extends StatefulWidget {
  final Map<String, dynamic> product;

  const ProductDetailScreen({
    super.key,
    required this.product,
  });

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends State<ProductDetailScreen> {
  int _quantity = 1;
  int _selectedImageIndex = 0;
  bool _isLoading = false;
  bool _isWholesaleCustomer = false;
  Map<String, dynamic>? _productDetails;

  @override
  void initState() {
    super.initState();
    _loadProductDetails();
    _checkUserType();
  }

  Future<void> _loadProductDetails() async {
    // في التطبيق الحقيقي، يمكن جلب تفاصيل أكثر من API
    setState(() {
      _productDetails = widget.product;
    });
  }

  Future<void> _checkUserType() async {
    final isWholesale = await AuthService.isWholesaleCustomer();
    setState(() {
      _isWholesaleCustomer = isWholesale;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          _buildAppBar(),
          SliverToBoxAdapter(
            child: Column(
              children: [
                _buildImageGallery(),
                _buildProductInfo(),
                _buildPriceSection(),
                _buildQuantitySelector(),
                _buildActionButtons(),
                _buildProductDescription(),
                _buildSpecifications(),
                const SizedBox(height: 100), // مساحة للأزرار السفلية
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomBar(),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 0,
      floating: true,
      pinned: true,
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        widget.product['name'] ?? 'تفاصيل المنتج',
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.favorite_border),
          onPressed: () {
            // إضافة للمفضلة
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة المنتج للمفضلة'),
                backgroundColor: Colors.green,
              ),
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.share),
          onPressed: () {
            // مشاركة المنتج
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم نسخ رابط المنتج'),
                backgroundColor: Colors.blue,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildImageGallery() {
    final images = widget.product['images'] as List<dynamic>? ?? [widget.product['image']];
    final validImages = images.where((img) => img != null).toList();

    return Container(
      height: 300,
      color: Colors.white,
      child: Column(
        children: [
          // الصورة الرئيسية
          Expanded(
            child: PageView.builder(
              itemCount: validImages.length,
              onPageChanged: (index) {
                setState(() {
                  _selectedImageIndex = index;
                });
              },
              itemBuilder: (context, index) {
                final imageUrl = ProductService.getImageUrl(validImages[index]);
                return Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: imageUrl.startsWith('assets/')
                        ? Image.asset(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return _buildPlaceholderImage();
                            },
                          )
                        : CachedNetworkImage(
                            imageUrl: imageUrl,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => _buildLoadingImage(),
                            errorWidget: (context, url, error) => _buildPlaceholderImage(),
                          ),
                  ),
                );
              },
            ),
          ),
          
          // مؤشرات الصور
          if (validImages.length > 1)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: validImages.asMap().entries.map((entry) {
                  return Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _selectedImageIndex == entry.key
                          ? Color(AppConfig.primaryColorValue)
                          : Colors.grey[300],
                    ),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingImage() {
    return Container(
      color: Colors.grey[100],
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      color: Colors.grey[200],
      child: const Icon(
        Icons.car_repair,
        size: 80,
        color: Colors.grey,
      ),
    );
  }

  Widget _buildProductInfo() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // اسم المنتج
          Text(
            widget.product['name'] ?? 'منتج غير محدد',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              height: 1.3,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // معلومات إضافية
          Row(
            children: [
              if (widget.product['sku'] != null) ...[
                Text(
                  'رمز المنتج: ${widget.product['sku']}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
              ],
              
              // حالة المخزون
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: (widget.product['in_stock'] == true || (widget.product['stock_quantity'] ?? 0) > 0)
                      ? Colors.green.withOpacity(0.1)
                      : Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  (widget.product['in_stock'] == true || (widget.product['stock_quantity'] ?? 0) > 0)
                      ? 'متوفر'
                      : 'غير متوفر',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: (widget.product['in_stock'] == true || (widget.product['stock_quantity'] ?? 0) > 0)
                        ? Colors.green
                        : Colors.red,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // التقييم
          Row(
            children: [
              Row(
                children: List.generate(5, (index) {
                  final rating = (widget.product['rating'] ?? 4.5).toDouble();
                  return Icon(
                    index < rating.floor() ? Icons.star : Icons.star_border,
                    color: Colors.amber,
                    size: 18,
                  );
                }),
              ),
              const SizedBox(width: 8),
              Text(
                '${widget.product['rating'] ?? 4.5} (${widget.product['reviews_count'] ?? 0} تقييم)',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSection() {
    final currentPrice = widget.product['price'] ?? widget.product['sale_price'] ?? 0;
    final originalPrice = widget.product['original_price'];
    final hasDiscount = originalPrice != null && originalPrice > currentPrice;

    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // السعر الحالي
              Text(
                '${ProductService.formatPrice(currentPrice)} د.ع',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(AppConfig.primaryColorValue),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // السعر الأصلي (إذا كان هناك خصم)
              if (hasDiscount) ...[
                Text(
                  '${ProductService.formatPrice(originalPrice)} د.ع',
                  style: const TextStyle(
                    fontSize: 16,
                    decoration: TextDecoration.lineThrough,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${ProductService.calculateDiscountPercentage(originalPrice, currentPrice)}% خصم',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
          
          // معلومات أسعار الجملة
          if (_isWholesaleCustomer) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.business,
                        color: Colors.green[700],
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'أسعار الجملة',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'سعر الجملة: ${ProductService.formatPrice(currentPrice * 0.85)} د.ع (خصم 15%)',
                    style: TextStyle(
                      color: Colors.green[700],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'الحد الأدنى للطلب: 10 قطع',
                    style: TextStyle(
                      color: Colors.green[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuantitySelector() {
    final maxQuantity = widget.product['stock_quantity'] ?? 99;
    final isInStock = widget.product['in_stock'] == true || maxQuantity > 0;

    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الكمية',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              // أزرار تحديد الكمية
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: isInStock && _quantity > 1
                          ? () {
                              setState(() {
                                _quantity--;
                              });
                            }
                          : null,
                      icon: const Icon(Icons.remove),
                      iconSize: 20,
                    ),
                    Container(
                      width: 50,
                      alignment: Alignment.center,
                      child: Text(
                        '$_quantity',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: isInStock && _quantity < maxQuantity
                          ? () {
                              setState(() {
                                _quantity++;
                              });
                            }
                          : null,
                      icon: const Icon(Icons.add),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'متوفر: $maxQuantity قطعة',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(top: 8),
      child: Row(
        children: [
          // زر إضافة للمفضلة
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم إضافة المنتج للمفضلة'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              icon: const Icon(Icons.favorite_border),
            ),
          ),
          const SizedBox(width: 12),

          // زر إضافة للسلة
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _isLoading || !(widget.product['in_stock'] == true || (widget.product['stock_quantity'] ?? 0) > 0)
                  ? null
                  : _addToCart,
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.add_shopping_cart),
              label: Text(_isLoading ? 'جاري الإضافة...' : 'إضافة للسلة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(AppConfig.primaryColorValue),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductDescription() {
    final description = widget.product['description'] ?? widget.product['short_description'] ?? '';

    if (description.isEmpty) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'وصف المنتج',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecifications() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'المواصفات',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),

          // العلامة التجارية
          if (widget.product['brand'] != null)
            _buildSpecRow('العلامة التجارية', widget.product['brand']),

          // رمز المنتج
          if (widget.product['sku'] != null)
            _buildSpecRow('رمز المنتج', widget.product['sku']),

          // الفئة
          if (widget.product['category'] != null)
            _buildSpecRow('الفئة', widget.product['category']),

          // الكلمات المفتاحية
          if (widget.product['tags'] != null && (widget.product['tags'] as List).isNotEmpty)
            _buildSpecRow('الكلمات المفتاحية', (widget.product['tags'] as List).join(', ')),
        ],
      ),
    );
  }

  Widget _buildSpecRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    final isInStock = widget.product['in_stock'] == true || (widget.product['stock_quantity'] ?? 0) > 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // السعر
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'السعر الإجمالي',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    '${ProductService.formatPrice((widget.product['price'] ?? 0) * _quantity)} د.ع',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(AppConfig.primaryColorValue),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // زر الشراء الآن
            Expanded(
              child: ElevatedButton(
                onPressed: _isLoading || !isInStock ? null : _buyNow,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(AppConfig.secondaryColorValue),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  isInStock ? 'اشتري الآن' : 'غير متوفر',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _addToCart() async {
    setState(() {
      _isLoading = true;
    });

    final result = await CartService.addToCart(
      productId: widget.product['id'].toString(),
      quantity: _quantity,
    );

    setState(() {
      _isLoading = false;
    });

    if (result.success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result.message ?? 'تم إضافة المنتج للسلة'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result.errorMessage ?? 'خطأ في إضافة المنتج للسلة'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _buyNow() async {
    // إضافة للسلة أولاً
    await _addToCart();

    // ثم الانتقال لصفحة الدفع
    if (mounted) {
      Navigator.pushNamed(context, '/cart');
    }
  }
}
